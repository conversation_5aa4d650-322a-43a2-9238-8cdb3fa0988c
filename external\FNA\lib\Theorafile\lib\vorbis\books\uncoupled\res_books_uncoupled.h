/********************************************************************
 *                                                                  *
 * THIS FILE IS PART OF THE OggVorbis SOFTWARE CODEC SOURCE CODE.   *
 * US<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ON OF THIS LIBRARY SOURCE IS     *
 * GOVERNED BY A BSD-STYLE SOURCE LICENSE INCLUDED WITH THIS SOURCE *
 * IN 'COPYING'. PLEASE READ THESE TERMS BEFORE DISTRIBUTING.       *
 *                                                                  *
 * THE OggVorbis SOURCE CODE IS (C) COPYRIGHT 1994-2007             *
 * by the Xiph.Org Foundation http://www.xiph.org/                  *
 *                                                                  *
 ********************************************************************

 function: static codebooks autogenerated by huff/huffbuld

 ********************************************************************/

#include "codebook.h"

static const long _vq_quantlist__16u0__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u0__p1_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 8, 5, 8, 8, 8,10,10, 8,
        10,11, 5, 8, 8, 8,10,10, 8,10,10, 4, 9, 9, 9,12,
        11, 8,11,11, 8,12,11,10,12,14,10,13,13, 7,11,11,
        10,14,12,11,14,14, 4, 9, 9, 8,11,11, 9,11,12, 7,
        11,11,10,13,14,10,12,14, 8,11,12,10,14,14,10,13,
        12,
};

static const static_codebook _16u0__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u0__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__16u0__p1_0,
        0
};

static const long _vq_quantlist__16u0__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u0__p2_0[] = {
         2, 4, 4, 5, 6, 6, 5, 6, 6, 5, 7, 7, 7, 8, 9, 7,
         8, 9, 5, 7, 7, 7, 9, 8, 7, 9, 7, 4, 7, 7, 7, 9,
         9, 7, 8, 8, 6, 9, 8, 7, 8,11, 9,11,10, 6, 8, 9,
         8,11, 8, 9,10,11, 4, 7, 7, 7, 8, 8, 7, 9, 9, 6,
         9, 8, 9,11,10, 8, 8,11, 6, 8, 9, 9,10,11, 8,11,
         8,
};

static const static_codebook _16u0__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u0__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__16u0__p2_0,
        0
};

static const long _vq_quantlist__16u0__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__16u0__p3_0[] = {
         1, 5, 5, 7, 7, 6, 7, 7, 8, 8, 6, 7, 8, 8, 8, 8,
         9, 9,11,11, 8, 9, 9,11,11, 6, 9, 8,10,10, 8,10,
        10,11,11, 8,10,10,11,11,10,11,10,13,12, 9,11,10,
        13,13, 6, 8, 9,10,10, 8,10,10,11,11, 8,10,10,11,
        11, 9,10,11,13,12,10,10,11,12,12, 8,11,11,14,13,
        10,12,11,15,13, 9,12,11,15,14,12,14,13,16,14,12,
        13,13,17,14, 8,11,11,13,14, 9,11,12,14,15,10,11,
        12,13,15,11,13,13,14,16,12,13,14,14,16, 5, 9, 9,
        11,11, 9,11,11,12,12, 8,11,11,12,12,11,12,12,15,
        14,10,12,12,15,15, 8,11,11,13,12,10,12,12,13,13,
        10,12,12,14,13,12,12,13,14,15,11,13,13,17,16, 7,
        11,11,13,13,10,12,12,14,13,10,12,12,13,14,12,13,
        12,15,14,11,13,13,15,14, 9,12,12,16,15,11,13,13,
        17,16,10,13,13,16,16,13,14,15,15,16,13,15,14,19,
        17, 9,12,12,14,16,11,13,13,15,16,10,13,13,17,16,
        13,14,13,17,15,12,15,15,16,17, 5, 9, 9,11,11, 8,
        11,11,13,12, 9,11,11,12,12,10,12,12,14,15,11,12,
        12,14,14, 7,11,10,13,12,10,12,12,14,13,10,11,12,
        13,13,11,13,13,15,16,12,12,13,15,15, 7,11,11,13,
        13,10,13,13,14,14,10,12,12,13,13,11,13,13,16,15,
        12,13,13,15,14, 9,12,12,15,15,10,13,13,17,16,11,
        12,13,15,15,12,15,14,18,18,13,14,14,16,17, 9,12,
        12,15,16,10,13,13,15,16,11,13,13,15,16,13,15,15,
        17,17,13,15,14,16,15, 7,11,11,15,16,10,13,12,16,
        17,10,12,13,15,17,15,16,16,18,17,13,15,15,17,18,
         8,12,12,16,16,11,13,14,17,18,11,13,13,18,16,15,
        17,16,17,19,14,15,15,17,16, 8,12,12,16,15,11,14,
        13,18,17,11,13,14,18,17,15,16,16,18,17,13,16,16,
        18,18,11,15,14,18,17,13,14,15,18, 0,12,15,15, 0,
        17,17,16,17,17,18,14,16,18,18, 0,11,14,14,17, 0,
        12,15,14,17,19,12,15,14,18, 0,15,18,16, 0,17,14,
        18,16,18, 0, 7,11,11,16,15,10,12,12,18,16,10,13,
        13,16,15,13,15,14,17,17,14,16,16,19,18, 8,12,12,
        16,16,11,13,13,18,16,11,13,14,17,16,14,15,15,19,
        18,15,16,16, 0,19, 8,12,12,16,17,11,13,13,17,17,
        11,14,13,17,17,13,15,15,17,19,15,17,17,19, 0,11,
        14,15,19,17,12,15,16,18,18,12,14,15,19,17,14,16,
        17, 0,18,16,16,19,17, 0,11,14,14,18,19,12,15,14,
        17,17,13,16,14,17,16,14,17,16,18,18,15,18,15, 0,
        18,
};

static const static_codebook _16u0__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__16u0__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__16u0__p3_0,
        0
};

static const long _vq_quantlist__16u0__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__16u0__p4_0[] = {
         3, 5, 5, 8, 8, 6, 6, 6, 9, 9, 6, 6, 6, 9, 9, 9,
        10, 9,11,11, 9, 9, 9,11,11, 6, 7, 7,10,10, 7, 7,
         8,10,10, 7, 7, 8,10,10,10,10,10,11,12, 9,10,10,
        11,12, 6, 7, 7,10,10, 7, 8, 7,10,10, 7, 8, 7,10,
        10,10,11,10,12,11,10,10,10,13,10, 9,10,10,12,12,
        10,11,10,14,12, 9,11,11,13,13,11,12,13,13,13,11,
        12,12,15,13, 9,10,10,12,13, 9,11,10,12,13,10,10,
        11,12,13,11,12,12,12,13,11,12,12,13,13, 5, 7, 7,
        10,10, 7, 8, 8,10,10, 7, 8, 8,10,10,10,11,10,12,
        13,10,10,11,12,12, 6, 8, 8,11,10, 7, 8, 9,10,12,
         8, 9, 9,11,11,11,10,11,11,12,10,11,11,13,12, 7,
         8, 8,10,11, 8, 9, 8,11,10, 8, 9, 9,11,11,10,12,
        10,13,11,10,11,11,13,13,10,11,10,14,13,10,10,11,
        13,13,10,12,11,14,13,12,11,13,12,13,13,12,13,14,
        14,10,11,11,13,13,10,11,10,12,13,10,12,12,12,14,
        12,12,12,14,12,12,13,12,17,15, 5, 7, 7,10,10, 7,
         8, 8,10,10, 7, 8, 8,11,10,10,10,11,12,12,10,11,
        11,12,13, 6, 8, 8,11,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,11,11,11,12,12,10,10,11,12,13, 6, 8, 8,10,
        11, 8, 9, 9,11,11, 7, 9, 7,11,10,10,12,12,13,13,
        11,11,10,13,11, 9,11,10,14,13,11,11,11,15,13,10,
        10,11,13,13,12,13,13,14,14,12,11,12,12,13,10,11,
        11,12,13,10,11,12,13,13,10,11,10,13,12,12,12,13,
        14, 0,12,13,11,13,11, 8,10,10,13,13,10,11,11,14,
        13,10,11,11,13,12,13,14,14,14,15,12,12,12,15,14,
         9,11,10,13,12,10,10,11,13,14,11,11,11,15,12,13,
        12,14,15,16,13,13,13,14,13, 9,11,11,12,12,10,12,
        11,13,13,10,11,11,13,14,13,13,13,15,15,13,13,14,
        17,15,11,12,12,14,14,10,11,12,13,15,12,13,13, 0,
        15,13,11,14,12,16,14,16,14, 0,15,11,12,12,14,16,
        11,13,12,16,15,12,13,13,14,15,12,14,12,15,13,15,
        14,14,16,16, 8,10,10,13,13,10,11,10,13,14,10,11,
        11,13,13,13,13,12,14,14,14,13,13,16,17, 9,10,10,
        12,14,10,12,11,14,13,10,11,12,13,14,12,12,12,15,
        15,13,13,13,14,14, 9,10,10,13,13,10,11,12,12,14,
        10,11,10,13,13,13,13,13,14,16,13,13,13,14,14,11,
        12,13,15,13,12,14,13,14,16,12,12,13,13,14,13,14,
        14,17,15,13,12,17,13,16,11,12,13,14,15,12,13,14,
        14,17,11,12,11,14,14,13,16,14,16, 0,14,15,11,15,
        11,
};

static const static_codebook _16u0__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__16u0__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__16u0__p4_0,
        0
};

static const long _vq_quantlist__16u0__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__16u0__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 6, 8, 8, 8, 8,
         9, 9, 4, 6, 6, 8, 8, 8, 8, 9, 9, 7, 8, 8, 9, 9,
         9, 9,11,10, 7, 8, 8, 9, 9, 9, 9,10,11, 7, 8, 8,
         9, 9,10,10,11,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
         9, 9,10,10,11,11,12,12, 9, 9, 9,10,10,11,11,12,
        12,
};

static const static_codebook _16u0__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__16u0__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__16u0__p5_0,
        0
};

static const long _vq_quantlist__16u0__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__16u0__p6_0[] = {
         1, 4, 4, 7, 7,10,10,12,12,13,13,18,17, 3, 6, 6,
         9, 9,11,11,13,13,14,14,18,17, 3, 6, 6, 9, 9,11,
        11,13,13,14,14,17,18, 7, 9, 9,11,11,13,13,14,14,
        15,15, 0, 0, 7, 9, 9,11,11,13,13,14,14,15,16,19,
        18,10,11,11,13,13,14,14,16,15,17,18, 0, 0,10,11,
        11,13,13,14,14,15,15,16,18, 0, 0,11,13,13,14,14,
        15,15,17,17, 0,19, 0, 0,11,13,13,14,14,14,15,16,
        18, 0,19, 0, 0,13,14,14,15,15,18,17,18,18, 0,19,
         0, 0,13,14,14,15,16,16,16,18,18,19, 0, 0, 0,16,
        17,17, 0,17,19,19, 0,19, 0, 0, 0, 0,16,19,16,17,
        18, 0,19, 0, 0, 0, 0, 0, 0,
};

static const static_codebook _16u0__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__16u0__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__16u0__p6_0,
        0
};

static const long _vq_quantlist__16u0__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__16u0__p6_1[] = {
         1, 4, 5, 6, 6, 4, 6, 6, 6, 6, 4, 6, 6, 6, 6, 6,
         6, 6, 7, 7, 6, 6, 6, 7, 7,
};

static const static_codebook _16u0__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__16u0__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__16u0__p6_1,
        0
};

static const long _vq_quantlist__16u0__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u0__p7_0[] = {
         1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7,
};

static const static_codebook _16u0__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u0__p7_0,
        1, -518803456, 1628680192, 2, 0,
        (long *)_vq_quantlist__16u0__p7_0,
        0
};

static const long _vq_quantlist__16u0__p7_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__16u0__p7_1[] = {
         1, 5, 5, 6, 5, 9,10,11,11,10,10,10,10,10,10, 5,
         8, 8, 8,10,10,10,10,10,10,10,10,10,10,10, 5, 8,
         9, 9, 9,10,10,10,10,10,10,10,10,10,10, 5,10, 8,
        10,10,10,10,10,10,10,10,10,10,10,10, 4, 8, 9,10,
        10,10,10,10,10,10,10,10,10,10,10, 9,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10, 9,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _16u0__p7_1 = {
        2, 225,
        (char *)_vq_lengthlist__16u0__p7_1,
        1, -520986624, 1620377600, 4, 0,
        (long *)_vq_quantlist__16u0__p7_1,
        0
};

static const long _vq_quantlist__16u0__p7_2[] = {
        10,
        9,
        11,
        8,
        12,
        7,
        13,
        6,
        14,
        5,
        15,
        4,
        16,
        3,
        17,
        2,
        18,
        1,
        19,
        0,
        20,
};

static const char _vq_lengthlist__16u0__p7_2[] = {
         1, 6, 6, 7, 8, 7, 7,10, 9,10, 9,11,10, 9,11,10,
         9, 9, 9, 9,10, 6, 8, 7, 9, 9, 8, 8,10,10, 9,11,
        11,12,12,10, 9,11, 9,12,10, 9, 6, 9, 8, 9,12, 8,
         8,11, 9,11,11,12,11,12,12,10,11,11,10,10,11, 7,
        10, 9, 9, 9, 9, 9,10, 9,10, 9,10,10,12,10,10,10,
        11,12,10,10, 7, 9, 9, 9,10, 9, 9,10,10, 9, 9, 9,
        11,11,10,10,10,10, 9, 9,12, 7, 9,10, 9,11, 9,10,
         9,10,11,11,11,10,11,12, 9,12,11,10,10,10, 7, 9,
         9, 9, 9,10,12,10, 9,11,12,10,11,12,12,11, 9,10,
        11,10,11, 7, 9,10,10,11,10, 9,10,11,11,11,10,12,
        12,12,11,11,10,11,11,12, 8, 9,10,12,11,10,10,12,
        12,12,12,12,10,11,11, 9,11,10,12,11,11, 8, 9,10,
        10,11,12,11,11,10,10,10,12,12,12, 9,10,12,12,12,
        12,12, 8,10,11,10,10,12, 9,11,12,12,11,12,12,12,
        12,10,12,10,10,10,10, 8,12,11,11,11,10,10,11,12,
        12,12,12,11,12,12,12,11,11,11,12,10, 9,10,10,12,
        10,12,10,12,12,10,10,10,11,12,12,12,11,12,12,12,
        11,10,11,12,12,12,11,12,12,11,12,12,11,12,12,12,
        12,11,12,12,10,10,10,10,11,11,12,11,12,12,12,12,
        12,12,12,11,12,11,10,11,11,12,11,11, 9,10,10,10,
        12,10,10,11, 9,11,12,11,12,11,12,12,10,11,10,12,
         9, 9, 9,12,11,10,11,10,12,10,12,10,12,12,12,11,
        11,11,11,11,10, 9,10,10,11,10,11,11,12,11,10,11,
        12,12,12,11,11, 9,12,10,12, 9,10,12,10,10,11,10,
        11,11,12,11,10,11,10,11,11,11,11,12,11,11,10, 9,
        10,10,10, 9,11,11,10, 9,12,10,11,12,11,12,12,11,
        12,11,12,11,10,11,10,12,11,12,11,12,11,12,10,11,
        10,10,12,11,10,11,11,11,10,
};

static const static_codebook _16u0__p7_2 = {
        2, 441,
        (char *)_vq_lengthlist__16u0__p7_2,
        1, -529268736, 1611661312, 5, 0,
        (long *)_vq_quantlist__16u0__p7_2,
        0
};

static const char _huff_lengthlist__16u0__single[] = {
         3, 5, 8, 7,14, 8, 9,19, 5, 2, 5, 5, 9, 6, 9,19,
         8, 4, 5, 7, 8, 9,13,19, 7, 4, 6, 5, 9, 6, 9,19,
        12, 8, 7, 9,10,11,13,19, 8, 5, 8, 6, 9, 6, 7,19,
         8, 8,10, 7, 7, 4, 5,19,12,17,19,15,18,13,11,18,
};

static const static_codebook _huff_book__16u0__single = {
        2, 64,
        (char *)_huff_lengthlist__16u0__single,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__16u1__long[] = {
         3, 6,10, 8,12, 8,14, 8,14,19, 5, 3, 5, 5, 7, 6,
        11, 7,16,19, 7, 5, 6, 7, 7, 9,11,12,19,19, 6, 4,
         7, 5, 7, 6,10, 7,18,18, 8, 6, 7, 7, 7, 7, 8, 9,
        18,18, 7, 5, 8, 5, 7, 5, 8, 6,18,18,12, 9,10, 9,
         9, 9, 8, 9,18,18, 8, 7,10, 6, 8, 5, 6, 4,11,18,
        11,15,16,12,11, 8, 8, 6, 9,18,14,18,18,18,16,16,
        16,13,16,18,
};

static const static_codebook _huff_book__16u1__long = {
        2, 100,
        (char *)_huff_lengthlist__16u1__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__16u1__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u1__p1_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 7, 7,10,10, 7,
         9,10, 5, 7, 8, 7,10, 9, 7,10,10, 5, 8, 8, 8,10,
        10, 8,10,10, 7,10,10,10,11,12,10,12,13, 7,10,10,
         9,13,11,10,12,13, 5, 8, 8, 8,10,10, 8,10,10, 7,
        10,10,10,12,12, 9,11,12, 7,10,11,10,12,12,10,13,
        11,
};

static const static_codebook _16u1__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u1__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__16u1__p1_0,
        0
};

static const long _vq_quantlist__16u1__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u1__p2_0[] = {
         3, 4, 4, 5, 6, 6, 5, 6, 6, 5, 6, 6, 6, 7, 8, 6,
         7, 8, 5, 6, 6, 6, 8, 7, 6, 8, 7, 5, 6, 6, 6, 8,
         8, 6, 8, 8, 6, 8, 8, 7, 7,10, 8, 9, 9, 6, 8, 8,
         7, 9, 8, 8, 9,10, 5, 6, 6, 6, 8, 8, 7, 8, 8, 6,
         8, 8, 8,10, 9, 7, 8, 9, 6, 8, 8, 8, 9, 9, 7,10,
         8,
};

static const static_codebook _16u1__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u1__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__16u1__p2_0,
        0
};

static const long _vq_quantlist__16u1__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__16u1__p3_0[] = {
         1, 5, 5, 8, 8, 6, 7, 7, 9, 9, 5, 7, 7, 9, 9, 9,
        10, 9,11,11, 9, 9,10,11,11, 6, 8, 8,10,10, 8, 9,
        10,11,11, 8, 9,10,11,11,10,11,11,12,13,10,11,11,
        13,13, 6, 8, 8,10,10, 8,10, 9,11,11, 8,10, 9,11,
        11,10,11,11,13,13,10,11,11,13,12, 9,11,11,14,13,
        10,12,12,15,14,10,12,11,14,13,12,13,13,15,15,12,
        13,13,16,14, 9,11,11,13,14,10,11,12,14,14,10,12,
        12,14,15,12,13,13,14,15,12,13,14,15,16, 5, 8, 8,
        11,11, 8,10,10,12,12, 8,10,10,12,12,11,12,12,14,
        14,11,12,12,14,14, 8,10,10,12,12, 9,11,12,12,13,
        10,12,12,13,13,12,12,13,14,15,11,13,13,15,15, 7,
        10,10,12,12, 9,12,11,13,12,10,11,12,13,13,12,13,
        12,15,14,11,12,13,15,15,10,12,12,15,14,11,13,13,
        16,15,11,13,13,16,15,14,13,14,15,16,13,15,15,17,
        17,10,12,12,14,15,11,12,12,15,15,11,13,13,15,16,
        13,15,13,16,15,13,15,15,16,17, 5, 8, 8,11,11, 8,
        10,10,12,12, 8,10,10,12,12,11,12,12,14,14,11,12,
        12,14,14, 7,10,10,12,12,10,12,12,14,13, 9,11,12,
        12,13,12,13,13,15,15,12,12,13,13,15, 7,10,10,12,
        13,10,11,12,13,13,10,12,11,13,13,11,13,13,15,15,
        12,13,12,15,14, 9,12,12,15,14,11,13,13,15,15,11,
        12,13,15,15,13,14,14,17,19,13,13,14,16,16,10,12,
        12,14,15,11,13,13,15,16,11,13,12,16,15,13,15,15,
        17,18,14,15,13,16,15, 8,11,11,15,14,10,12,12,16,
        15,10,12,12,16,16,14,15,15,18,17,13,14,15,16,18,
         9,12,12,15,15,11,12,14,16,17,11,13,13,16,15,15,
        15,15,17,18,14,15,16,17,17, 9,12,12,15,15,11,14,
        13,16,16,11,13,13,16,16,15,16,15,17,18,14,16,15,
        17,16,12,14,14,17,16,12,14,15,18,17,13,15,15,17,
        17,15,15,18,16,20,15,16,17,18,18,11,14,14,16,17,
        13,15,14,18,17,13,15,15,17,17,15,17,15,18,17,15,
        17,16,19,18, 8,11,11,14,15,10,12,12,15,15,10,12,
        12,16,16,13,14,14,17,16,14,15,15,17,17, 9,12,12,
        15,16,11,13,13,16,16,11,12,13,16,16,14,16,15,20,
        17,14,16,16,17,17, 9,12,12,15,16,11,13,13,16,17,
        11,13,13,17,16,14,15,15,17,18,15,15,15,18,18,11,
        14,14,17,16,13,15,15,17,17,13,14,14,18,17,15,16,
        16,18,19,15,15,17,17,19,11,14,14,16,17,13,15,14,
        17,19,13,15,14,18,17,15,17,16,18,18,15,17,15,18,
        16,
};

static const static_codebook _16u1__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__16u1__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__16u1__p3_0,
        0
};

static const long _vq_quantlist__16u1__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__16u1__p4_0[] = {
         4, 5, 5, 8, 8, 6, 6, 7, 9, 9, 6, 6, 6, 9, 9, 9,
        10, 9,11,11, 9, 9,10,11,11, 6, 7, 7,10, 9, 7, 7,
         8, 9,10, 7, 7, 8,10,10,10,10,10,10,12, 9, 9,10,
        11,12, 6, 7, 7, 9, 9, 7, 8, 7,10,10, 7, 8, 7,10,
        10, 9,10, 9,12,11,10,10, 9,12,10, 9,10,10,12,11,
        10,10,10,12,12, 9,10,10,12,12,12,11,12,13,13,11,
        11,12,12,13, 9,10,10,11,12, 9,10,10,12,12,10,10,
        10,12,12,11,12,11,14,13,11,12,12,14,13, 5, 7, 7,
        10,10, 7, 8, 8,10,10, 7, 8, 7,10,10,10,10,10,12,
        12,10,10,10,12,12, 6, 8, 7,10,10, 7, 7, 9,10,11,
         8, 9, 9,11,10,10,10,11,11,13,10,10,11,12,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,10,11,10,11,
        10,13,11,10,11,10,12,12,10,11,10,12,11,10,10,10,
        12,13,10,11,11,13,12,11,11,13,11,14,12,12,13,14,
        14, 9,10,10,12,13,10,11,10,13,12,10,11,11,12,13,
        11,12,11,14,12,12,13,13,15,14, 5, 7, 7,10,10, 7,
         7, 8,10,10, 7, 8, 8,10,10,10,10,10,11,12,10,10,
        10,12,12, 7, 8, 8,10,10, 8, 9, 8,11,10, 7, 8, 9,
        10,11,10,11,11,12,12,10,10,11,11,13, 7, 7, 8,10,
        10, 8, 8, 9,10,11, 7, 9, 7,11,10,10,11,11,13,12,
        11,11,10,13,11, 9,10,10,12,12,10,11,11,13,12,10,
        10,11,12,12,12,13,13,14,14,11,11,12,12,14,10,10,
        11,12,12,10,11,11,12,13,10,10,10,13,12,12,13,13,
        15,14,12,13,10,14,11, 8,10,10,12,12,10,11,10,13,
        13, 9,10,10,12,12,12,13,13,15,14,11,12,12,13,13,
         9,10,10,13,12,10,10,11,13,13,10,11,10,13,12,12,
        12,13,14,15,12,13,12,15,13, 9,10,10,12,13,10,11,
        10,13,12,10,10,11,12,13,12,14,12,15,13,12,12,13,
        14,15,11,12,11,14,13,11,11,12,14,15,12,13,12,15,
        14,13,11,15,11,16,13,14,14,16,15,11,12,12,14,14,
        11,12,11,14,13,12,12,13,14,15,13,14,12,16,12,14,
        14,14,15,15, 8,10,10,12,12, 9,10,10,12,12,10,10,
        11,13,13,11,12,12,13,13,12,13,13,14,15, 9,10,10,
        13,12,10,11,11,13,12,10,10,11,13,13,12,13,12,15,
        14,12,12,13,13,16, 9, 9,10,12,13,10,10,11,12,13,
        10,11,10,13,13,12,12,13,13,15,13,13,12,15,13,11,
        12,12,14,14,12,13,12,15,14,11,11,12,13,14,14,14,
        14,16,15,13,12,15,12,16,11,11,12,13,14,12,13,13,
        14,15,10,12,11,14,13,14,15,14,16,16,13,14,11,15,
        11,
};

static const static_codebook _16u1__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__16u1__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__16u1__p4_0,
        0
};

static const long _vq_quantlist__16u1__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__16u1__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 6, 8, 8, 8, 8,
        10,10, 4, 5, 6, 8, 8, 8, 8,10,10, 7, 8, 8, 9, 9,
         9, 9,11,11, 7, 8, 8, 9, 9, 9, 9,11,11, 7, 8, 8,
        10, 9,11,11,12,11, 7, 8, 8, 9, 9,11,11,12,12, 9,
        10,10,11,11,12,12,13,12, 9,10,10,11,11,12,12,12,
        13,
};

static const static_codebook _16u1__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__16u1__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__16u1__p5_0,
        0
};

static const long _vq_quantlist__16u1__p6_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__16u1__p6_0[] = {
         3, 4, 4, 6, 6, 7, 7, 9, 9, 4, 4, 4, 6, 6, 8, 8,
         9, 9, 4, 4, 4, 6, 6, 7, 7, 9, 9, 6, 6, 6, 7, 7,
         8, 8,10, 9, 6, 6, 6, 7, 7, 8, 8, 9,10, 7, 8, 7,
         8, 8, 9, 9,10,10, 7, 8, 8, 8, 8, 9, 9,10,10, 9,
         9, 9,10,10,10,10,11,11, 9, 9, 9,10,10,10,10,11,
        11,
};

static const static_codebook _16u1__p6_0 = {
        2, 81,
        (char *)_vq_lengthlist__16u1__p6_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__16u1__p6_0,
        0
};

static const long _vq_quantlist__16u1__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__16u1__p7_0[] = {
         1, 4, 4, 4, 8, 8, 4, 8, 8, 5,11, 9, 8,12,11, 8,
        12,11, 5,10,11, 8,11,12, 8,11,12, 4,11,11,11,14,
        13,10,13,13, 8,14,13,12,14,16,12,16,15, 8,14,14,
        13,16,14,12,15,16, 4,11,11,10,14,13,11,14,14, 8,
        15,14,12,15,15,12,14,16, 8,14,14,11,16,15,12,15,
        13,
};

static const static_codebook _16u1__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__16u1__p7_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__16u1__p7_0,
        0
};

static const long _vq_quantlist__16u1__p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__16u1__p7_1[] = {
         2, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8, 4, 6, 5, 7, 7,
         8, 8, 8, 8, 8, 8, 4, 5, 6, 7, 7, 8, 8, 8, 8, 8,
         8, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 6, 7, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 7, 8, 8, 8, 8, 9, 9, 9,10,
         9,10, 7, 8, 8, 8, 8, 9, 9, 9, 9,10, 9, 8, 8, 8,
         9, 9,10,10,10,10,10,10, 8, 8, 8, 9, 9, 9, 9,10,
        10,10,10, 8, 8, 8, 9, 9, 9,10,10,10,10,10, 8, 8,
         8, 9, 9,10,10,10,10,10,10,
};

static const static_codebook _16u1__p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__16u1__p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__16u1__p7_1,
        0
};

static const long _vq_quantlist__16u1__p8_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__16u1__p8_0[] = {
         1, 4, 4, 5, 5, 8, 8,10,10,12,12, 4, 7, 7, 8, 8,
         9, 9,12,11,14,13, 4, 7, 7, 7, 8, 9,10,11,11,13,
        12, 5, 8, 8, 9, 9,11,11,12,13,15,14, 5, 7, 8, 9,
         9,11,11,13,13,17,15, 8, 9,10,11,11,12,13,17,14,
        17,16, 8,10, 9,11,11,12,12,13,15,15,17,10,11,11,
        12,13,14,15,15,16,16,17, 9,11,11,12,12,14,15,17,
        15,15,16,11,14,12,14,15,16,15,16,16,16,15,11,13,
        13,14,14,15,15,16,16,15,16,
};

static const static_codebook _16u1__p8_0 = {
        2, 121,
        (char *)_vq_lengthlist__16u1__p8_0,
        1, -524582912, 1618345984, 4, 0,
        (long *)_vq_quantlist__16u1__p8_0,
        0
};

static const long _vq_quantlist__16u1__p8_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__16u1__p8_1[] = {
         2, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8, 4, 6, 6, 7, 7,
         8, 7, 8, 8, 8, 8, 4, 6, 6, 7, 7, 7, 7, 8, 8, 8,
         8, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 9, 6, 7, 7, 7,
         7, 8, 8, 8, 8, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9,
         9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8, 8,
         8, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9,
         9, 9, 9, 8, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9, 8, 8,
         8, 9, 9, 9, 9, 9, 9, 9, 9,
};

static const static_codebook _16u1__p8_1 = {
        2, 121,
        (char *)_vq_lengthlist__16u1__p8_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__16u1__p8_1,
        0
};

static const long _vq_quantlist__16u1__p9_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__16u1__p9_0[] = {
         1, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8,
};

static const static_codebook _16u1__p9_0 = {
        2, 225,
        (char *)_vq_lengthlist__16u1__p9_0,
        1, -514071552, 1627381760, 4, 0,
        (long *)_vq_quantlist__16u1__p9_0,
        0
};

static const long _vq_quantlist__16u1__p9_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__16u1__p9_1[] = {
         1, 6, 5, 9, 9,10,10, 6, 7, 9, 9,10,10,10,10, 5,
        10, 8,10, 8,10,10, 8, 8,10, 9,10,10,10,10, 5, 8,
         9,10,10,10,10, 8,10,10,10,10,10,10,10, 9,10,10,
        10,10,10,10, 9, 9,10,10,10,10,10,10, 9, 9, 8, 9,
        10,10,10, 9,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10, 8,10,10,10,10,
        10,10,10,10,10,10,10,10,10, 6, 8, 8,10,10,10, 8,
        10,10,10,10,10,10,10,10, 5, 8, 8,10,10,10, 9, 9,
        10,10,10,10,10,10,10,10, 9,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9,
};

static const static_codebook _16u1__p9_1 = {
        2, 225,
        (char *)_vq_lengthlist__16u1__p9_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__16u1__p9_1,
        0
};

static const long _vq_quantlist__16u1__p9_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__16u1__p9_2[] = {
         1, 6, 6, 7, 8, 8,11,10, 9, 9,11, 9,10, 9,11,11,
         9, 6, 7, 6,11, 8,11, 9,10,10,11, 9,11,10,10,10,
        11, 9, 5, 7, 7, 8, 8,10,11, 8, 8,11, 9, 9,10,11,
         9,10,11, 8, 9, 6, 8, 8, 9, 9,10,10,11,11,11, 9,
        11,10, 9,11, 8, 8, 8, 9, 8, 9,10,11, 9, 9,11,11,
        10, 9, 9,11,10, 8,11, 8, 9, 8,11, 9,10, 9,10,11,
        11,10,10, 9,10,10, 8, 8, 9,10,10,10, 9,11, 9,10,
        11,11,11,11,10, 9,11, 9, 9,11,11,10, 8,11,11,11,
         9,10,10,11,10,11,11, 9,11,10, 9,11,10,10,10,10,
         9,11,10,11,10, 9, 9,10,11, 9, 8,10,11,11,10,10,
        11, 9,11,10,11,11,10,11, 9, 9, 8,10, 8, 9,11, 9,
         8,10,10, 9,11,10,11,10,11, 9,11, 8,10,11,11,11,
        11,10,10,11,11,11,11,10,11,11,10, 9, 8,10,10, 9,
        11,10,11,11,11, 9, 9, 9,11,11,11,10,10, 9, 9,10,
         9,11,11,11,11, 8,10,11,10,11,11,10,11,11, 9, 9,
         9,10, 9,11, 9,11,11,11,11,11,10,11,11,10,11,10,
        11,11, 9,11,10,11,10, 9,10, 9,10,10,11,11,11,11,
         9,10, 9,10,11,11,10,11,11,11,11,11,11,10,11,11,
        10,
};

static const static_codebook _16u1__p9_2 = {
        2, 289,
        (char *)_vq_lengthlist__16u1__p9_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__16u1__p9_2,
        0
};

static const char _huff_lengthlist__16u1__short[] = {
         5, 7,10, 9,11,10,15,11,13,16, 6, 4, 6, 6, 7, 7,
        10, 9,12,16,10, 6, 5, 6, 6, 7,10,11,16,16, 9, 6,
         7, 6, 7, 7,10, 8,14,16,11, 6, 5, 4, 5, 6, 8, 9,
        15,16, 9, 6, 6, 5, 6, 6, 9, 8,14,16,12, 7, 6, 6,
         5, 6, 6, 7,13,16, 8, 6, 7, 6, 5, 5, 4, 4,11,16,
         9, 8, 9, 9, 7, 7, 6, 5,13,16,14,14,16,15,16,15,
        16,16,16,16,
};

static const static_codebook _huff_book__16u1__short = {
        2, 100,
        (char *)_huff_lengthlist__16u1__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__16u2__long[] = {
	 5, 8,10,10,10,11,11,12,14,18, 7, 5, 5, 6, 8, 9,
	10,12,14,17, 9, 5, 4, 5, 6, 8,10,11,13,19, 9, 5,
	 4, 4, 5, 6, 9,10,12,17, 8, 6, 5, 4, 4, 5, 7,10,
	11,15, 8, 7, 7, 6, 5, 5, 6, 9,11,14, 8, 9, 8, 7,
	 6, 5, 6, 7,11,14, 9,11,11, 9, 7, 6, 6, 6, 9,14,
	11,14,15,13, 9, 8, 7, 7, 9,14,13,15,19,17,12,11,
	10, 9,10,14,
};

static const static_codebook _huff_book__16u2__long = {
	2, 100,
	(char *)_huff_lengthlist__16u2__long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__16u2_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__16u2_p1_0[] = {
	 1, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 9, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 8, 9, 9, 5, 7, 7, 8, 9,
	 9, 7, 9, 9, 7, 9, 9, 9,10,11, 9,10,10, 7, 9, 9,
	 9,10, 9, 9,10,11, 5, 8, 7, 7, 9, 9, 8, 9, 9, 7,
	 9, 9, 9,11,10, 9, 9,10, 7, 9, 9, 9,10,10, 9,11,
	10,
};

static const static_codebook _16u2_p1_0 = {
	4, 81,
	(char *)_vq_lengthlist__16u2_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__16u2_p1_0,
	0
};

static const long _vq_quantlist__16u2_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__16u2_p2_0[] = {
	 3, 5, 5, 8, 8, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9, 9,
	10, 9,11,11, 9, 9, 9,11,11, 5, 7, 7, 9, 9, 7, 8,
	 8,10,10, 7, 8, 8,10,10,10,10,10,12,12, 9,10,10,
	11,12, 5, 7, 7, 9, 9, 7, 8, 8,10,10, 7, 8, 8,10,
	10, 9,10,10,12,11,10,10,10,12,12, 9,10,10,12,12,
	10,10,10,12,12, 9,10,10,12,12,12,12,12,14,14,11,
	12,12,13,14, 9,10,10,12,12, 9,10,10,12,12,10,10,
	10,12,12,11,12,12,14,13,12,12,12,14,13, 5, 7, 7,
	 9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,
	12,10,10,10,12,12, 7, 8, 8,11,10, 8, 9, 9,11,11,
	 8, 9, 9,11,11,10,11,11,12,13,10,11,11,12,13, 7,
	 8, 8,10,10, 8, 9, 8,11,10, 8, 9, 9,11,11,10,11,
	10,13,12,10,11,11,13,13,10,11,10,13,12,10,11,11,
	13,13,10,11,11,13,13,12,12,13,13,14,12,13,13,14,
	14, 9,10,10,12,12,10,11,10,13,12,10,11,11,13,13,
	12,13,12,14,13,12,13,13,14,15, 5, 7, 7, 9,10, 7,
	 8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,12,10,10,
	11,12,12, 7, 8, 8,10,10, 8, 9, 9,11,11, 8, 8, 9,
	10,11,10,11,11,13,13,10,10,11,12,13, 7, 8, 8,10,
	10, 8, 9, 9,11,11, 8, 9, 9,11,11,10,11,11,13,12,
	10,11,11,13,12, 9,10,10,12,12,10,11,11,13,13,10,
	10,11,12,13,12,13,13,15,14,12,12,13,12,14, 9,10,
	11,12,13,10,11,11,13,13,10,11,11,13,13,12,13,13,
	14,14,12,13,12,14,13, 8,10,10,12,12, 9,11,10,13,
	12, 9,10,10,12,13,12,13,13,14,14,12,12,12,14,14,
	 9,10,10,13,13,10,11,11,13,13,10,11,11,13,13,13,
	13,13,14,15,12,13,13,14,15, 9,10,10,12,13,10,11,
	10,13,13,10,11,11,12,13,12,13,12,15,14,12,13,13,
	14,15,11,12,12,15,14,12,12,13,14,15,12,13,13,15,
	14,13,13,15,14,16,14,14,14,16,15,11,12,12,14,14,
	11,12,12,14,14,12,13,13,14,15,13,14,13,15,13,14,
	14,14,15,16, 8, 9,10,12,12, 9,10,10,13,12, 9,10,
	11,12,13,12,12,12,14,14,12,13,13,14,14, 9,10,10,
	13,12,10,11,11,13,13,10,10,11,13,13,12,13,13,15,
	14,12,12,13,14,15, 9,10,10,13,13,10,11,11,13,13,
	10,11,11,13,13,12,13,13,14,14,13,13,13,15,15,11,
	12,12,14,13,12,13,13,15,14,11,12,12,14,14,14,14,
	14,16,15,13,13,14,13,16,11,12,12,14,14,12,13,13,
	14,15,12,13,12,14,14,14,14,14,16,16,14,15,13,16,
	14,
};

static const static_codebook _16u2_p2_0 = {
	4, 625,
	(char *)_vq_lengthlist__16u2_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__16u2_p2_0,
	0
};

static const long _vq_quantlist__16u2_p3_0[] = {
	4,
	3,
	5,
	2,
	6,
	1,
	7,
	0,
	8,
};

static const char _vq_lengthlist__16u2_p3_0[] = {
	 2, 4, 4, 6, 6, 7, 7, 9, 9, 4, 5, 5, 6, 6, 8, 7,
	 9, 9, 4, 5, 5, 6, 6, 7, 8, 9, 9, 6, 6, 6, 7, 7,
	 8, 8,10,10, 6, 6, 6, 7, 7, 8, 8,10,10, 7, 8, 7,
	 8, 8, 9, 9,11,10, 7, 7, 8, 8, 8, 9, 9,10,11, 9,
	 9, 9,10,10,11,10,11,11, 9, 9, 9,10,10,10,11,11,
	11,
};

static const static_codebook _16u2_p3_0 = {
	2, 81,
	(char *)_vq_lengthlist__16u2_p3_0,
	1, -531628032, 1611661312, 4, 0,
	(long *)_vq_quantlist__16u2_p3_0,
	0
};

static const long _vq_quantlist__16u2_p4_0[] = {
	8,
	7,
	9,
	6,
	10,
	5,
	11,
	4,
	12,
	3,
	13,
	2,
	14,
	1,
	15,
	0,
	16,
};

static const char _vq_lengthlist__16u2_p4_0[] = {
	 2, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9,10,10,11,11,11,
	11, 5, 5, 5, 7, 6, 8, 7, 9, 9, 9, 9,10,10,11,11,
	12,12, 5, 5, 5, 6, 6, 7, 8, 8, 9, 9, 9,10,10,11,
	11,12,12, 6, 7, 6, 7, 7, 8, 8, 9, 9, 9, 9,10,10,
	11,11,12,12, 6, 6, 7, 7, 7, 8, 8, 9, 9, 9, 9,10,
	10,11,11,12,12, 7, 8, 8, 8, 8, 9, 9, 9, 9,10,10,
	11,11,11,11,12,12, 7, 7, 8, 8, 8, 9, 9, 9, 9,10,
	10,11,11,11,11,12,12, 8, 9, 9, 9, 9, 9, 9,10,10,
	10,10,11,11,12,12,12,12, 8, 9, 9, 9, 9, 9, 9,10,
	10,10,10,11,11,12,12,12,12, 9, 9, 9, 9, 9,10,10,
	10,10,10,11,11,11,12,12,13,13, 9, 9, 9, 9, 9,10,
	10,10,10,11,10,11,11,12,12,13,13,10,10,10,10,10,
	11,11,11,11,11,11,11,12,12,12,13,13,10,10,10,10,
	10,11,11,11,11,11,11,12,11,12,12,13,13,11,11,11,
	11,11,11,11,12,12,12,12,12,12,13,13,13,13,11,11,
	11,11,11,11,11,12,12,12,12,13,12,13,13,13,13,11,
	12,12,12,12,12,12,12,12,13,13,13,13,13,13,14,14,
	11,12,12,12,12,12,12,12,13,13,13,13,13,13,13,14,
	14,
};

static const static_codebook _16u2_p4_0 = {
	2, 289,
	(char *)_vq_lengthlist__16u2_p4_0,
	1, -529530880, 1611661312, 5, 0,
	(long *)_vq_quantlist__16u2_p4_0,
	0
};

static const long _vq_quantlist__16u2_p5_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__16u2_p5_0[] = {
	 1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 8, 7, 9, 9, 7,
	 9,10, 5, 8, 8, 7,10, 9, 7,10, 9, 5, 8, 8, 8,11,
	10, 8,10,10, 7,10,10, 9, 9,12,10,12,12, 7,10,10,
	 9,12,10,10,11,12, 5, 8, 8, 8,10,10, 8,11,11, 7,
	11,10,10,12,11, 9,10,12, 7,10,11,10,12,12, 9,12,
	 9,
};

static const static_codebook _16u2_p5_0 = {
	4, 81,
	(char *)_vq_lengthlist__16u2_p5_0,
	1, -529137664, 1618345984, 2, 0,
	(long *)_vq_quantlist__16u2_p5_0,
	0
};

static const long _vq_quantlist__16u2_p5_1[] = {
	5,
	4,
	6,
	3,
	7,
	2,
	8,
	1,
	9,
	0,
	10,
};

static const char _vq_lengthlist__16u2_p5_1[] = {
	 2, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8, 5, 6, 6, 7, 7,
	 7, 7, 8, 8, 8, 8, 5, 6, 6, 6, 7, 7, 7, 8, 8, 8,
	 8, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 6, 7, 7, 7,
	 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8,
	 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8,
	 8, 8, 8, 8, 8, 9, 9, 9, 9,
};

static const static_codebook _16u2_p5_1 = {
	2, 121,
	(char *)_vq_lengthlist__16u2_p5_1,
	1, -531365888, 1611661312, 4, 0,
	(long *)_vq_quantlist__16u2_p5_1,
	0
};

static const long _vq_quantlist__16u2_p6_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__16u2_p6_0[] = {
	 1, 5, 4, 7, 7, 8, 8, 8, 8,10,10,11,11, 4, 6, 6,
	 7, 7, 9, 9, 9, 9,10,10,11,11, 4, 6, 6, 7, 7, 9,
	 9, 9, 9,10,10,11,11, 7, 8, 8, 9, 9, 9, 9,10,10,
	11,11,12,12, 7, 7, 7, 9, 8,10, 9,10,10,11,11,12,
	12, 8, 9, 9, 9,10,10,10,11,11,12,12,13,13, 8, 9,
	 9,10, 9,10,10,11,11,12,12,13,13, 8, 9, 9,10,10,
	11,11,11,11,12,12,13,13, 8, 9, 9,10,10,11,11,12,
	11,12,12,13,13,10,10,10,11,11,12,12,12,12,13,13,
	14,14,10,10,10,11,11,12,12,12,12,13,13,14,14,11,
	11,11,12,12,13,13,13,13,14,14,14,14,11,11,11,12,
	12,13,13,13,13,14,14,14,14,
};

static const static_codebook _16u2_p6_0 = {
	2, 169,
	(char *)_vq_lengthlist__16u2_p6_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__16u2_p6_0,
	0
};

static const long _vq_quantlist__16u2_p6_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__16u2_p6_1[] = {
	 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _16u2_p6_1 = {
	2, 25,
	(char *)_vq_lengthlist__16u2_p6_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__16u2_p6_1,
	0
};

static const long _vq_quantlist__16u2_p7_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__16u2_p7_0[] = {
	 1, 4, 4, 7, 7, 8, 8, 8, 8, 9, 9,10,10, 4, 6, 6,
	 8, 8, 9, 9, 9, 9,10,10,11,10, 4, 6, 6, 8, 8, 9,
	 9, 9, 9,10,10,11,11, 7, 8, 8,10, 9,10,10,10,10,
	11,11,12,12, 7, 8, 8,10,10,10,10,10,10,11,11,12,
	12, 8, 9, 9,10,10,11,11,11,11,12,12,13,13, 8, 9,
	 9,10,10,11,11,11,11,12,12,13,13, 8, 9, 9,11,10,
	11,11,12,12,13,13,14,13, 8, 9, 9,10,10,11,11,12,
	12,13,13,13,13, 9,10,10,11,11,12,12,13,13,13,13,
	14,14, 9,10,10,11,11,12,12,13,13,13,13,14,14,10,
	11,11,12,12,13,13,14,13,14,14,15,14,10,11,11,12,
	12,13,13,14,13,14,14,15,14,
};

static const static_codebook _16u2_p7_0 = {
	2, 169,
	(char *)_vq_lengthlist__16u2_p7_0,
	1, -523206656, 1618345984, 4, 0,
	(long *)_vq_quantlist__16u2_p7_0,
	0
};

static const long _vq_quantlist__16u2_p7_1[] = {
	5,
	4,
	6,
	3,
	7,
	2,
	8,
	1,
	9,
	0,
	10,
};

static const char _vq_lengthlist__16u2_p7_1[] = {
	 2, 5, 5, 7, 7, 7, 7, 7, 7, 8, 8, 5, 6, 6, 7, 7,
	 7, 7, 8, 8, 8, 8, 5, 6, 6, 7, 7, 7, 7, 8, 8, 8,
	 8, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7,
	 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8,
	 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _16u2_p7_1 = {
	2, 121,
	(char *)_vq_lengthlist__16u2_p7_1,
	1, -531365888, 1611661312, 4, 0,
	(long *)_vq_quantlist__16u2_p7_1,
	0
};

static const long _vq_quantlist__16u2_p8_0[] = {
	7,
	6,
	8,
	5,
	9,
	4,
	10,
	3,
	11,
	2,
	12,
	1,
	13,
	0,
	14,
};

static const char _vq_lengthlist__16u2_p8_0[] = {
	 1, 4, 4, 7, 7, 8, 8, 7, 7, 9, 8,10, 9,11,11, 4,
	 7, 6, 9, 8, 9, 9, 9, 9,10, 9,11, 9,12, 9, 4, 6,
	 7, 8, 8, 9, 9, 9, 9,10,10,10,11,11,12, 7, 9, 8,
	10,10,11,11,10,10,11,11,12,12,13,12, 7, 8, 8,10,
	10,10,11,10,10,11,11,11,12,12,13, 8, 9, 9,11,11,
	11,11,11,11,12,12,13,13,13,13, 8, 9, 9,11,11,11,
	11,11,11,12,12,13,13,13,14, 8, 9, 9,10,10,11,11,
	12,11,13,13,14,13,14,14, 8, 9, 9,10,10,11,11,12,
	12,12,12,13,13,14,14, 9,10,10,11,11,12,12,13,12,
	13,13,14,14,15,15, 9,10,10,11,11,12,12,12,13,13,
	13,14,14,14,15,10,11,11,12,12,13,13,14,13,14,14,
	15,14,15,15,10,11,11,12,12,13,12,13,14,14,14,14,
	14,15,15,11,12,12,13,13,13,13,14,14,15,14,15,15,
	16,16,11,12,12,13,13,13,13,14,14,14,15,15,15,16,
	16,
};

static const static_codebook _16u2_p8_0 = {
	2, 225,
	(char *)_vq_lengthlist__16u2_p8_0,
	1, -520986624, 1620377600, 4, 0,
	(long *)_vq_quantlist__16u2_p8_0,
	0
};

static const long _vq_quantlist__16u2_p8_1[] = {
	10,
	9,
	11,
	8,
	12,
	7,
	13,
	6,
	14,
	5,
	15,
	4,
	16,
	3,
	17,
	2,
	18,
	1,
	19,
	0,
	20,
};

static const char _vq_lengthlist__16u2_p8_1[] = {
	 3, 5, 5, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 5, 6, 6, 7, 7, 8, 8, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9,10,10,10,10, 5, 6, 6, 7, 7, 8,
	 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 7,
	 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9,10,10,10,10,
	10,10,10,10, 7, 7, 7, 8, 8, 9, 9, 9, 9, 9, 9, 9,
	 9,10, 9,10,10,10, 9,10, 9, 8, 8, 8, 9, 8, 9, 9,
	 9, 9,10, 9,10,10,10,10,10,10,10,10,10,10, 8, 8,
	 8, 8, 9, 9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,
	10,10,10, 8, 9, 9, 9, 9, 9, 9, 9, 9,10,10,10,10,
	10,10,10,10,10,10,10,10, 8, 9, 9, 9, 9, 9, 9, 9,
	10,10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
	 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10, 9, 9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
	 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10, 9, 9, 9,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10, 9, 9,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10, 9,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10, 9,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10, 9,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,
};

static const static_codebook _16u2_p8_1 = {
	2, 441,
	(char *)_vq_lengthlist__16u2_p8_1,
	1, -529268736, 1611661312, 5, 0,
	(long *)_vq_quantlist__16u2_p8_1,
	0
};

static const long _vq_quantlist__16u2_p9_0[] = {
	7,
	6,
	8,
	5,
	9,
	4,
	10,
	3,
	11,
	2,
	12,
	1,
	13,
	0,
	14,
};

static const char _vq_lengthlist__16u2_p9_0[] = {
	 1, 5, 3, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 5,
	 7, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 5, 7,
	 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,
};

static const static_codebook _16u2_p9_0 = {
	2, 225,
	(char *)_vq_lengthlist__16u2_p9_0,
	1, -510036736, 1631393792, 4, 0,
	(long *)_vq_quantlist__16u2_p9_0,
	0
};

static const long _vq_quantlist__16u2_p9_1[] = {
	9,
	8,
	10,
	7,
	11,
	6,
	12,
	5,
	13,
	4,
	14,
	3,
	15,
	2,
	16,
	1,
	17,
	0,
	18,
};

static const char _vq_lengthlist__16u2_p9_1[] = {
	 1, 4, 4, 7, 7, 7, 7, 7, 6, 9, 7,10, 8,12,12,13,
	13,14,14, 4, 7, 7, 9, 9, 9, 8, 9, 8,10, 9,11, 9,
	14, 9,14,10,13,11, 4, 7, 7, 9, 9, 9, 9, 8, 9,10,
	10,11,11,12,13,12,13,14,15, 7, 9, 9,10,11,10,10,
	10,10,11,12,13,13,13,14,17,14,15,16, 7, 9, 9,10,
	10,10,10,10,10,11,12,13,13,14,14,15,15,18,18, 8,
	 9, 9,11,10,11,11,11,12,13,12,14,14,16,15,15,17,
	18,15, 8, 9, 9,10,10,11,11,11,11,13,13,14,14,15,
	15,15,16,16,18, 7, 9, 8,10,10,11,11,12,12,14,14,
	15,15,16,16,15,17,16,18, 8, 9, 9,10,10,11,12,12,
	12,13,13,16,15,17,16,17,18,17,18, 9,10,10,12,11,
	13,13,14,13,14,14,15,17,16,18,17,18,17,18, 9,10,
	10,12,11,12,13,13,14,15,16,14,15,16,18,18,18,18,
	17,11,11,11,13,13,14,14,16,15,15,15,16,15,15,18,
	18,18,17,16,11,11,12,13,13,15,14,15,16,16,16,17,
	16,15,18,17,18,16,18,12,13,13,15,15,15,16,18,16,
	17,16,17,16,17,17,17,18,18,17,13,13,13,15,13,16,
	15,17,16,16,16,18,18,18,18,16,17,17,18,13,15,14,
	15,15,18,17,18,18,18,16,18,17,18,17,18,16,17,17,
	14,14,14,15,16,17,16,18,18,18,17,18,17,18,18,18,
	16,16,16,14,17,16,17,15,16,18,18,17,18,17,18,17,
	18,18,18,17,18,17,15,16,15,18,15,18,17,16,18,18,
	18,18,18,18,17,18,16,18,17,
};

static const static_codebook _16u2_p9_1 = {
	2, 361,
	(char *)_vq_lengthlist__16u2_p9_1,
	1, -518287360, 1622704128, 5, 0,
	(long *)_vq_quantlist__16u2_p9_1,
	0
};

static const long _vq_quantlist__16u2_p9_2[] = {
	24,
	23,
	25,
	22,
	26,
	21,
	27,
	20,
	28,
	19,
	29,
	18,
	30,
	17,
	31,
	16,
	32,
	15,
	33,
	14,
	34,
	13,
	35,
	12,
	36,
	11,
	37,
	10,
	38,
	9,
	39,
	8,
	40,
	7,
	41,
	6,
	42,
	5,
	43,
	4,
	44,
	3,
	45,
	2,
	46,
	1,
	47,
	0,
	48,
};

static const char _vq_lengthlist__16u2_p9_2[] = {
	 2, 3, 4, 4, 4, 5, 5, 6, 5, 6, 6, 6, 6, 6, 6, 7,
	 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
	 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 8, 8, 8, 8, 8,
	 8,
};

static const static_codebook _16u2_p9_2 = {
	1, 49,
	(char *)_vq_lengthlist__16u2_p9_2,
	1, -526909440, 1611661312, 6, 0,
	(long *)_vq_quantlist__16u2_p9_2,
	0
};

static const char _huff_lengthlist__16u2__short[] = {
	 8,11,13,13,15,16,19,19,19,19,11, 8, 8, 9, 9,11,
	13,15,19,20,14, 8, 7, 7, 8, 9,12,13,15,20,15, 9,
	 6, 5, 5, 7,10,12,14,18,14, 9, 7, 5, 3, 4, 7,10,
	12,16,13,10, 8, 6, 3, 3, 5, 8,11,14,11,10, 9, 7,
	 5, 4, 4, 6,11,14,10,10,10, 8, 6, 5, 5, 6,10,14,
	10,10,10, 9, 8, 7, 7, 7,10,14,11,12,12,12,11,10,
	10,10,12,16,
};

static const static_codebook _huff_book__16u2__short = {
	2, 100,
	(char *)_huff_lengthlist__16u2__short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__8u0__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u0__p1_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 8, 8,10,10, 7,
        10,10, 5, 8, 8, 7,10,10, 8,10,10, 4, 9, 8, 8,11,
        11, 8,11,11, 7,11,11,10,11,13,10,13,13, 7,11,11,
        10,13,12,10,13,13, 5, 9, 8, 8,11,11, 8,11,11, 7,
        11,11, 9,13,13,10,12,13, 7,11,11,10,13,13,10,13,
        11,
};

static const static_codebook _8u0__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u0__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__8u0__p1_0,
        0
};

static const long _vq_quantlist__8u0__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u0__p2_0[] = {
         2, 4, 4, 5, 6, 6, 5, 6, 6, 5, 7, 7, 6, 7, 8, 6,
         7, 8, 5, 7, 7, 6, 8, 8, 7, 9, 7, 5, 7, 7, 7, 9,
         9, 7, 8, 8, 6, 9, 8, 7, 7,10, 8,10,10, 6, 8, 8,
         8,10, 8, 8,10,10, 5, 7, 7, 7, 8, 8, 7, 8, 9, 6,
         8, 8, 8,10,10, 8, 8,10, 6, 8, 9, 8,10,10, 7,10,
         8,
};

static const static_codebook _8u0__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u0__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__8u0__p2_0,
        0
};

static const long _vq_quantlist__8u0__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__8u0__p3_0[] = {
         1, 5, 5, 7, 7, 6, 7, 7, 9, 9, 6, 7, 7, 9, 9, 8,
        10, 9,11,11, 8, 9, 9,11,11, 6, 8, 8,10,10, 8,10,
        10,11,11, 8,10,10,11,11,10,11,11,12,12,10,11,11,
        12,13, 6, 8, 8,10,10, 8,10,10,11,11, 8,10,10,11,
        11, 9,10,11,12,12,10,11,11,12,12, 8,11,11,14,13,
        10,12,11,15,13,10,12,11,14,14,12,13,12,16,14,12,
        14,12,16,15, 8,11,11,13,14,10,11,12,13,15,10,11,
        12,13,15,11,12,13,14,15,12,12,14,14,16, 5, 8, 8,
        11,11, 9,11,11,12,12, 8,10,11,12,12,11,12,12,15,
        14,11,12,12,14,14, 7,11,10,13,12,10,11,12,13,14,
        10,12,12,14,13,12,13,13,14,15,12,13,13,15,15, 7,
        10,11,12,13,10,12,11,14,13,10,12,13,13,15,12,13,
        12,14,14,11,13,13,15,16, 9,12,12,15,14,11,13,13,
        15,16,11,13,13,16,16,13,14,15,15,15,12,14,15,17,
        16, 9,12,12,14,15,11,13,13,15,16,11,13,13,16,18,
        13,14,14,17,16,13,15,15,17,18, 5, 8, 9,11,11, 8,
        11,11,12,12, 8,10,11,12,12,11,12,12,14,14,11,12,
        12,14,15, 7,11,10,12,13,10,12,12,14,13,10,11,12,
        13,14,11,13,13,15,14,12,13,13,14,15, 7,10,11,13,
        13,10,12,12,13,14,10,12,12,13,13,11,13,13,16,16,
        12,13,13,15,14, 9,12,12,16,15,10,13,13,15,15,11,
        13,13,17,15,12,15,15,18,17,13,14,14,15,16, 9,12,
        12,15,15,11,13,13,15,16,11,13,13,15,15,12,15,15,
        16,16,13,15,14,17,15, 7,11,11,15,15,10,13,13,16,
        15,10,13,13,15,16,14,15,15,17,19,13,15,14,15,18,
         9,12,12,16,16,11,13,14,17,16,11,13,13,17,16,15,
        15,16,17,19,13,15,16, 0,18, 9,12,12,16,15,11,14,
        13,17,17,11,13,14,16,16,15,16,16,19,18,13,15,15,
        17,19,11,14,14,19,16,12,14,15, 0,18,12,16,15,18,
        17,15,15,18,16,19,14,15,17,19,19,11,14,14,18,19,
        13,15,14,19,19,12,16,15,18,17,15,17,15, 0,16,14,
        17,16,19, 0, 7,11,11,14,14,10,12,12,15,15,10,13,
        13,16,15,13,15,15,17, 0,14,15,15,16,19, 9,12,12,
        16,16,11,14,14,16,16,11,13,13,16,16,14,17,16,19,
         0,14,18,17,17,19, 9,12,12,15,16,11,13,13,15,17,
        12,14,13,19,16,13,15,15,17,19,15,17,16,17,19,11,
        14,14,19,16,12,15,15,19,17,13,14,15,17,19,14,16,
        17,19,19,16,15,16,17,19,11,15,14,16,16,12,15,15,
        19, 0,12,14,15,19,19,14,16,16, 0,18,15,19,14,18,
        16,
};

static const static_codebook _8u0__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__8u0__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__8u0__p3_0,
        0
};

static const long _vq_quantlist__8u0__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__8u0__p4_0[] = {
         3, 5, 5, 8, 8, 5, 6, 7, 9, 9, 6, 7, 6, 9, 9, 9,
         9, 9,10,11, 9, 9, 9,11,10, 6, 7, 7,10,10, 7, 7,
         8,10,10, 7, 8, 8,10,10,10,10,10,10,11, 9,10,10,
        11,12, 6, 7, 7,10,10, 7, 8, 8,10,10, 7, 8, 7,10,
        10, 9,10,10,12,11,10,10,10,11,10, 9,10,10,12,11,
        10,10,10,13,11, 9,10,10,12,12,11,11,12,12,13,11,
        11,11,12,13, 9,10,10,12,12,10,10,11,12,12,10,10,
        11,12,12,11,11,11,13,13,11,12,12,13,13, 5, 7, 7,
        10,10, 7, 8, 8,10,10, 7, 8, 8,10,10,10,11,11,12,
        12,10,11,10,12,12, 7, 8, 8,11,11, 7, 8, 9,10,11,
         8, 9, 9,11,11,11,10,11,10,12,10,11,11,12,13, 7,
         8, 8,10,11, 8, 9, 8,12,10, 8, 9, 9,11,12,10,11,
        10,13,11,10,11,11,13,12, 9,11,10,13,12,10,10,11,
        12,12,10,11,11,13,13,12,10,13,11,14,11,12,12,15,
        13, 9,11,11,13,13,10,11,11,13,12,10,11,11,12,14,
        12,13,11,14,12,12,12,12,14,14, 5, 7, 7,10,10, 7,
         8, 8,10,10, 7, 8, 8,11,10,10,11,11,12,12,10,11,
        10,12,12, 7, 8, 8,10,11, 8, 9, 9,12,11, 8, 8, 9,
        10,11,10,11,11,12,13,11,10,11,11,13, 6, 8, 8,10,
        11, 8, 9, 9,11,11, 7, 9, 7,11,10,10,11,11,12,12,
        10,11,10,13,10, 9,11,10,13,12,10,12,11,13,13,10,
        10,11,12,13,11,12,13,15,14,11,11,13,12,13, 9,10,
        11,12,13,10,11,11,12,13,10,11,10,13,12,12,13,13,
        13,14,12,12,11,14,11, 8,10,10,12,13,10,11,11,13,
        13,10,11,10,13,13,12,13,14,15,14,12,12,12,14,13,
         9,10,10,13,12,10,10,12,13,13,10,11,11,15,12,12,
        12,13,15,14,12,13,13,15,13, 9,10,11,12,13,10,12,
        10,13,12,10,11,11,12,13,12,14,12,15,13,12,12,12,
        15,14,11,12,11,14,13,11,11,12,14,14,12,13,13,14,
        13,13,11,15,11,15,14,14,14,16,15,11,12,12,13,14,
        11,13,11,14,14,12,12,13,14,15,12,14,12,15,12,13,
        15,14,16,15, 8,10,10,12,12,10,10,10,12,13,10,11,
        11,13,13,12,12,12,13,14,13,13,13,15,15, 9,10,10,
        12,12,10,11,11,13,12,10,10,11,13,13,12,12,12,14,
        14,12,12,13,15,14, 9,10,10,13,12,10,10,12,12,13,
        10,11,10,13,13,12,13,13,14,14,12,13,12,14,13,11,
        12,12,14,13,12,13,12,14,14,10,12,12,14,14,14,14,
        14,16,14,13,12,14,12,15,10,12,12,14,15,12,13,13,
        14,16,11,12,11,15,14,13,14,14,14,15,13,14,11,14,
        12,
};

static const static_codebook _8u0__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__8u0__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__8u0__p4_0,
        0
};

static const long _vq_quantlist__8u0__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__8u0__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 6, 8, 7, 8, 8,
        10,10, 4, 6, 6, 8, 8, 8, 8,10,10, 6, 8, 8, 9, 9,
         9, 9,11,11, 7, 8, 8, 9, 9, 9, 9,11,11, 7, 8, 8,
         9, 9,10,10,12,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
        10,10,11,11,11,12,12,12, 9,10,10,11,11,12,12,12,
        12,
};

static const static_codebook _8u0__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__8u0__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__8u0__p5_0,
        0
};

static const long _vq_quantlist__8u0__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__8u0__p6_0[] = {
         1, 4, 4, 7, 7, 9, 9,11,11,12,12,16,16, 3, 6, 6,
         9, 9,11,11,12,12,13,14,18,16, 3, 6, 7, 9, 9,11,
        11,13,12,14,14,17,16, 7, 9, 9,11,11,12,12,14,14,
        14,14,17,16, 7, 9, 9,11,11,13,12,13,13,14,14,17,
         0, 9,11,11,12,13,14,14,14,13,15,14,17,17, 9,11,
        11,12,12,14,14,13,14,14,15, 0, 0,11,12,12,15,14,
        15,14,15,14,15,16,17, 0,11,12,13,13,13,14,14,15,
        14,15,15, 0, 0,12,14,14,15,15,14,16,15,15,17,16,
         0,18,13,14,14,15,14,15,14,15,16,17,16, 0, 0,17,
        17,18, 0,16,18,16, 0, 0, 0,17, 0, 0,16, 0, 0,16,
        16, 0,15, 0,17, 0, 0, 0, 0,
};

static const static_codebook _8u0__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__8u0__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__8u0__p6_0,
        0
};

static const long _vq_quantlist__8u0__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__8u0__p6_1[] = {
         1, 4, 4, 6, 6, 4, 6, 5, 7, 7, 4, 5, 6, 7, 7, 6,
         7, 7, 7, 7, 6, 7, 7, 7, 7,
};

static const static_codebook _8u0__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__8u0__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__8u0__p6_1,
        0
};

static const long _vq_quantlist__8u0__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u0__p7_0[] = {
         1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7,
};

static const static_codebook _8u0__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u0__p7_0,
        1, -518803456, 1628680192, 2, 0,
        (long *)_vq_quantlist__8u0__p7_0,
        0
};

static const long _vq_quantlist__8u0__p7_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__8u0__p7_1[] = {
         1, 5, 5, 5, 5,10,10,11,11,11,11,11,11,11,11, 5,
         7, 6, 8, 8, 9,10,11,11,11,11,11,11,11,11, 6, 6,
         7, 9, 7,11,10,11,11,11,11,11,11,11,11, 5, 6, 6,
        11, 8,11,11,11,11,11,11,11,11,11,11, 5, 6, 6, 9,
        10,11,10,11,11,11,11,11,11,11,11, 7,10,10,11,11,
        11,11,11,11,11,11,11,11,11,11, 7,11, 8,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _8u0__p7_1 = {
        2, 225,
        (char *)_vq_lengthlist__8u0__p7_1,
        1, -520986624, 1620377600, 4, 0,
        (long *)_vq_quantlist__8u0__p7_1,
        0
};

static const long _vq_quantlist__8u0__p7_2[] = {
        10,
        9,
        11,
        8,
        12,
        7,
        13,
        6,
        14,
        5,
        15,
        4,
        16,
        3,
        17,
        2,
        18,
        1,
        19,
        0,
        20,
};

static const char _vq_lengthlist__8u0__p7_2[] = {
         1, 6, 5, 7, 7, 9, 9, 9, 9,10,12,12,10,11,11,10,
        11,11,11,10,11, 6, 8, 8, 9, 9,10,10, 9,10,11,11,
        10,11,11,11,11,10,11,11,11,11, 6, 7, 8, 9, 9, 9,
        10,11,10,11,12,11,10,11,11,11,11,11,11,12,10, 8,
         9, 9,10, 9,10,10, 9,10,10,10,10,10, 9,10,10,10,
        10, 9,10,10, 9, 9, 9, 9,10,10, 9, 9,10,10,11,10,
         9,12,10,11,10, 9,10,10,10, 8, 9, 9,10, 9,10, 9,
         9,10,10, 9,10, 9,11,10,10,10,10,10, 9,10, 8, 8,
         9, 9,10, 9,11, 9, 8, 9, 9,10,11,10,10,10,11,12,
         9, 9,11, 8, 9, 8,11,10,11,10,10, 9,11,10,10,10,
        10,10,10,10,11,11,11,11, 8, 9, 9, 9,10,10,10,11,
        11,12,11,12,11,10,10,10,12,11,11,11,10, 8,10, 9,
        11,10,10,11,12,10,11,12,11,11,12,11,12,12,10,11,
        11,10, 9, 9,10,11,12,10,10,10,11,10,11,11,10,12,
        12,10,11,10,11,12,10, 9,10,10,11,10,11,11,11,11,
        11,12,11,11,11, 9,11,10,11,10,11,10, 9, 9,10,11,
        11,11,10,10,11,12,12,11,12,11,11,11,12,12,12,12,
        11, 9,11,11,12,10,11,11,11,11,11,11,12,11,11,12,
        11,11,11,10,11,11, 9,11,10,11,11,11,10,10,10,11,
        11,11,12,10,11,10,11,11,11,11,12, 9,11,10,11,11,
        10,10,11,11, 9,11,11,12,10,10,10,10,10,11,11,10,
         9,10,11,11,12,11,10,10,12,11,11,12,11,12,11,11,
        10,10,11,11,10,12,11,10,11,10,11,10,10,10,11,11,
        10,10,11,11,11,11,10,10,10,12,11,11,11,11,10, 9,
        10,11,11,11,12,11,11,11,12,10,11,11,11, 9,10,11,
        11,11,11,11,11,10,10,11,11,12,11,10,11,12,11,10,
        10,11, 9,10,11,11,11,11,11,10,11,11,10,12,11,11,
        11,12,11,11,11,10,10,11,11,
};

static const static_codebook _8u0__p7_2 = {
        2, 441,
        (char *)_vq_lengthlist__8u0__p7_2,
        1, -529268736, 1611661312, 5, 0,
        (long *)_vq_quantlist__8u0__p7_2,
        0
};

static const char _huff_lengthlist__8u0__single[] = {
         4, 7,11, 9,12, 8, 7,10, 6, 4, 5, 5, 7, 5, 6,16,
         9, 5, 5, 6, 7, 7, 9,16, 7, 4, 6, 5, 7, 5, 7,17,
        10, 7, 7, 8, 7, 7, 8,18, 7, 5, 6, 4, 5, 4, 5,15,
         7, 6, 7, 5, 6, 4, 5,15,12,13,18,12,17,11, 9,17,
};

static const static_codebook _huff_book__8u0__single = {
        2, 64,
        (char *)_huff_lengthlist__8u0__single,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__8u1__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u1__p1_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 8, 7, 9,10, 7,
         9, 9, 5, 8, 8, 7,10, 9, 7, 9, 9, 5, 8, 8, 8,10,
        10, 8,10,10, 7,10,10, 9,10,12,10,12,12, 7,10,10,
         9,12,11,10,12,12, 5, 8, 8, 8,10,10, 8,10,10, 7,
        10,10,10,12,12, 9,11,12, 7,10,10,10,12,12, 9,12,
        10,
};

static const static_codebook _8u1__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u1__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__8u1__p1_0,
        0
};

static const long _vq_quantlist__8u1__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u1__p2_0[] = {
         3, 4, 5, 5, 6, 6, 5, 6, 6, 5, 7, 6, 6, 7, 8, 6,
         7, 8, 5, 6, 6, 6, 8, 7, 6, 8, 7, 5, 6, 6, 7, 8,
         8, 6, 7, 7, 6, 8, 7, 7, 7, 9, 8, 9, 9, 6, 7, 8,
         7, 9, 7, 8, 9, 9, 5, 6, 6, 6, 7, 7, 7, 8, 8, 6,
         8, 7, 8, 9, 9, 7, 7, 9, 6, 7, 8, 8, 9, 9, 7, 9,
         7,
};

static const static_codebook _8u1__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u1__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__8u1__p2_0,
        0
};

static const long _vq_quantlist__8u1__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__8u1__p3_0[] = {
         1, 5, 5, 7, 7, 6, 7, 7, 9, 9, 6, 7, 7, 9, 9, 8,
        10, 9,11,11, 9, 9, 9,11,11, 6, 8, 8,10,10, 8,10,
        10,11,11, 8, 9,10,11,11,10,11,11,12,12,10,11,11,
        12,13, 6, 8, 8,10,10, 8,10, 9,11,11, 8,10, 9,11,
        11,10,11,11,12,12,10,11,11,12,12, 9,11,11,14,13,
        10,12,11,14,14,10,12,11,14,13,12,13,13,15,14,12,
        13,13,15,14, 8,11,11,13,14,10,11,12,13,15,10,11,
        12,14,14,12,13,13,14,15,12,13,13,14,15, 5, 8, 8,
        11,11, 8,10,10,12,12, 8,10,10,12,12,11,12,12,14,
        13,11,12,12,13,14, 8,10,10,12,12, 9,11,12,13,14,
        10,12,12,13,13,12,12,13,14,14,11,13,13,15,15, 7,
        10,10,12,12, 9,12,11,14,12,10,11,12,13,14,12,13,
        12,14,14,12,13,13,15,16,10,12,12,15,14,11,12,13,
        15,15,11,13,13,15,16,14,14,15,15,16,13,14,15,17,
        15, 9,12,12,14,15,11,13,12,15,15,11,13,13,15,15,
        13,14,13,15,14,13,14,14,17, 0, 5, 8, 8,11,11, 8,
        10,10,12,12, 8,10,10,12,12,11,12,12,14,14,11,12,
        12,14,14, 7,10,10,12,12,10,12,12,13,13, 9,11,12,
        12,13,11,12,13,15,15,11,12,13,14,15, 8,10,10,12,
        12,10,12,11,13,13,10,12,11,13,13,11,13,13,15,14,
        12,13,12,15,13, 9,12,12,14,14,11,13,13,16,15,11,
        12,13,16,15,13,14,15,16,16,13,13,15,15,16,10,12,
        12,15,14,11,13,13,14,16,11,13,13,15,16,13,15,15,
        16,17,13,15,14,16,15, 8,11,11,14,15,10,12,12,15,
        15,10,12,12,15,16,14,15,15,16,17,13,14,14,16,16,
         9,12,12,15,15,11,13,14,15,17,11,13,13,15,16,14,
        15,16,19,17,13,15,15, 0,17, 9,12,12,15,15,11,14,
        13,16,15,11,13,13,15,16,15,15,15,18,17,13,15,15,
        17,17,11,15,14,18,16,12,14,15,17,17,12,15,15,18,
        18,15,15,16,15,19,14,16,16, 0, 0,11,14,14,16,17,
        12,15,14,18,17,12,15,15,18,18,15,17,15,18,16,14,
        16,16,18,18, 7,11,11,14,14,10,12,12,15,15,10,12,
        13,15,15,13,14,15,16,16,14,15,15,18,18, 9,12,12,
        15,15,11,13,13,16,15,11,12,13,16,16,14,15,15,17,
        16,15,16,16,17,17, 9,12,12,15,15,11,13,13,15,17,
        11,14,13,16,15,13,15,15,17,17,15,15,15,18,17,11,
        14,14,17,15,12,14,15,17,18,13,13,15,17,17,14,16,
        16,19,18,16,15,17,17, 0,11,14,14,17,17,12,15,15,
        18, 0,12,15,14,18,16,14,17,17,19, 0,16,18,15, 0,
        16,
};

static const static_codebook _8u1__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__8u1__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__8u1__p3_0,
        0
};

static const long _vq_quantlist__8u1__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__8u1__p4_0[] = {
         4, 5, 5, 9, 9, 6, 7, 7, 9, 9, 6, 7, 7, 9, 9, 9,
         9, 9,11,11, 9, 9, 9,11,11, 6, 7, 7, 9, 9, 7, 7,
         8, 9,10, 7, 7, 8, 9,10, 9, 9,10,10,11, 9, 9,10,
        10,12, 6, 7, 7, 9, 9, 7, 8, 7,10, 9, 7, 8, 7,10,
         9, 9,10, 9,12,11,10,10, 9,12,10, 9,10,10,12,11,
         9,10,10,12,11, 9,10,10,12,12,11,11,12,12,13,11,
        11,12,12,13, 9, 9,10,12,11, 9,10,10,12,12,10,10,
        10,12,12,11,12,11,13,12,11,12,11,13,12, 6, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 7,10, 9,10,10,10,12,
        12,10,10,10,12,11, 7, 8, 7,10,10, 7, 7, 9,10,11,
         8, 9, 9,11,10,10,10,11,10,12,10,10,11,12,12, 7,
         8, 8,10,10, 7, 9, 8,11,10, 8, 8, 9,11,11,10,11,
        10,12,11,10,11,11,12,12, 9,10,10,12,12, 9,10,10,
        12,12,10,11,11,13,12,11,10,12,10,14,12,12,12,13,
        14, 9,10,10,12,12, 9,11,10,12,12,10,11,11,12,12,
        11,12,11,14,12,12,12,12,14,14, 5, 7, 7, 9, 9, 7,
         7, 7, 9,10, 7, 8, 8,10,10,10,10,10,11,11,10,10,
        10,12,12, 7, 8, 8,10,10, 8, 9, 8,11,10, 7, 8, 9,
        10,11,10,10,10,11,12,10,10,11,11,13, 6, 7, 8,10,
        10, 8, 9, 9,10,10, 7, 9, 7,11,10,10,11,10,12,12,
        10,11,10,12,10, 9,10,10,12,12,10,11,11,13,12, 9,
        10,10,12,12,12,12,12,14,13,11,11,12,11,14, 9,10,
        10,11,12,10,11,11,12,13, 9,10,10,12,12,12,12,12,
        14,13,11,12,10,14,11, 9, 9,10,11,12, 9,10,10,12,
        12, 9,10,10,12,12,12,12,12,14,14,11,12,12,13,12,
         9,10, 9,12,12, 9,10,11,12,13,10,11,10,13,11,12,
        12,13,13,14,12,12,12,13,13, 9,10,10,12,12,10,11,
        10,13,12,10,10,11,12,13,12,13,12,14,13,12,12,12,
        13,14,11,12,11,14,13,10,10,11,13,13,12,12,12,14,
        13,12,10,14,10,15,13,14,14,14,14,11,11,12,13,14,
        10,12,11,13,13,12,12,12,13,15,12,13,11,15,12,13,
        13,14,14,14, 9,10, 9,12,12, 9,10,10,12,12,10,10,
        10,12,12,11,11,12,12,13,12,12,12,14,14, 9,10,10,
        12,12,10,11,10,13,12,10,10,11,12,13,12,12,12,14,
        13,12,12,13,13,14, 9,10,10,12,13,10,10,11,11,12,
         9,11,10,13,12,12,12,12,13,14,12,13,12,14,13,11,
        12,11,13,13,12,13,12,14,13,10,11,12,13,13,13,13,
        13,14,15,12,11,14,12,14,11,11,12,12,13,12,12,12,
        13,14,10,12,10,14,13,13,13,13,14,15,12,14,11,15,
        10,
};

static const static_codebook _8u1__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__8u1__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__8u1__p4_0,
        0
};

static const long _vq_quantlist__8u1__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__8u1__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 5, 8, 7, 8, 8,
        10,10, 4, 6, 6, 8, 8, 8, 8,10,10, 7, 8, 8, 9, 9,
         9, 9,11,11, 7, 8, 8, 9, 9, 9, 9,11,11, 8, 8, 8,
         9, 9,10,10,12,11, 8, 8, 8, 9, 9,10,10,11,11, 9,
        10,10,11,11,11,11,13,12, 9,10,10,11,11,12,12,12,
        13,
};

static const static_codebook _8u1__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__8u1__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__8u1__p5_0,
        0
};

static const long _vq_quantlist__8u1__p6_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__8u1__p6_0[] = {
         3, 4, 4, 6, 6, 7, 7, 9, 9, 4, 4, 5, 6, 6, 7, 7,
         9, 9, 4, 4, 4, 6, 6, 7, 7, 9, 9, 6, 6, 6, 7, 7,
         8, 8, 9, 9, 6, 6, 6, 7, 7, 8, 8, 9, 9, 7, 7, 7,
         8, 8, 8, 9,10,10, 7, 7, 7, 8, 8, 9, 8,10,10, 9,
         9, 9, 9, 9,10,10,10,10, 9, 9, 9, 9, 9,10,10,10,
        10,
};

static const static_codebook _8u1__p6_0 = {
        2, 81,
        (char *)_vq_lengthlist__8u1__p6_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__8u1__p6_0,
        0
};

static const long _vq_quantlist__8u1__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__8u1__p7_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 9, 9, 8,10,10, 8,
        10,10, 5, 9, 9, 7,10,10, 8,10,10, 4,10,10, 9,12,
        12, 9,11,11, 7,12,11,10,11,13,10,13,13, 7,12,12,
        10,13,12,10,13,13, 4,10,10, 9,12,12, 9,12,12, 7,
        12,12,10,13,13,10,12,13, 7,11,12,10,13,13,10,13,
        11,
};

static const static_codebook _8u1__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__8u1__p7_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__8u1__p7_0,
        0
};

static const long _vq_quantlist__8u1__p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__8u1__p7_1[] = {
         2, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8, 4, 5, 5, 7, 7,
         8, 8, 9, 9, 9, 9, 4, 5, 5, 7, 7, 8, 8, 9, 9, 9,
         9, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 6, 7, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9,
         9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 8, 9, 9,
         9, 9, 9, 9,10,10,10,10, 8, 9, 9, 9, 9, 9, 9,10,
        10,10,10, 8, 9, 9, 9, 9, 9, 9,10,10,10,10, 8, 9,
         9, 9, 9, 9, 9,10,10,10,10,
};

static const static_codebook _8u1__p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__8u1__p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__8u1__p7_1,
        0
};

static const long _vq_quantlist__8u1__p8_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__8u1__p8_0[] = {
         1, 4, 4, 6, 6, 8, 8,10,10,11,11, 4, 6, 6, 7, 7,
         9, 9,11,11,13,12, 4, 6, 6, 7, 7, 9, 9,11,11,12,
        12, 6, 7, 7, 9, 9,11,11,12,12,13,13, 6, 7, 7, 9,
         9,11,11,12,12,13,13, 8, 9, 9,11,11,12,12,13,13,
        14,14, 8, 9, 9,11,11,12,12,13,13,14,14, 9,11,11,
        12,12,13,13,14,14,15,15, 9,11,11,12,12,13,13,14,
        14,15,14,11,12,12,13,13,14,14,15,15,16,16,11,12,
        12,13,13,14,14,15,15,15,15,
};

static const static_codebook _8u1__p8_0 = {
        2, 121,
        (char *)_vq_lengthlist__8u1__p8_0,
        1, -524582912, 1618345984, 4, 0,
        (long *)_vq_quantlist__8u1__p8_0,
        0
};

static const long _vq_quantlist__8u1__p8_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__8u1__p8_1[] = {
         2, 5, 5, 6, 6, 7, 7, 7, 7, 8, 8, 5, 6, 6, 7, 7,
         7, 7, 8, 8, 8, 8, 5, 6, 6, 7, 7, 7, 7, 8, 8, 8,
         8, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 6, 7, 7, 7,
         7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8,
         8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 9,
         8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8,
         8, 8, 8, 8, 8, 9, 9, 9, 9,
};

static const static_codebook _8u1__p8_1 = {
        2, 121,
        (char *)_vq_lengthlist__8u1__p8_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__8u1__p8_1,
        0
};

static const long _vq_quantlist__8u1__p9_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__8u1__p9_0[] = {
         1, 4, 4,11,11,11,11,11,11,11,11,11,11,11,11, 3,
        11, 8,11,11,11,11,11,11,11,11,11,11,11,11, 3, 9,
         9,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _8u1__p9_0 = {
        2, 225,
        (char *)_vq_lengthlist__8u1__p9_0,
        1, -514071552, 1627381760, 4, 0,
        (long *)_vq_quantlist__8u1__p9_0,
        0
};

static const long _vq_quantlist__8u1__p9_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__8u1__p9_1[] = {
         1, 4, 4, 7, 7, 9, 9, 7, 7, 8, 8,10,10,11,11, 4,
         7, 7, 9, 9,10,10, 8, 8,10,10,10,11,10,11, 4, 7,
         7, 9, 9,10,10, 8, 8,10, 9,11,11,11,11, 7, 9, 9,
        12,12,11,12,10,10,11,10,12,11,11,11, 7, 9, 9,11,
        11,13,12, 9, 9,11,10,11,11,12,11, 9,10,10,12,12,
        14,14,10,10,11,12,12,11,11,11, 9,10,11,11,13,14,
        13,10,11,11,11,12,11,12,12, 7, 8, 8,10, 9,11,10,
        11,12,12,11,12,14,12,13, 7, 8, 8, 9,10,10,11,12,
        12,12,11,12,12,12,13, 9, 9, 9,11,11,13,12,12,12,
        12,11,12,12,13,12, 8,10,10,11,10,11,12,12,12,12,
        12,12,14,12,12, 9,11,11,11,12,12,12,12,13,13,12,
        12,13,13,12,10,11,11,12,11,12,12,12,11,12,13,12,
        12,12,13,11,11,12,12,12,13,12,12,11,12,13,13,12,
        12,13,12,11,12,12,13,13,12,13,12,13,13,13,13,14,
        13,
};

static const static_codebook _8u1__p9_1 = {
        2, 225,
        (char *)_vq_lengthlist__8u1__p9_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__8u1__p9_1,
        0
};

static const long _vq_quantlist__8u1__p9_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__8u1__p9_2[] = {
         2, 5, 4, 6, 6, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 5, 6, 6, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 5, 6, 6, 7, 7, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 7, 7, 7, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9,
         9,10,10, 9, 7, 7, 7, 8, 8, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9,10,10, 8, 8, 8, 9, 9, 9, 9,10,10,10, 9,
        10,10,10,10,10,10, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
        10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,10,10,
        10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
         9,10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9,10,
         9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10, 9,
        10, 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
         9, 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _8u1__p9_2 = {
        2, 289,
        (char *)_vq_lengthlist__8u1__p9_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__8u1__p9_2,
        0
};

static const char _huff_lengthlist__8u1__single[] = {
         4, 7,13, 9,15, 9,16, 8,10,13, 7, 5, 8, 6, 9, 7,
        10, 7,10,11,11, 6, 7, 8, 8, 9, 9, 9,12,16, 8, 5,
         8, 6, 8, 6, 9, 7,10,12,11, 7, 7, 7, 6, 7, 7, 7,
        11,15, 7, 5, 8, 6, 7, 5, 7, 6, 9,13,13, 9, 9, 8,
         6, 6, 5, 5, 9,14, 8, 6, 8, 6, 6, 4, 5, 3, 5,13,
         9, 9,11, 8,10, 7, 8, 4, 5,12,11,16,17,15,17,12,
        13, 8, 8,15,
};

static const static_codebook _huff_book__8u1__single = {
        2, 100,
        (char *)_huff_lengthlist__8u1__single,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u0__long[] = {
         5, 8,13,10,17,11,11,15, 7, 2, 4, 5, 8, 7, 9,16,
        13, 4, 3, 5, 6, 8,11,20,10, 4, 5, 5, 7, 6, 8,18,
        15, 7, 6, 7, 8,10,14,20,10, 6, 7, 6, 9, 7, 8,17,
         9, 8,10, 8,10, 5, 4,11,12,17,19,14,16,10, 7,12,
};

static const static_codebook _huff_book__44u0__long = {
        2, 64,
        (char *)_huff_lengthlist__44u0__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u0__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u0__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,11,11, 8,
        10,10, 5, 8, 8, 8,11,10, 8,11,11, 4, 8, 8, 8,11,
        11, 8,11,11, 8,12,11,11,13,13,11,13,14, 7,11,11,
        10,13,12,11,13,14, 4, 8, 8, 8,11,11, 8,11,12, 8,
        11,11,11,13,13,10,12,13, 8,11,11,11,14,13,11,14,
        13,
};

static const static_codebook _44u0__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u0__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u0__p1_0,
        0
};

static const long _vq_quantlist__44u0__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u0__p2_0[] = {
         2, 4, 4, 5, 6, 6, 5, 6, 6, 5, 7, 7, 7, 8, 8, 6,
         8, 8, 5, 7, 7, 6, 8, 8, 7, 8, 8, 4, 7, 7, 7, 8,
         8, 7, 8, 8, 7, 8, 8, 8, 9,10, 8,10,10, 6, 8, 8,
         8,10, 8, 8,10,10, 5, 7, 7, 7, 8, 8, 7, 8, 8, 6,
         8, 8, 8,10,10, 8, 8,10, 6, 8, 8, 8,10,10, 8,10,
         9,
};

static const static_codebook _44u0__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u0__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u0__p2_0,
        0
};

static const long _vq_quantlist__44u0__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u0__p3_0[] = {
         1, 5, 5, 8, 8, 5, 8, 7, 9, 9, 5, 7, 8, 9, 9, 9,
        10, 9,12,12, 9, 9,10,12,12, 6, 8, 8,11,10, 8,10,
        10,11,11, 8, 9,10,11,11,10,11,11,14,13,10,11,11,
        13,13, 5, 8, 8,10,10, 8,10,10,11,11, 8,10,10,11,
        11,10,11,11,13,13,10,11,11,13,13, 9,11,11,15,14,
        10,12,12,15,14,10,12,11,15,14,13,14,14,16,16,12,
        14,13,17,15, 9,11,11,14,15,10,11,12,14,16,10,11,
        12,14,16,12,13,14,16,16,13,13,15,15,18, 5, 8, 8,
        11,11, 8,10,10,12,12, 8,10,10,12,13,11,12,12,14,
        14,11,12,12,15,15, 8,10,10,13,13,10,12,12,13,13,
        10,12,12,14,14,12,13,13,15,15,12,13,13,16,16, 7,
        10,10,12,12,10,12,11,13,13,10,12,12,13,14,12,13,
        12,15,14,12,13,13,16,16,10,12,12,17,16,12,13,13,
        16,15,11,13,13,17,17,15,15,15,16,17,14,15,15,19,
        19,10,12,12,15,16,11,13,12,15,18,11,13,13,16,16,
        14,15,15,17,17,14,15,15,17,19, 5, 8, 8,11,11, 8,
        10,10,12,12, 8,10,10,12,12,11,12,12,16,15,11,12,
        12,14,15, 7,10,10,13,13,10,12,12,14,13,10,11,12,
        13,13,12,13,13,16,16,12,12,13,15,15, 8,10,10,13,
        13,10,12,12,14,14,10,12,12,13,13,12,13,13,16,16,
        12,13,13,15,15,10,12,12,16,15,11,13,13,17,16,11,
        12,13,16,15,13,15,15,19,17,14,15,14,17,16,10,12,
        12,16,16,11,13,13,16,17,12,13,13,15,17,14,15,15,
        17,19,14,15,15,17,17, 8,11,11,16,16,10,13,12,17,
        17,10,12,13,16,16,15,17,16,20,19,14,15,17,18,19,
         9,12,12,16,17,11,13,14,17,18,11,13,13,19,18,16,
        17,18,19,19,15,16,16,19,19, 9,12,12,16,17,11,14,
        13,18,17,11,13,13,17,17,16,17,16,20,19,14,16,16,
        18,18,12,15,15,19,17,14,15,16, 0,20,13,15,16,20,
        17,18,16,20, 0, 0,15,16,19,20, 0,12,15,14,18,19,
        13,16,15,20,19,13,16,15,20,18,17,18,17, 0,20,16,
        17,16, 0, 0, 8,11,11,16,15,10,12,12,17,17,10,13,
        13,17,16,14,16,15,18,20,15,16,16,19,19, 9,12,12,
        16,16,11,13,13,17,16,11,13,14,17,18,15,15,16,20,
        20,16,16,17,19,19, 9,13,12,16,17,11,14,13,17,17,
        11,14,14,18,17,14,16,15,18,19,16,17,18,18,19,12,
        14,15,19,18,13,15,16,18, 0,13,14,15, 0, 0,16,16,
        17,20, 0,17,17,20,20, 0,12,15,15,19,20,13,15,15,
         0, 0,14,16,15, 0, 0,15,18,16, 0, 0,17,18,16, 0,
        19,
};

static const static_codebook _44u0__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u0__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u0__p3_0,
        0
};

static const long _vq_quantlist__44u0__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u0__p4_0[] = {
         4, 5, 5, 9, 9, 5, 6, 6, 9, 9, 5, 6, 6, 9, 9, 9,
        10, 9,12,12, 9, 9,10,12,12, 5, 7, 7,10,10, 7, 7,
         8,10,10, 6, 7, 8,10,10,10,10,10,11,13,10, 9,10,
        12,13, 5, 7, 7,10,10, 6, 8, 7,10,10, 7, 8, 7,10,
        10, 9,10,10,12,12,10,10,10,13,11, 9,10,10,13,13,
        10,11,10,13,13,10,10,10,13,13,12,12,13,14,14,12,
        12,13,14,14, 9,10,10,13,13,10,10,10,13,13,10,10,
        10,13,13,12,13,12,15,14,12,13,12,15,15, 5, 7, 6,
        10,10, 7, 8, 8,10,10, 7, 8, 8,10,10,10,11,10,13,
        13,10,10,10,12,12, 7, 8, 8,11,10, 8, 8, 9,10,11,
         8, 9, 9,11,11,11,10,11,11,14,11,11,11,13,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,14,11,10,11,11,13,13,10,11,11,14,13,10,10,11,
        14,13,10,11,11,14,14,12,11,13,12,16,13,14,14,15,
        15,10,10,11,13,14,10,11,10,14,13,10,11,11,14,14,
        12,13,12,15,13,13,13,14,15,16, 5, 7, 7,10,10, 7,
         8, 8,10,10, 7, 8, 8,10,10,10,10,10,13,13,10,10,
        11,12,13, 6, 8, 8,11,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,13,13,10,10,11,11,13, 6, 8, 8,10,
        11, 8, 9, 9,11,11, 8, 9, 8,12,10,10,11,11,13,13,
        10,11,10,14,11,10,10,10,14,13,10,11,11,14,13,10,
        10,11,13,13,12,14,14,16,16,12,12,13,13,15,10,11,
        11,13,14,10,11,11,14,15,10,11,10,13,13,13,14,13,
        16,16,12,13,11,15,12, 9,10,10,13,13,10,11,11,14,
        13,10,10,11,13,14,13,14,13,16,16,13,13,13,15,16,
         9,10,10,13,13,10,10,11,13,14,10,11,11,15,13,13,
        13,14,14,18,13,13,14,16,15, 9,10,10,13,14,10,11,
        10,14,13,10,11,11,13,14,13,14,13,16,15,13,13,14,
        15,16,12,13,12,16,14,11,11,13,15,15,13,14,13,16,
        15,15,12,16,12,17,14,15,15,17,17,12,13,13,14,16,
        11,13,11,16,15,12,13,14,15,16,14,15,13, 0,14,14,
        16,16, 0, 0, 9,10,10,13,13,10,11,10,14,14,10,11,
        11,13,13,12,13,13,14,16,13,14,14,16,16, 9,10,10,
        14,14,11,11,11,14,13,10,10,11,14,14,13,13,13,16,
        16,13,13,14,14,17, 9,10,10,13,14,10,11,11,13,15,
        10,11,10,14,14,13,13,13,14,17,13,14,13,17,14,12,
        13,13,16,14,13,14,13,16,15,12,12,13,15,16,15,15,
        16,18,16,15,13,15,14, 0,12,12,13,14,16,13,13,14,
        15,16,11,12,11,16,14,15,16,16,17,17,14,15,12,17,
        12,
};

static const static_codebook _44u0__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u0__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u0__p4_0,
        0
};

static const long _vq_quantlist__44u0__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u0__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 6, 8, 8, 8, 8,
         9, 9, 4, 6, 6, 8, 8, 8, 8, 9, 9, 7, 8, 8, 9, 9,
         9, 9,11,10, 7, 8, 8, 9, 9, 9, 9,10,10, 7, 8, 8,
         9, 9,10,10,11,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
         9, 9,10,10,11,11,12,12, 9, 9, 9,10,11,11,11,12,
        12,
};

static const static_codebook _44u0__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u0__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u0__p5_0,
        0
};

static const long _vq_quantlist__44u0__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u0__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8,10, 9,11,10,14,13, 4, 6, 5,
         8, 8, 9, 9,11,10,11,11,14,14, 4, 5, 6, 8, 8, 9,
         9,10,10,11,11,14,14, 6, 8, 8, 9, 9,10,10,11,11,
        12,12,16,15, 7, 8, 8, 9, 9,10,10,11,11,12,12,15,
        15, 9,10,10,10,10,11,11,12,12,12,12,15,15, 9,10,
         9,10,11,11,11,12,12,12,13,15,15,10,10,11,11,11,
        12,12,13,12,13,13,16,15,10,11,11,11,11,12,12,13,
        12,13,13,16,17,11,11,12,12,12,13,13,13,14,14,15,
        17,17,11,11,12,12,12,13,13,13,14,14,14,16,18,14,
        15,15,15,15,16,16,16,16,17,18, 0, 0,14,15,15,15,
        15,17,16,17,18,17,17,18, 0,
};

static const static_codebook _44u0__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u0__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u0__p6_0,
        0
};

static const long _vq_quantlist__44u0__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u0__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5,
         6, 6, 6, 6, 5, 6, 6, 6, 6,
};

static const static_codebook _44u0__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u0__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u0__p6_1,
        0
};

static const long _vq_quantlist__44u0__p7_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u0__p7_0[] = {
         1, 4, 4,11,11, 9,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11, 9,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,10,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _44u0__p7_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u0__p7_0,
        1, -518709248, 1626677248, 3, 0,
        (long *)_vq_quantlist__44u0__p7_0,
        0
};

static const long _vq_quantlist__44u0__p7_1[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u0__p7_1[] = {
         1, 4, 4, 6, 6, 6, 6, 7, 7, 8, 8, 9, 9, 5, 7, 7,
         8, 7, 7, 7, 9, 8,10, 9,10,11, 5, 7, 7, 8, 8, 7,
         7, 8, 9,10,10,11,11, 6, 8, 8, 9, 9, 9, 9,11,10,
        12,12,15,12, 6, 8, 8, 9, 9, 9, 9,11,11,12,11,14,
        12, 7, 8, 8,10,10,12,12,13,13,13,15,13,13, 7, 8,
         8,10,10,11,11,13,12,14,15,15,15, 9,10,10,11,12,
        13,13,14,15,14,15,14,15, 8,10,10,12,12,14,14,15,
        14,14,15,15,14,10,12,12,14,14,15,14,15,15,15,14,
        15,15,10,12,12,13,14,15,14,15,15,14,15,15,15,12,
        15,13,15,14,15,15,15,15,15,15,15,15,13,13,15,15,
        15,15,15,15,15,15,15,15,15,
};

static const static_codebook _44u0__p7_1 = {
        2, 169,
        (char *)_vq_lengthlist__44u0__p7_1,
        1, -523010048, 1618608128, 4, 0,
        (long *)_vq_quantlist__44u0__p7_1,
        0
};

static const long _vq_quantlist__44u0__p7_2[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u0__p7_2[] = {
         2, 5, 4, 6, 6, 7, 7, 8, 8, 8, 8, 9, 8, 5, 5, 6,
         7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 5, 6, 5, 7, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 6, 7, 7, 8, 8, 8, 8, 9, 8,
         9, 9, 9, 9, 6, 7, 7, 8, 7, 8, 8, 9, 9, 9, 9, 9,
         9, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 7, 8,
         8, 9, 8, 9, 8, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9,
         9, 9, 9, 9, 9, 9,10,10, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9,10, 9,10, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10,10,10, 9, 9, 9, 9, 9,
         9, 9, 9,10, 9, 9,10,10, 9,
};

static const static_codebook _44u0__p7_2 = {
        2, 169,
        (char *)_vq_lengthlist__44u0__p7_2,
        1, -531103744, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u0__p7_2,
        0
};

static const char _huff_lengthlist__44u0__short[] = {
        12,13,14,13,17,12,15,17, 5, 5, 6,10,10,11,15,16,
         4, 3, 3, 7, 5, 7,10,16, 7, 7, 7,10, 9,11,12,16,
         6, 5, 5, 9, 5, 6,10,16, 8, 7, 7, 9, 6, 7, 9,16,
        11, 7, 3, 6, 4, 5, 8,16,12, 9, 4, 8, 5, 7, 9,16,
};

static const static_codebook _huff_book__44u0__short = {
        2, 64,
        (char *)_huff_lengthlist__44u0__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u1__long[] = {
         5, 8,13,10,17,11,11,15, 7, 2, 4, 5, 8, 7, 9,16,
        13, 4, 3, 5, 6, 8,11,20,10, 4, 5, 5, 7, 6, 8,18,
        15, 7, 6, 7, 8,10,14,20,10, 6, 7, 6, 9, 7, 8,17,
         9, 8,10, 8,10, 5, 4,11,12,17,19,14,16,10, 7,12,
};

static const static_codebook _huff_book__44u1__long = {
        2, 64,
        (char *)_huff_lengthlist__44u1__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u1__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u1__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,11,11, 8,
        10,10, 5, 8, 8, 8,11,10, 8,11,11, 4, 8, 8, 8,11,
        11, 8,11,11, 8,12,11,11,13,13,11,13,14, 7,11,11,
        10,13,12,11,13,14, 4, 8, 8, 8,11,11, 8,11,12, 8,
        11,11,11,13,13,10,12,13, 8,11,11,11,14,13,11,14,
        13,
};

static const static_codebook _44u1__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u1__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u1__p1_0,
        0
};

static const long _vq_quantlist__44u1__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u1__p2_0[] = {
         2, 4, 4, 5, 6, 6, 5, 6, 6, 5, 7, 7, 7, 8, 8, 6,
         8, 8, 5, 7, 7, 6, 8, 8, 7, 8, 8, 4, 7, 7, 7, 8,
         8, 7, 8, 8, 7, 8, 8, 8, 9,10, 8,10,10, 6, 8, 8,
         8,10, 8, 8,10,10, 5, 7, 7, 7, 8, 8, 7, 8, 8, 6,
         8, 8, 8,10,10, 8, 8,10, 6, 8, 8, 8,10,10, 8,10,
         9,
};

static const static_codebook _44u1__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u1__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u1__p2_0,
        0
};

static const long _vq_quantlist__44u1__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u1__p3_0[] = {
         1, 5, 5, 8, 8, 5, 8, 7, 9, 9, 5, 7, 8, 9, 9, 9,
        10, 9,12,12, 9, 9,10,12,12, 6, 8, 8,11,10, 8,10,
        10,11,11, 8, 9,10,11,11,10,11,11,14,13,10,11,11,
        13,13, 5, 8, 8,10,10, 8,10,10,11,11, 8,10,10,11,
        11,10,11,11,13,13,10,11,11,13,13, 9,11,11,15,14,
        10,12,12,15,14,10,12,11,15,14,13,14,14,16,16,12,
        14,13,17,15, 9,11,11,14,15,10,11,12,14,16,10,11,
        12,14,16,12,13,14,16,16,13,13,15,15,18, 5, 8, 8,
        11,11, 8,10,10,12,12, 8,10,10,12,13,11,12,12,14,
        14,11,12,12,15,15, 8,10,10,13,13,10,12,12,13,13,
        10,12,12,14,14,12,13,13,15,15,12,13,13,16,16, 7,
        10,10,12,12,10,12,11,13,13,10,12,12,13,14,12,13,
        12,15,14,12,13,13,16,16,10,12,12,17,16,12,13,13,
        16,15,11,13,13,17,17,15,15,15,16,17,14,15,15,19,
        19,10,12,12,15,16,11,13,12,15,18,11,13,13,16,16,
        14,15,15,17,17,14,15,15,17,19, 5, 8, 8,11,11, 8,
        10,10,12,12, 8,10,10,12,12,11,12,12,16,15,11,12,
        12,14,15, 7,10,10,13,13,10,12,12,14,13,10,11,12,
        13,13,12,13,13,16,16,12,12,13,15,15, 8,10,10,13,
        13,10,12,12,14,14,10,12,12,13,13,12,13,13,16,16,
        12,13,13,15,15,10,12,12,16,15,11,13,13,17,16,11,
        12,13,16,15,13,15,15,19,17,14,15,14,17,16,10,12,
        12,16,16,11,13,13,16,17,12,13,13,15,17,14,15,15,
        17,19,14,15,15,17,17, 8,11,11,16,16,10,13,12,17,
        17,10,12,13,16,16,15,17,16,20,19,14,15,17,18,19,
         9,12,12,16,17,11,13,14,17,18,11,13,13,19,18,16,
        17,18,19,19,15,16,16,19,19, 9,12,12,16,17,11,14,
        13,18,17,11,13,13,17,17,16,17,16,20,19,14,16,16,
        18,18,12,15,15,19,17,14,15,16, 0,20,13,15,16,20,
        17,18,16,20, 0, 0,15,16,19,20, 0,12,15,14,18,19,
        13,16,15,20,19,13,16,15,20,18,17,18,17, 0,20,16,
        17,16, 0, 0, 8,11,11,16,15,10,12,12,17,17,10,13,
        13,17,16,14,16,15,18,20,15,16,16,19,19, 9,12,12,
        16,16,11,13,13,17,16,11,13,14,17,18,15,15,16,20,
        20,16,16,17,19,19, 9,13,12,16,17,11,14,13,17,17,
        11,14,14,18,17,14,16,15,18,19,16,17,18,18,19,12,
        14,15,19,18,13,15,16,18, 0,13,14,15, 0, 0,16,16,
        17,20, 0,17,17,20,20, 0,12,15,15,19,20,13,15,15,
         0, 0,14,16,15, 0, 0,15,18,16, 0, 0,17,18,16, 0,
        19,
};

static const static_codebook _44u1__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u1__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u1__p3_0,
        0
};

static const long _vq_quantlist__44u1__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u1__p4_0[] = {
         4, 5, 5, 9, 9, 5, 6, 6, 9, 9, 5, 6, 6, 9, 9, 9,
        10, 9,12,12, 9, 9,10,12,12, 5, 7, 7,10,10, 7, 7,
         8,10,10, 6, 7, 8,10,10,10,10,10,11,13,10, 9,10,
        12,13, 5, 7, 7,10,10, 6, 8, 7,10,10, 7, 8, 7,10,
        10, 9,10,10,12,12,10,10,10,13,11, 9,10,10,13,13,
        10,11,10,13,13,10,10,10,13,13,12,12,13,14,14,12,
        12,13,14,14, 9,10,10,13,13,10,10,10,13,13,10,10,
        10,13,13,12,13,12,15,14,12,13,12,15,15, 5, 7, 6,
        10,10, 7, 8, 8,10,10, 7, 8, 8,10,10,10,11,10,13,
        13,10,10,10,12,12, 7, 8, 8,11,10, 8, 8, 9,10,11,
         8, 9, 9,11,11,11,10,11,11,14,11,11,11,13,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,14,11,10,11,11,13,13,10,11,11,14,13,10,10,11,
        14,13,10,11,11,14,14,12,11,13,12,16,13,14,14,15,
        15,10,10,11,13,14,10,11,10,14,13,10,11,11,14,14,
        12,13,12,15,13,13,13,14,15,16, 5, 7, 7,10,10, 7,
         8, 8,10,10, 7, 8, 8,10,10,10,10,10,13,13,10,10,
        11,12,13, 6, 8, 8,11,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,13,13,10,10,11,11,13, 6, 8, 8,10,
        11, 8, 9, 9,11,11, 8, 9, 8,12,10,10,11,11,13,13,
        10,11,10,14,11,10,10,10,14,13,10,11,11,14,13,10,
        10,11,13,13,12,14,14,16,16,12,12,13,13,15,10,11,
        11,13,14,10,11,11,14,15,10,11,10,13,13,13,14,13,
        16,16,12,13,11,15,12, 9,10,10,13,13,10,11,11,14,
        13,10,10,11,13,14,13,14,13,16,16,13,13,13,15,16,
         9,10,10,13,13,10,10,11,13,14,10,11,11,15,13,13,
        13,14,14,18,13,13,14,16,15, 9,10,10,13,14,10,11,
        10,14,13,10,11,11,13,14,13,14,13,16,15,13,13,14,
        15,16,12,13,12,16,14,11,11,13,15,15,13,14,13,16,
        15,15,12,16,12,17,14,15,15,17,17,12,13,13,14,16,
        11,13,11,16,15,12,13,14,15,16,14,15,13, 0,14,14,
        16,16, 0, 0, 9,10,10,13,13,10,11,10,14,14,10,11,
        11,13,13,12,13,13,14,16,13,14,14,16,16, 9,10,10,
        14,14,11,11,11,14,13,10,10,11,14,14,13,13,13,16,
        16,13,13,14,14,17, 9,10,10,13,14,10,11,11,13,15,
        10,11,10,14,14,13,13,13,14,17,13,14,13,17,14,12,
        13,13,16,14,13,14,13,16,15,12,12,13,15,16,15,15,
        16,18,16,15,13,15,14, 0,12,12,13,14,16,13,13,14,
        15,16,11,12,11,16,14,15,16,16,17,17,14,15,12,17,
        12,
};

static const static_codebook _44u1__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u1__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u1__p4_0,
        0
};

static const long _vq_quantlist__44u1__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u1__p5_0[] = {
         1, 4, 4, 7, 7, 7, 7, 9, 9, 4, 6, 6, 8, 8, 8, 8,
         9, 9, 4, 6, 6, 8, 8, 8, 8, 9, 9, 7, 8, 8, 9, 9,
         9, 9,11,10, 7, 8, 8, 9, 9, 9, 9,10,10, 7, 8, 8,
         9, 9,10,10,11,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
         9, 9,10,10,11,11,12,12, 9, 9, 9,10,11,11,11,12,
        12,
};

static const static_codebook _44u1__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u1__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u1__p5_0,
        0
};

static const long _vq_quantlist__44u1__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u1__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8,10, 9,11,10,14,13, 4, 6, 5,
         8, 8, 9, 9,11,10,11,11,14,14, 4, 5, 6, 8, 8, 9,
         9,10,10,11,11,14,14, 6, 8, 8, 9, 9,10,10,11,11,
        12,12,16,15, 7, 8, 8, 9, 9,10,10,11,11,12,12,15,
        15, 9,10,10,10,10,11,11,12,12,12,12,15,15, 9,10,
         9,10,11,11,11,12,12,12,13,15,15,10,10,11,11,11,
        12,12,13,12,13,13,16,15,10,11,11,11,11,12,12,13,
        12,13,13,16,17,11,11,12,12,12,13,13,13,14,14,15,
        17,17,11,11,12,12,12,13,13,13,14,14,14,16,18,14,
        15,15,15,15,16,16,16,16,17,18, 0, 0,14,15,15,15,
        15,17,16,17,18,17,17,18, 0,
};

static const static_codebook _44u1__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u1__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u1__p6_0,
        0
};

static const long _vq_quantlist__44u1__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u1__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5,
         6, 6, 6, 6, 5, 6, 6, 6, 6,
};

static const static_codebook _44u1__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u1__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u1__p6_1,
        0
};

static const long _vq_quantlist__44u1__p7_0[] = {
        3,
        2,
        4,
        1,
        5,
        0,
        6,
};

static const char _vq_lengthlist__44u1__p7_0[] = {
         1, 3, 2, 9, 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8,
};

static const static_codebook _44u1__p7_0 = {
        2, 49,
        (char *)_vq_lengthlist__44u1__p7_0,
        1, -518017024, 1626677248, 3, 0,
        (long *)_vq_quantlist__44u1__p7_0,
        0
};

static const long _vq_quantlist__44u1__p7_1[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u1__p7_1[] = {
         1, 4, 4, 6, 6, 6, 6, 7, 7, 8, 8, 9, 9, 5, 7, 7,
         8, 7, 7, 7, 9, 8,10, 9,10,11, 5, 7, 7, 8, 8, 7,
         7, 8, 9,10,10,11,11, 6, 8, 8, 9, 9, 9, 9,11,10,
        12,12,15,12, 6, 8, 8, 9, 9, 9, 9,11,11,12,11,14,
        12, 7, 8, 8,10,10,12,12,13,13,13,15,13,13, 7, 8,
         8,10,10,11,11,13,12,14,15,15,15, 9,10,10,11,12,
        13,13,14,15,14,15,14,15, 8,10,10,12,12,14,14,15,
        14,14,15,15,14,10,12,12,14,14,15,14,15,15,15,14,
        15,15,10,12,12,13,14,15,14,15,15,14,15,15,15,12,
        15,13,15,14,15,15,15,15,15,15,15,15,13,13,15,15,
        15,15,15,15,15,15,15,15,15,
};

static const static_codebook _44u1__p7_1 = {
        2, 169,
        (char *)_vq_lengthlist__44u1__p7_1,
        1, -523010048, 1618608128, 4, 0,
        (long *)_vq_quantlist__44u1__p7_1,
        0
};

static const long _vq_quantlist__44u1__p7_2[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u1__p7_2[] = {
         2, 5, 4, 6, 6, 7, 7, 8, 8, 8, 8, 9, 8, 5, 5, 6,
         7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 5, 6, 5, 7, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 6, 7, 7, 8, 8, 8, 8, 9, 8,
         9, 9, 9, 9, 6, 7, 7, 8, 7, 8, 8, 9, 9, 9, 9, 9,
         9, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 7, 8,
         8, 9, 8, 9, 8, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9,
         9, 9, 9, 9, 9, 9,10,10, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9,10, 9,10, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10,10,10, 9, 9, 9, 9, 9,
         9, 9, 9,10, 9, 9,10,10, 9,
};

static const static_codebook _44u1__p7_2 = {
        2, 169,
        (char *)_vq_lengthlist__44u1__p7_2,
        1, -531103744, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u1__p7_2,
        0
};

static const char _huff_lengthlist__44u1__short[] = {
        12,13,14,13,17,12,15,17, 5, 5, 6,10,10,11,15,16,
         4, 3, 3, 7, 5, 7,10,16, 7, 7, 7,10, 9,11,12,16,
         6, 5, 5, 9, 5, 6,10,16, 8, 7, 7, 9, 6, 7, 9,16,
        11, 7, 3, 6, 4, 5, 8,16,12, 9, 4, 8, 5, 7, 9,16,
};

static const static_codebook _huff_book__44u1__short = {
        2, 64,
        (char *)_huff_lengthlist__44u1__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u2__long[] = {
         5, 9,14,12,15,13,10,13, 7, 4, 5, 6, 8, 7, 8,12,
        13, 4, 3, 5, 5, 6, 9,15,12, 6, 5, 6, 6, 6, 7,14,
        14, 7, 4, 6, 4, 6, 8,15,12, 6, 6, 5, 5, 5, 6,14,
         9, 7, 8, 6, 7, 5, 4,10,10,13,14,14,15,10, 6, 8,
};

static const static_codebook _huff_book__44u2__long = {
        2, 64,
        (char *)_huff_lengthlist__44u2__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u2__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u2__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,11,11, 8,
        10,11, 5, 8, 8, 8,11,10, 8,11,11, 4, 8, 8, 8,11,
        11, 8,11,11, 8,11,11,11,13,14,11,13,13, 7,11,11,
        10,13,12,11,14,14, 4, 8, 8, 8,11,11, 8,11,11, 8,
        11,11,11,14,13,10,12,13, 8,11,11,11,13,13,11,13,
        13,
};

static const static_codebook _44u2__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u2__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u2__p1_0,
        0
};

static const long _vq_quantlist__44u2__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u2__p2_0[] = {
         2, 5, 5, 5, 6, 6, 5, 6, 6, 5, 6, 6, 7, 8, 8, 6,
         8, 8, 5, 6, 6, 6, 8, 7, 7, 8, 8, 5, 6, 6, 7, 8,
         8, 6, 8, 8, 6, 8, 8, 8, 9,10, 8,10,10, 6, 8, 8,
         7,10, 8, 8,10,10, 5, 6, 6, 6, 8, 8, 7, 8, 8, 6,
         8, 8, 8,10,10, 8, 8,10, 6, 8, 8, 8,10,10, 8,10,
         9,
};

static const static_codebook _44u2__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u2__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u2__p2_0,
        0
};

static const long _vq_quantlist__44u2__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u2__p3_0[] = {
         2, 4, 4, 7, 8, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9, 8,
         9, 9,12,11, 8, 9, 9,11,12, 5, 7, 7,10,10, 7, 9,
         9,11,11, 7, 9, 9,10,11,10,11,11,13,13, 9,10,11,
        12,13, 5, 7, 7,10,10, 7, 9, 9,11,10, 7, 9, 9,11,
        11, 9,11,10,13,13,10,11,11,13,13, 8,10,10,14,13,
        10,11,11,15,14, 9,11,11,15,14,13,14,13,16,14,12,
        13,13,15,16, 8,10,10,13,14, 9,11,11,14,15,10,11,
        11,14,15,12,13,13,15,15,12,13,14,15,16, 5, 7, 7,
        10,10, 7, 9, 9,11,11, 7, 9, 9,11,12,10,11,11,14,
        13,10,11,11,14,14, 7, 9, 9,12,12, 9,11,11,13,13,
         9,11,11,13,13,12,13,12,14,14,11,12,13,15,15, 7,
         9, 9,12,12, 8,11,10,13,12, 9,11,11,13,13,11,13,
        12,15,13,11,13,13,15,16, 9,12,11,15,15,11,12,12,
        16,15,11,12,13,16,16,13,14,15,16,15,13,15,15,17,
        17, 9,11,11,14,15,10,12,12,15,15,11,13,12,15,16,
        13,15,14,16,16,13,15,15,17,19, 5, 7, 7,10,10, 7,
         9, 9,12,11, 7, 9, 9,11,11,10,11,11,14,14,10,11,
        11,13,14, 7, 9, 9,12,12, 9,11,11,13,13, 9,10,11,
        12,13,11,13,12,16,15,11,12,12,14,15, 7, 9, 9,12,
        12, 9,11,11,13,13, 9,11,11,13,12,11,13,12,15,16,
        12,13,13,15,14, 9,11,11,15,14,11,13,12,16,15,10,
        11,12,15,15,13,14,14,18,17,13,14,14,15,17,10,11,
        11,14,15,11,13,12,15,17,11,13,12,15,16,13,15,14,
        18,17,14,15,15,16,18, 7,10,10,14,14,10,12,12,15,
        15,10,12,12,15,15,14,15,15,18,17,13,15,15,16,16,
         9,11,11,16,15,11,13,13,16,18,11,13,13,16,16,15,
        16,16, 0, 0,14,15,16,18,17, 9,11,11,15,15,10,13,
        12,17,16,11,12,13,16,17,14,15,16,19,19,14,15,15,
         0,20,12,14,14, 0, 0,13,14,16,19,18,13,15,16,20,
        17,16,18, 0, 0, 0,15,16,17,18,19,11,14,14, 0,19,
        12,15,14,17,17,13,15,15, 0, 0,16,17,15,20,19,15,
        17,16,19, 0, 8,10,10,14,15,10,12,11,15,15,10,11,
        12,16,15,13,14,14,19,17,14,15,15, 0, 0, 9,11,11,
        16,15,11,13,13,17,16,10,12,13,16,17,14,15,15,18,
        18,14,15,16,20,19, 9,12,12, 0,15,11,13,13,16,17,
        11,13,13,19,17,14,16,16,18,17,15,16,16,17,19,11,
        14,14,18,18,13,14,15, 0, 0,12,14,15,19,18,15,16,
        19, 0,19,15,16,19,19,17,12,14,14,16,19,13,15,15,
         0,17,13,15,14,18,18,15,16,15, 0,18,16,17,17, 0,
         0,
};

static const static_codebook _44u2__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u2__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u2__p3_0,
        0
};

static const long _vq_quantlist__44u2__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u2__p4_0[] = {
         4, 5, 5, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 9,
         9, 9,11,11, 9, 9, 9,11,11, 5, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 7, 8,10,10,10,10,10,11,12, 9,10,10,
        11,12, 5, 7, 7, 9, 9, 6, 8, 7,10,10, 7, 8, 8,10,
        10, 9,10,10,12,11, 9,10,10,12,11, 9,10,10,12,12,
        10,10,10,13,12, 9,10,10,12,13,12,12,12,14,14,11,
        12,12,13,14, 9,10,10,12,12, 9,10,10,12,13,10,10,
        10,12,13,11,12,12,14,13,12,12,12,14,13, 5, 7, 7,
        10, 9, 7, 8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,
        12,10,10,10,12,12, 7, 8, 8,11,10, 8, 8, 9,11,11,
         8, 9, 9,11,11,10,11,11,12,13,10,11,11,13,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,13,11,10,11,11,13,13, 9,10,10,13,13,10,11,11,
        13,13,10,11,11,14,13,12,11,13,12,15,12,13,13,15,
        15, 9,10,10,12,13,10,11,10,13,13,10,11,11,13,13,
        12,13,11,15,13,12,13,13,15,15, 5, 7, 7, 9,10, 7,
         8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,12,10,10,
        11,12,12, 6, 8, 8,10,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,13,13,10,10,11,11,13, 7, 8, 8,10,
        11, 8, 9, 9,11,11, 8, 9, 8,11,11,10,11,11,13,13,
        10,11,11,13,12, 9,10,10,13,12,10,11,11,14,13,10,
        10,11,13,13,12,13,13,15,15,12,11,13,12,14, 9,10,
        10,12,13,10,11,11,13,14,10,11,11,13,13,12,13,13,
        15,15,12,13,12,15,12, 8, 9, 9,12,12, 9,11,10,13,
        13, 9,10,10,13,13,12,13,13,15,15,12,12,12,14,14,
         9,10,10,13,13,10,11,11,13,14,10,11,11,14,12,13,
        13,14,14,16,12,13,13,15,14, 9,10,10,13,13,10,11,
        10,14,13,10,11,11,13,14,12,14,13,16,14,13,13,13,
        14,15,11,13,12,15,14,11,12,13,14,15,12,13,13,16,
        15,14,12,15,12,16,14,15,15,17,16,11,12,12,14,15,
        11,13,11,15,14,12,13,13,15,16,13,15,12,17,13,14,
        15,15,16,16, 8, 9, 9,12,12, 9,10,10,13,13, 9,10,
        10,13,13,12,13,12,14,14,12,13,13,15,15, 9,10,10,
        13,13,10,11,11,14,13,10,10,11,13,14,12,13,13,15,
        14,12,12,14,14,16, 9,10,10,13,13,10,11,11,13,14,
        10,11,11,14,13,13,13,13,15,15,13,14,13,16,14,11,
        12,12,14,14,12,13,13,16,15,11,12,13,14,15,14,15,
        15,16,16,14,13,15,13,17,11,12,12,14,15,12,13,13,
        15,16,11,13,12,15,15,14,15,14,16,16,14,15,12,17,
        13,
};

static const static_codebook _44u2__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u2__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u2__p4_0,
        0
};

static const long _vq_quantlist__44u2__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u2__p5_0[] = {
         1, 4, 4, 7, 7, 8, 8, 9, 9, 4, 6, 5, 8, 8, 8, 8,
        10,10, 4, 5, 6, 8, 8, 8, 8,10,10, 7, 8, 8, 9, 9,
         9, 9,11,11, 7, 8, 8, 9, 9, 9, 9,11,11, 8, 8, 8,
         9, 9,10,11,12,12, 8, 8, 8, 9, 9,10,10,12,12,10,
        10,10,11,11,12,12,13,13,10,10,10,11,11,12,12,13,
        13,
};

static const static_codebook _44u2__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u2__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u2__p5_0,
        0
};

static const long _vq_quantlist__44u2__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u2__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8,10,10,11,11,14,13, 4, 6, 5,
         8, 8, 9, 9,11,10,12,11,15,14, 4, 5, 6, 8, 8, 9,
         9,11,11,11,11,14,14, 6, 8, 8,10, 9,11,11,11,11,
        12,12,15,15, 6, 8, 8, 9, 9,11,11,11,12,12,12,15,
        15, 8,10,10,11,11,11,11,12,12,13,13,15,16, 8,10,
        10,11,11,11,11,12,12,13,13,16,16,10,11,11,12,12,
        12,12,13,13,13,13,17,16,10,11,11,12,12,12,12,13,
        13,13,14,16,17,11,12,12,13,13,13,13,14,14,15,14,
        18,17,11,12,12,13,13,13,13,14,14,14,15,19,18,14,
        15,15,15,15,16,16,18,19,18,18, 0, 0,14,15,15,16,
        15,17,17,16,18,17,18, 0, 0,
};

static const static_codebook _44u2__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u2__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u2__p6_0,
        0
};

static const long _vq_quantlist__44u2__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u2__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 6, 5, 4, 5, 5, 5, 6, 5,
         6, 5, 6, 6, 5, 5, 6, 6, 6,
};

static const static_codebook _44u2__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u2__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u2__p6_1,
        0
};

static const long _vq_quantlist__44u2__p7_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u2__p7_0[] = {
         1, 3, 2,12,12,12,12,12,12, 4,12,12,12,12,12,12,
        12,12, 5,12,12,12,12,12,12,12,12,12,12,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,
};

static const static_codebook _44u2__p7_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u2__p7_0,
        1, -516612096, 1626677248, 4, 0,
        (long *)_vq_quantlist__44u2__p7_0,
        0
};

static const long _vq_quantlist__44u2__p7_1[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u2__p7_1[] = {
         1, 4, 4, 7, 6, 7, 6, 8, 7, 9, 7, 9, 8, 4, 7, 6,
         8, 8, 9, 8,10, 9,10,10,11,11, 4, 7, 7, 8, 8, 8,
         8, 9,10,11,11,11,11, 6, 8, 8,10,10,10,10,11,11,
        12,12,12,12, 7, 8, 8,10,10,10,10,11,11,12,12,13,
        13, 7, 9, 9,11,10,12,12,13,13,14,13,14,14, 7, 9,
         9,10,11,11,12,13,13,13,13,16,14, 9,10,10,12,12,
        13,13,14,14,15,16,15,16, 9,10,10,12,12,12,13,14,
        14,14,15,16,15,10,12,12,13,13,15,13,16,16,15,17,
        17,17,10,11,11,12,14,14,14,15,15,17,17,15,17,11,
        12,12,14,14,14,15,15,15,17,16,17,17,10,12,12,13,
        14,14,14,17,15,17,17,17,17,
};

static const static_codebook _44u2__p7_1 = {
        2, 169,
        (char *)_vq_lengthlist__44u2__p7_1,
        1, -523010048, 1618608128, 4, 0,
        (long *)_vq_quantlist__44u2__p7_1,
        0
};

static const long _vq_quantlist__44u2__p7_2[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u2__p7_2[] = {
         2, 5, 5, 6, 6, 7, 7, 8, 7, 8, 8, 8, 8, 5, 6, 6,
         7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 5, 6, 6, 7, 7, 8,
         7, 8, 8, 8, 8, 8, 8, 6, 7, 7, 7, 8, 8, 8, 8, 8,
         9, 9, 9, 9, 6, 7, 7, 8, 7, 8, 8, 9, 9, 9, 9, 9,
         9, 7, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 7, 8,
         8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9,
};

static const static_codebook _44u2__p7_2 = {
        2, 169,
        (char *)_vq_lengthlist__44u2__p7_2,
        1, -531103744, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u2__p7_2,
        0
};

static const char _huff_lengthlist__44u2__short[] = {
        13,15,17,17,15,15,12,17,11, 9, 7,10,10, 9,12,17,
        10, 6, 3, 6, 5, 7,10,17,15,10, 6, 9, 8, 9,11,17,
        15, 8, 4, 7, 3, 5, 9,16,16,10, 5, 8, 4, 5, 8,16,
        13,11, 5, 8, 3, 3, 5,14,13,12, 7,10, 5, 5, 7,14,
};

static const static_codebook _huff_book__44u2__short = {
        2, 64,
        (char *)_huff_lengthlist__44u2__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u3__long[] = {
         6, 9,13,12,14,11,10,13, 8, 4, 5, 7, 8, 7, 8,12,
        11, 4, 3, 5, 5, 7, 9,14,11, 6, 5, 6, 6, 6, 7,13,
        13, 7, 5, 6, 4, 5, 7,14,11, 7, 6, 6, 5, 5, 6,13,
         9, 7, 8, 6, 7, 5, 3, 9, 9,12,13,12,14,10, 6, 7,
};

static const static_codebook _huff_book__44u3__long = {
        2, 64,
        (char *)_huff_lengthlist__44u3__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u3__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u3__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,10,11, 8,
        10,11, 5, 8, 8, 8,11,10, 8,11,11, 4, 8, 8, 8,11,
        11, 8,11,11, 8,11,11,11,13,14,11,14,14, 8,11,11,
        10,14,12,11,14,14, 4, 8, 8, 8,11,11, 8,11,11, 7,
        11,11,11,14,14,10,12,14, 8,11,11,11,14,14,11,14,
        13,
};

static const static_codebook _44u3__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u3__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u3__p1_0,
        0
};

static const long _vq_quantlist__44u3__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u3__p2_0[] = {
         2, 5, 4, 5, 6, 6, 5, 6, 6, 5, 6, 6, 7, 8, 8, 6,
         8, 8, 5, 6, 6, 6, 8, 8, 7, 8, 8, 5, 7, 6, 7, 8,
         8, 6, 8, 8, 7, 8, 8, 8, 9,10, 8,10,10, 6, 8, 8,
         8,10, 8, 8,10,10, 5, 6, 6, 6, 8, 8, 7, 8, 8, 6,
         8, 8, 8,10,10, 8, 8,10, 7, 8, 8, 8,10,10, 8,10,
         9,
};

static const static_codebook _44u3__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u3__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u3__p2_0,
        0
};

static const long _vq_quantlist__44u3__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u3__p3_0[] = {
         2, 4, 4, 7, 7, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9, 8,
         9, 9,12,12, 8, 9, 9,11,12, 5, 7, 7,10,10, 7, 9,
         9,11,11, 7, 9, 9,10,11,10,11,11,13,13, 9,10,11,
        13,13, 5, 7, 7,10,10, 7, 9, 9,11,10, 7, 9, 9,11,
        11, 9,11,10,13,13,10,11,11,14,13, 8,10,10,14,13,
        10,11,11,15,14, 9,11,11,14,14,13,14,13,16,16,12,
        13,13,15,15, 8,10,10,13,14, 9,11,11,14,14,10,11,
        11,14,15,12,13,13,15,15,13,14,14,15,16, 5, 7, 7,
        10,10, 7, 9, 9,11,11, 7, 9, 9,11,12,10,11,11,14,
        14,10,11,11,14,14, 7, 9, 9,12,12, 9,11,11,13,13,
         9,11,11,13,13,12,12,13,15,15,11,12,13,15,16, 7,
         9, 9,11,11, 8,11,10,13,12, 9,11,11,13,13,11,13,
        12,15,13,11,13,13,15,16, 9,12,11,15,14,11,12,13,
        16,15,11,13,13,15,16,14,14,15,17,16,13,15,16, 0,
        17, 9,11,11,15,15,10,13,12,15,15,11,13,13,15,16,
        13,15,13,16,15,14,16,15, 0,19, 5, 7, 7,10,10, 7,
         9, 9,11,11, 7, 9, 9,11,11,10,12,11,14,14,10,11,
        12,14,14, 7, 9, 9,12,12, 9,11,11,14,13, 9,10,11,
        12,13,11,13,13,16,16,11,12,13,13,16, 7, 9, 9,12,
        12, 9,11,11,13,13, 9,11,11,13,13,11,13,13,15,15,
        12,13,12,15,14, 9,11,11,15,14,11,13,12,16,16,10,
        12,12,15,15,13,15,15,17,19,13,14,15,16,17,10,12,
        12,15,15,11,13,13,16,16,11,13,13,15,16,13,15,15,
         0, 0,14,15,15,16,16, 8,10,10,14,14,10,12,12,15,
        15,10,12,11,15,16,14,15,15,19,20,13,14,14,18,16,
         9,11,11,15,15,11,13,13,17,16,11,13,13,16,16,15,
        17,17,20,20,14,15,16,17,20, 9,11,11,15,15,10,13,
        12,16,15,11,13,13,15,17,14,16,15,18, 0,14,16,15,
        18,20,12,14,14, 0, 0,14,14,16, 0, 0,13,16,15, 0,
         0,17,17,18, 0, 0,16,17,19,19, 0,12,14,14,18, 0,
        12,16,14, 0,17,13,15,15,18, 0,16,18,17, 0,17,16,
        18,17, 0, 0, 7,10,10,14,14,10,12,11,15,15,10,12,
        12,16,15,13,15,15,18, 0,14,15,15,17, 0, 9,11,11,
        15,15,11,13,13,16,16,11,12,13,16,16,14,15,16,17,
        17,14,16,16,16,18, 9,11,12,16,16,11,13,13,17,17,
        11,14,13,20,17,15,16,16,19, 0,15,16,17, 0,19,11,
        13,14,17,16,14,15,15,20,18,13,14,15,17,19,16,18,
        18, 0,20,16,16,19,17, 0,12,15,14,17, 0,14,15,15,
        18,19,13,16,15,19,20,15,18,18, 0,20,17, 0,16, 0,
         0,
};

static const static_codebook _44u3__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u3__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u3__p3_0,
        0
};

static const long _vq_quantlist__44u3__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u3__p4_0[] = {
         4, 5, 5, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 9,
         9, 9,11,11, 9, 9, 9,11,11, 5, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 7, 8,10,10, 9,10,10,11,12, 9,10,10,
        11,12, 5, 7, 7, 9, 9, 7, 8, 7,10,10, 7, 8, 8,10,
        10, 9,10, 9,12,11, 9,10,10,12,11, 9,10, 9,12,12,
         9,10,10,13,12, 9,10,10,12,13,12,12,12,14,14,11,
        12,12,13,14, 9, 9,10,12,12, 9,10,10,12,12, 9,10,
        10,12,13,11,12,11,14,13,12,12,12,14,13, 5, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,
        12, 9,10,10,12,12, 7, 8, 8,11,10, 8, 8, 9,11,11,
         8, 9, 9,11,11,11,11,11,12,13,10,11,11,13,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,13,11,10,11,11,13,13, 9,11,10,13,12,10,11,11,
        13,13,10,11,11,13,13,12,12,13,12,15,12,13,13,15,
        15, 9,10,10,12,13,10,11,10,13,12,10,11,11,13,14,
        12,13,11,15,13,12,13,13,15,15, 5, 7, 7, 9, 9, 7,
         8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,12,10,10,
        11,12,12, 6, 8, 8,10,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,13,13,10,10,11,11,13, 7, 8, 8,10,
        10, 8, 9, 9,11,11, 8, 9, 9,11,11,10,11,11,13,13,
        11,11,11,13,12, 9,10,10,13,12,10,11,11,14,13,10,
        10,11,12,13,12,13,13,15,15,12,11,13,13,14, 9,10,
        11,12,13,10,11,11,13,13,10,11,11,13,13,12,13,13,
        15,15,12,13,12,15,12, 8, 9, 9,12,12, 9,11,10,13,
        13, 9,10,10,13,13,12,13,13,15,14,12,12,12,14,13,
         9,10,10,13,12,10,11,11,13,13,10,11,11,14,12,13,
        13,14,14,16,12,13,13,15,15, 9,10,10,13,13,10,11,
        10,14,13,10,11,11,13,14,12,14,13,15,14,13,13,13,
        15,15,11,13,12,15,14,11,12,13,14,15,12,13,13,16,
        14,14,12,15,12,16,14,15,15,17,15,11,12,12,14,14,
        11,13,11,15,14,12,13,13,15,15,13,15,12,17,13,14,
        15,15,16,16, 8, 9, 9,12,12, 9,10,10,12,13, 9,10,
        10,13,13,12,12,12,14,14,12,13,13,15,15, 9,10,10,
        13,12,10,11,11,14,13,10,10,11,13,14,12,13,13,15,
        15,12,12,13,14,16, 9,10,10,13,13,10,11,11,13,14,
        10,11,11,14,13,12,13,13,14,15,13,14,13,16,14,11,
        12,12,14,14,12,13,13,15,14,11,12,13,14,15,14,15,
        15,16,16,13,13,15,13,16,11,12,12,14,15,12,13,13,
        14,15,11,13,12,15,14,14,15,15,16,16,14,15,12,16,
        13,
};

static const static_codebook _44u3__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u3__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u3__p4_0,
        0
};

static const long _vq_quantlist__44u3__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u3__p5_0[] = {
         2, 3, 3, 6, 6, 7, 7, 9, 9, 4, 5, 5, 7, 7, 8, 8,
        10,10, 4, 5, 5, 7, 7, 8, 8,10,10, 6, 7, 7, 8, 8,
         9, 9,11,10, 6, 7, 7, 8, 8, 9, 9,10,10, 7, 8, 8,
         9, 9,10,10,11,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
        10,10,11,10,11,11,12,12, 9,10,10,10,10,11,11,12,
        12,
};

static const static_codebook _44u3__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u3__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u3__p5_0,
        0
};

static const long _vq_quantlist__44u3__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u3__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8, 9, 9,10,11,13,14, 4, 6, 5,
         8, 8, 9, 9,10,10,11,11,14,14, 4, 6, 6, 8, 8, 9,
         9,10,10,11,11,14,14, 6, 8, 8, 9, 9,10,10,11,11,
        12,12,15,15, 6, 8, 8, 9, 9,10,11,11,11,12,12,15,
        15, 8, 9, 9,11,10,11,11,12,12,13,13,15,16, 8, 9,
         9,10,11,11,11,12,12,13,13,16,16,10,10,11,11,11,
        12,12,13,13,13,14,17,16, 9,10,11,12,11,12,12,13,
        13,13,13,16,18,11,12,11,12,12,13,13,13,14,15,14,
        17,17,11,11,12,12,12,13,13,13,14,14,15,18,17,14,
        15,15,15,15,16,16,17,17,19,18, 0,20,14,15,14,15,
        15,16,16,16,17,18,16,20,18,
};

static const static_codebook _44u3__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u3__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u3__p6_0,
        0
};

static const long _vq_quantlist__44u3__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u3__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 6, 5, 4, 5, 5, 5, 6, 5,
         6, 5, 6, 6, 5, 5, 6, 6, 6,
};

static const static_codebook _44u3__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u3__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u3__p6_1,
        0
};

static const long _vq_quantlist__44u3__p7_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u3__p7_0[] = {
         1, 3, 3,10,10,10,10,10,10, 4,10,10,10,10,10,10,
        10,10, 4,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9,
};

static const static_codebook _44u3__p7_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u3__p7_0,
        1, -515907584, 1627381760, 4, 0,
        (long *)_vq_quantlist__44u3__p7_0,
        0
};

static const long _vq_quantlist__44u3__p7_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u3__p7_1[] = {
         1, 4, 4, 6, 6, 7, 6, 8, 7, 9, 8,10, 9,11,11, 4,
         7, 7, 8, 7, 9, 9,10,10,11,11,11,11,12,12, 4, 7,
         7, 7, 7, 9, 9,10,10,11,11,12,12,12,11, 6, 8, 8,
         9, 9,10,10,11,11,12,12,13,12,13,13, 6, 8, 8, 9,
         9,10,11,11,11,12,12,13,14,13,13, 8, 9, 9,11,11,
        12,12,12,13,14,13,14,14,14,15, 8, 9, 9,11,11,11,
        12,13,14,13,14,15,17,14,15, 9,10,10,12,12,13,13,
        13,14,15,15,15,16,16,16, 9,11,11,12,12,13,13,14,
        14,14,15,16,16,16,16,10,12,12,13,13,14,14,15,15,
        15,16,17,17,17,17,10,12,11,13,13,15,14,15,14,16,
        17,16,16,16,16,11,13,12,14,14,14,14,15,16,17,16,
        17,17,17,17,11,13,12,14,14,14,15,17,16,17,17,17,
        17,17,17,12,13,13,15,16,15,16,17,17,16,16,17,17,
        17,17,12,13,13,15,15,15,16,17,17,17,16,17,16,17,
        17,
};

static const static_codebook _44u3__p7_1 = {
        2, 225,
        (char *)_vq_lengthlist__44u3__p7_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__44u3__p7_1,
        0
};

static const long _vq_quantlist__44u3__p7_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u3__p7_2[] = {
         2, 5, 5, 7, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
        10,10, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 8, 9, 9, 9,
         9,10, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
        10,10,10,10, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9,10,
         9,10,10,10,10, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
        10,10,10,10,10,10, 7, 8, 8, 9, 8, 9, 9, 9, 9,10,
         9,10,10,10,10,10,10, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9,10,10,10,10,10,10,10, 8, 9, 8, 9, 9, 9, 9,10,
         9,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9,10,
         9,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,
         9,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,10, 9,
        10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,10,10,10,10,11, 9,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,11, 9,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
         9,10,10,10,10,10,10,10,10,10,10,10,11,11,11,10,
        11,
};

static const static_codebook _44u3__p7_2 = {
        2, 289,
        (char *)_vq_lengthlist__44u3__p7_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u3__p7_2,
        0
};

static const char _huff_lengthlist__44u3__short[] = {
        14,14,14,15,13,15,12,16,10, 8, 7, 9, 9, 8,12,16,
        10, 5, 4, 6, 5, 6, 9,16,14, 8, 6, 8, 7, 8,10,16,
        14, 7, 4, 6, 3, 5, 8,16,15, 9, 5, 7, 4, 4, 7,16,
        13,10, 6, 7, 4, 3, 4,13,13,12, 7, 9, 5, 5, 6,12,
};

static const static_codebook _huff_book__44u3__short = {
        2, 64,
        (char *)_huff_lengthlist__44u3__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u4__long[] = {
         3, 8,12,12,13,12,11,13, 5, 4, 6, 7, 8, 8, 9,13,
         9, 5, 4, 5, 5, 7, 9,13, 9, 6, 5, 6, 6, 7, 8,12,
        12, 7, 5, 6, 4, 5, 8,13,11, 7, 6, 6, 5, 5, 6,12,
        10, 8, 8, 7, 7, 5, 3, 8,10,12,13,12,12, 9, 6, 7,
};

static const static_codebook _huff_book__44u4__long = {
        2, 64,
        (char *)_huff_lengthlist__44u4__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u4__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u4__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,10,11, 8,
        10,11, 5, 8, 8, 8,11,10, 8,11,11, 4, 8, 8, 8,11,
        11, 8,11,11, 8,11,11,11,13,14,11,15,14, 8,11,11,
        10,13,12,11,14,14, 4, 8, 8, 8,11,11, 8,11,11, 7,
        11,11,11,15,14,10,12,14, 8,11,11,11,14,14,11,14,
        13,
};

static const static_codebook _44u4__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u4__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u4__p1_0,
        0
};

static const long _vq_quantlist__44u4__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u4__p2_0[] = {
         2, 5, 5, 5, 6, 6, 5, 6, 6, 5, 6, 6, 7, 8, 8, 6,
         8, 8, 5, 6, 6, 6, 8, 8, 7, 8, 8, 5, 7, 6, 6, 8,
         8, 6, 8, 8, 6, 8, 8, 8, 9,10, 8,10,10, 6, 8, 8,
         8,10, 8, 8,10,10, 5, 6, 6, 6, 8, 8, 6, 8, 8, 6,
         8, 8, 8,10,10, 8, 8,10, 6, 8, 8, 8,10,10, 8,10,
         9,
};

static const static_codebook _44u4__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u4__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u4__p2_0,
        0
};

static const long _vq_quantlist__44u4__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u4__p3_0[] = {
         2, 4, 4, 8, 8, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9, 8,
        10, 9,12,12, 8, 9,10,12,12, 5, 7, 7,10,10, 7, 9,
         9,11,11, 7, 9, 9,11,11,10,12,11,14,14, 9,10,11,
        13,14, 5, 7, 7,10,10, 7, 9, 9,11,11, 7, 9, 9,11,
        11, 9,11,10,14,13,10,11,11,14,14, 8,10,10,14,13,
        10,12,12,15,14, 9,11,11,15,14,13,14,14,17,17,12,
        14,14,16,16, 8,10,10,14,14, 9,11,11,14,15,10,12,
        12,14,15,12,14,13,16,16,13,14,15,15,18, 4, 7, 7,
        10,10, 7, 9, 9,12,11, 7, 9, 9,11,12,10,12,11,15,
        14,10,11,12,14,15, 7, 9, 9,12,12, 9,11,12,13,13,
         9,11,12,13,13,12,13,13,15,16,11,13,13,15,16, 7,
         9, 9,12,12, 9,11,10,13,12, 9,11,12,13,14,11,13,
        12,16,14,12,13,13,15,16,10,12,12,16,15,11,13,13,
        17,16,11,13,13,17,16,14,15,15,17,17,14,16,16,18,
        20, 9,11,11,15,16,11,13,12,16,16,11,13,13,16,17,
        14,15,14,18,16,14,16,16,17,20, 5, 7, 7,10,10, 7,
         9, 9,12,11, 7, 9,10,11,12,10,12,11,15,15,10,12,
        12,14,14, 7, 9, 9,12,12, 9,12,11,14,13, 9,10,11,
        12,13,12,13,14,16,16,11,12,13,14,16, 7, 9, 9,12,
        12, 9,12,11,13,13, 9,12,11,13,13,11,13,13,16,16,
        12,13,13,16,15, 9,11,11,16,14,11,13,13,16,16,11,
        12,13,16,16,14,16,16,17,17,13,14,15,16,17,10,12,
        12,15,15,11,13,13,16,17,11,13,13,16,16,14,16,15,
        19,19,14,15,15,17,18, 8,10,10,14,14,10,12,12,15,
        15,10,12,12,16,16,14,16,15,20,19,13,15,15,17,16,
         9,12,12,16,16,11,13,13,16,18,11,14,13,16,17,16,
        17,16,20, 0,15,16,18,18,20, 9,11,11,15,15,11,14,
        12,17,16,11,13,13,17,17,15,17,15,20,20,14,16,16,
        17, 0,13,15,14,18,16,14,15,16, 0,18,14,16,16, 0,
         0,18,16, 0, 0,20,16,18,18, 0, 0,12,14,14,17,18,
        13,15,14,20,18,14,16,15,19,19,16,20,16, 0,18,16,
        19,17,19, 0, 8,10,10,14,14,10,12,12,16,15,10,12,
        12,16,16,13,15,15,18,17,14,16,16,19, 0, 9,11,11,
        16,15,11,14,13,18,17,11,12,13,17,18,14,17,16,18,
        18,15,16,17,18,18, 9,12,12,16,16,11,13,13,16,18,
        11,14,13,17,17,15,16,16,18,20,16,17,17,20,20,12,
        14,14,18,17,14,16,16, 0,19,13,14,15,18, 0,16, 0,
         0, 0, 0,16,16, 0,19,20,13,15,14, 0, 0,14,16,16,
        18,19,14,16,15, 0,20,16,20,18, 0,20,17,20,17, 0,
         0,
};

static const static_codebook _44u4__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u4__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u4__p3_0,
        0
};

static const long _vq_quantlist__44u4__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u4__p4_0[] = {
         4, 5, 5, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 9,
         9, 9,11,11, 8, 9, 9,11,11, 5, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 7, 8,10,10, 9,10,10,11,12, 9,10,10,
        11,12, 5, 7, 7, 9, 9, 7, 8, 7,10,10, 7, 8, 8,10,
        10, 9,10,10,12,11, 9,10,10,12,11, 9,10, 9,12,12,
         9,10,10,13,12, 9,10,10,12,12,12,12,12,14,14,11,
        12,12,13,14, 9, 9,10,12,12, 9,10,10,13,13, 9,10,
        10,12,13,11,12,12,14,13,11,12,12,14,14, 5, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10,10,10,10,12,
        12, 9,10,10,12,12, 7, 8, 8,11,10, 8, 8, 9,11,11,
         8, 9, 9,11,11,11,11,11,12,13,10,11,11,13,13, 6,
         8, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,13,11,10,11,11,13,13, 9,11,10,13,12,10,11,11,
        13,14,10,11,11,14,13,12,12,13,12,15,12,13,13,15,
        15, 9,10,10,12,13,10,11,10,13,12,10,11,11,13,14,
        12,13,11,15,13,13,13,13,15,15, 5, 7, 7, 9, 9, 7,
         8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,12,10,10,
        11,12,13, 6, 8, 8,10,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,13,13,10,10,11,11,13, 7, 8, 8,10,
        11, 8, 9, 9,11,11, 8, 9, 8,11,11,10,11,11,13,13,
        11,12,11,13,12, 9,10,10,13,12,10,11,11,14,13,10,
        10,11,12,13,12,13,13,15,15,12,11,13,13,14, 9,10,
        11,12,13,10,11,11,13,14,10,11,11,13,13,12,13,13,
        15,15,12,13,12,15,12, 8, 9, 9,12,12, 9,11,10,13,
        13, 9,10,10,13,13,12,13,13,15,15,12,12,12,14,14,
         9,10,10,13,13,10,11,11,13,14,10,11,11,14,13,13,
        13,14,14,16,13,13,13,15,15, 9,10,10,13,13,10,11,
        10,14,13,10,11,11,13,14,12,14,13,16,14,12,13,13,
        14,15,11,12,12,15,14,11,12,13,14,15,12,13,13,16,
        15,14,12,15,12,16,14,15,15,16,16,11,12,12,14,14,
        11,13,12,15,14,12,13,13,15,16,13,15,13,17,13,14,
        15,15,16,17, 8, 9, 9,12,12, 9,10,10,12,13, 9,10,
        10,13,13,12,12,12,14,14,12,13,13,15,15, 9,10,10,
        13,12,10,11,11,14,13,10,10,11,13,14,13,13,13,15,
        15,12,13,14,14,16, 9,10,10,13,13,10,11,11,13,14,
        10,11,11,14,14,13,13,13,15,15,13,14,13,16,14,11,
        12,12,15,14,12,13,13,16,15,11,12,13,14,15,14,15,
        15,17,16,13,13,15,13,16,11,12,13,14,15,13,13,13,
        15,16,11,13,12,15,14,14,15,15,16,16,14,15,12,17,
        13,
};

static const static_codebook _44u4__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u4__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u4__p4_0,
        0
};

static const long _vq_quantlist__44u4__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u4__p5_0[] = {
         2, 3, 3, 6, 6, 7, 7, 9, 9, 4, 5, 5, 7, 7, 8, 8,
        10, 9, 4, 5, 5, 7, 7, 8, 8,10,10, 6, 7, 7, 8, 8,
         9, 9,11,10, 6, 7, 7, 8, 8, 9, 9,10,11, 7, 8, 8,
         9, 9,10,10,11,11, 7, 8, 8, 9, 9,10,10,11,11, 9,
        10,10,11,10,11,11,12,12, 9,10,10,10,11,11,11,12,
        12,
};

static const static_codebook _44u4__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u4__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u4__p5_0,
        0
};

static const long _vq_quantlist__44u4__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u4__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8, 9, 9,11,10,13,13, 4, 6, 5,
         8, 8, 9, 9,10,10,11,11,14,14, 4, 6, 6, 8, 8, 9,
         9,10,10,11,11,14,14, 6, 8, 8, 9, 9,10,10,11,11,
        12,12,15,15, 6, 8, 8, 9, 9,10,11,11,11,12,12,15,
        15, 8, 9, 9,11,10,11,11,12,12,13,13,16,16, 8, 9,
         9,10,10,11,11,12,12,13,13,16,16,10,10,10,12,11,
        12,12,13,13,14,14,16,16,10,10,10,11,12,12,12,13,
        13,13,14,16,17,11,12,11,12,12,13,13,14,14,15,14,
        18,17,11,11,12,12,12,13,13,14,14,14,15,19,18,14,
        15,14,15,15,17,16,17,17,17,17,21, 0,14,15,15,16,
        16,16,16,17,17,18,17,20,21,
};

static const static_codebook _44u4__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u4__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u4__p6_0,
        0
};

static const long _vq_quantlist__44u4__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u4__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 6, 5, 4, 5, 5, 5, 6, 5,
         6, 5, 6, 6, 5, 5, 6, 6, 6,
};

static const static_codebook _44u4__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u4__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u4__p6_1,
        0
};

static const long _vq_quantlist__44u4__p7_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u4__p7_0[] = {
         1, 3, 3,12,12,12,12,12,12,12,12,12,12, 3,12,11,
        12,12,12,12,12,12,12,12,12,12, 4,11,10,12,12,12,
        12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
        12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
        12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
        12,12,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,
};

static const static_codebook _44u4__p7_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u4__p7_0,
        1, -514332672, 1627381760, 4, 0,
        (long *)_vq_quantlist__44u4__p7_0,
        0
};

static const long _vq_quantlist__44u4__p7_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u4__p7_1[] = {
         1, 4, 4, 6, 6, 7, 7, 9, 8,10, 8,10, 9,11,11, 4,
         7, 6, 8, 7, 9, 9,10,10,11,10,11,10,12,10, 4, 6,
         7, 8, 8, 9, 9,10,10,11,11,11,11,12,12, 6, 8, 8,
        10, 9,11,10,12,11,12,12,12,12,13,13, 6, 8, 8,10,
        10,10,11,11,11,12,12,13,12,13,13, 8, 9, 9,11,11,
        12,11,12,12,13,13,13,13,13,13, 8, 9, 9,11,11,11,
        12,12,12,13,13,13,13,13,13, 9,10,10,12,11,13,13,
        13,13,14,13,13,14,14,14, 9,10,11,11,12,12,13,13,
        13,13,13,14,15,14,14,10,11,11,12,12,13,13,14,14,
        14,14,14,15,16,16,10,11,11,12,13,13,13,13,15,14,
        14,15,16,15,16,10,12,12,13,13,14,14,14,15,15,15,
        15,15,15,16,11,12,12,13,13,14,14,14,15,15,15,16,
        15,17,16,11,12,12,13,13,13,15,15,14,16,16,16,16,
        16,17,11,12,12,13,13,14,14,15,14,15,15,17,17,16,
        16,
};

static const static_codebook _44u4__p7_1 = {
        2, 225,
        (char *)_vq_lengthlist__44u4__p7_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__44u4__p7_1,
        0
};

static const long _vq_quantlist__44u4__p7_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u4__p7_2[] = {
         2, 5, 5, 7, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
        10,10,10,10, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9,10,
         9,10, 9,10,10, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
        10,10,10,10,10,10, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9,10,10,10,10,10,10, 8, 9, 8, 9, 9, 9, 9, 9, 9,
        10,10,10,10,10,10,10,10, 8, 8, 8, 9, 9, 9, 9, 9,
        10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,10,
        10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,10,
        10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,11,10,10,10, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9, 9,
         9,10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9,
        10, 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
         9,10, 9,10,10,10,10,10,10,10,10,10,10,11,10,10,
        10,
};

static const static_codebook _44u4__p7_2 = {
        2, 289,
        (char *)_vq_lengthlist__44u4__p7_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u4__p7_2,
        0
};

static const char _huff_lengthlist__44u4__short[] = {
        14,17,15,17,16,14,13,16,10, 7, 7,10,13,10,15,16,
         9, 4, 4, 6, 5, 7, 9,16,12, 8, 7, 8, 8, 8,11,16,
        14, 7, 4, 6, 3, 5, 8,15,13, 8, 5, 7, 4, 5, 7,16,
        12, 9, 6, 8, 3, 3, 5,16,14,13, 7,10, 5, 5, 7,15,
};

static const static_codebook _huff_book__44u4__short = {
        2, 64,
        (char *)_huff_lengthlist__44u4__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u5__long[] = {
         3, 8,13,12,14,12,16,11,13,14, 5, 4, 5, 6, 7, 8,
        10, 9,12,15,10, 5, 5, 5, 6, 8, 9, 9,13,15,10, 5,
         5, 6, 6, 7, 8, 8,11,13,12, 7, 5, 6, 4, 6, 7, 7,
        11,14,11, 7, 7, 6, 6, 6, 7, 6,10,14,14, 9, 8, 8,
         6, 7, 7, 7,11,16,11, 8, 8, 7, 6, 6, 7, 4, 7,12,
        10,10,12,10,10, 9,10, 5, 6, 9,10,12,15,13,14,14,
        14, 8, 7, 8,
};

static const static_codebook _huff_book__44u5__long = {
        2, 100,
        (char *)_huff_lengthlist__44u5__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u5__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u5__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 7, 5, 8, 8, 8,10,10, 7,
         9,10, 5, 8, 8, 7,10, 9, 8,10,10, 5, 8, 8, 8,10,
        10, 8,10,10, 8,10,10,10,12,13,10,13,13, 7,10,10,
        10,13,11,10,13,13, 4, 8, 8, 8,11,10, 8,10,10, 7,
        10,10,10,13,13,10,11,13, 8,10,11,10,13,13,10,13,
        12,
};

static const static_codebook _44u5__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u5__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u5__p1_0,
        0
};

static const long _vq_quantlist__44u5__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u5__p2_0[] = {
         3, 4, 4, 5, 6, 6, 5, 6, 6, 5, 6, 6, 6, 8, 8, 6,
         7, 8, 5, 6, 6, 6, 8, 7, 6, 8, 8, 5, 6, 6, 6, 8,
         8, 6, 8, 8, 6, 8, 8, 8, 9, 9, 8, 9, 9, 6, 8, 7,
         7, 9, 8, 8, 9, 9, 5, 6, 6, 6, 8, 7, 6, 8, 8, 6,
         8, 7, 8, 9, 9, 7, 8, 9, 6, 8, 8, 8, 9, 9, 8, 9,
         9,
};

static const static_codebook _44u5__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u5__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u5__p2_0,
        0
};

static const long _vq_quantlist__44u5__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u5__p3_0[] = {
         2, 4, 5, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 8,
        10, 9,13,12, 8, 9,10,12,12, 5, 7, 7,10,10, 7, 9,
         9,11,11, 6, 8, 9,11,11,10,11,11,14,14, 9,10,11,
        13,14, 5, 7, 7, 9,10, 7, 9, 8,11,11, 7, 9, 9,11,
        11, 9,11,10,14,13,10,11,11,14,14, 8,10,10,13,13,
        10,11,11,15,14, 9,11,11,14,14,13,14,14,17,16,12,
        13,13,15,16, 8,10,10,13,13, 9,11,11,14,15,10,11,
        11,14,15,12,14,13,16,16,13,15,14,15,17, 5, 7, 7,
        10,10, 7, 9, 9,11,11, 7, 9, 9,11,11,10,11,11,14,
        14,10,11,12,14,14, 7, 9, 9,12,11, 9,11,11,13,13,
         9,11,11,13,13,12,13,13,15,16,11,12,13,15,16, 6,
         9, 9,11,11, 8,11,10,13,12, 9,11,11,13,14,11,13,
        12,16,14,11,13,13,16,17,10,12,11,15,15,11,13,13,
        16,16,11,13,13,17,16,14,15,15,17,17,14,16,16,17,
        18, 9,11,11,14,15,10,12,12,15,15,11,13,13,16,17,
        13,15,13,17,15,14,15,16,18, 0, 5, 7, 7,10,10, 7,
         9, 9,11,11, 7, 9, 9,11,11,10,11,11,14,14,10,11,
        12,14,15, 6, 9, 9,12,11, 9,11,11,13,13, 8,10,11,
        12,13,11,13,13,16,15,11,12,13,14,15, 7, 9, 9,11,
        12, 9,11,11,13,13, 9,11,11,13,13,11,13,13,15,16,
        11,13,13,15,14, 9,11,11,15,14,11,13,13,17,15,10,
        12,12,15,15,14,16,16,17,17,13,13,15,15,17,10,11,
        12,15,15,11,13,13,16,16,11,13,13,15,15,14,15,15,
        18,18,14,15,15,17,17, 8,10,10,13,13,10,12,11,15,
        15,10,11,12,15,15,14,15,15,18,18,13,14,14,18,18,
         9,11,11,15,16,11,13,13,17,17,11,13,13,16,16,15,
        15,16,17, 0,14,15,17, 0, 0, 9,11,11,15,15,10,13,
        12,18,16,11,13,13,15,16,14,16,15,20,20,14,15,16,
        17, 0,13,14,14,20,16,14,15,16,19,18,14,15,15,19,
         0,18,16, 0,20,20,16,18,18, 0, 0,12,14,14,18,18,
        13,15,14,18,16,14,15,16,18,20,16,19,16, 0,17,17,
        18,18,19, 0, 8,10,10,14,14,10,11,11,14,15,10,11,
        12,15,15,13,15,14,19,17,13,15,15,17, 0, 9,11,11,
        16,15,11,13,13,16,16,10,12,13,15,17,14,16,16,18,
        18,14,15,15,18, 0, 9,11,11,15,15,11,13,13,16,17,
        11,13,13,18,17,14,18,16,18,18,15,17,17,18, 0,12,
        14,14,18,18,14,15,15,20, 0,13,14,15,17, 0,16,18,
        17, 0, 0,16,16, 0,17,20,12,14,14,18,18,14,16,15,
         0,18,14,16,15,18, 0,16,19,17, 0, 0,17,18,16, 0,
         0,
};

static const static_codebook _44u5__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u5__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u5__p3_0,
        0
};

static const long _vq_quantlist__44u5__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u5__p4_0[] = {
         4, 5, 5, 8, 8, 6, 7, 6, 9, 9, 6, 6, 7, 9, 9, 8,
         9, 9,11,11, 8, 9, 9,11,11, 6, 7, 7, 9, 9, 7, 8,
         8,10,10, 6, 7, 8, 9,10, 9,10,10,11,12, 9, 9,10,
        11,12, 6, 7, 7, 9, 9, 6, 8, 7,10, 9, 7, 8, 8,10,
        10, 9,10, 9,12,11, 9,10,10,12,11, 8, 9, 9,12,11,
         9,10,10,12,12, 9,10,10,12,12,11,12,12,13,14,11,
        11,12,13,14, 8, 9, 9,11,12, 9,10,10,12,12, 9,10,
        10,12,12,11,12,11,14,13,11,12,12,13,13, 5, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,
        12, 9,10,10,12,12, 7, 8, 8,10,10, 8, 8, 9,10,11,
         8, 9, 9,11,11,10,10,11,11,13,10,11,11,12,13, 6,
         7, 8,10,10, 7, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,13,11,10,11,11,12,12, 9,10,10,12,12,10,10,11,
        12,13,10,11,11,13,13,12,11,13,12,15,12,13,13,14,
        15, 9,10,10,12,12, 9,11,10,13,12,10,11,11,13,13,
        11,13,11,14,12,12,13,13,14,15, 5, 7, 7, 9, 9, 7,
         8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,12, 9,10,
        10,12,12, 6, 8, 7,10,10, 8, 9, 9,11,11, 7, 8, 9,
        10,11,10,11,11,12,12,10,10,11,11,13, 7, 8, 8,10,
        10, 8, 9, 9,11,11, 8, 9, 8,11,10,10,11,11,13,12,
        10,11,10,13,11, 9,10,10,12,12,10,11,11,13,12, 9,
        10,10,12,13,12,13,13,14,15,11,11,13,12,14, 9,10,
        10,12,12,10,11,11,13,13,10,11,10,13,12,12,13,13,
        14,14,12,13,11,14,12, 8, 9, 9,12,12, 9,10,10,12,
        12, 9,10,10,12,12,12,12,12,14,14,11,12,12,14,13,
         9,10,10,12,12,10,11,11,13,13,10,11,11,13,12,12,
        12,13,14,15,12,13,13,15,14, 9,10,10,12,12,10,11,
        10,13,12,10,11,11,12,13,12,13,12,15,13,12,13,13,
        14,15,11,12,12,14,13,11,12,12,14,15,12,13,13,15,
        14,13,12,14,12,16,13,14,14,15,15,11,11,12,14,14,
        11,12,11,14,13,12,13,13,14,15,13,14,12,16,12,14,
        14,15,16,16, 8, 9, 9,11,12, 9,10,10,12,12, 9,10,
        10,12,13,11,12,12,13,13,12,12,13,14,14, 9,10,10,
        12,12,10,11,10,13,12,10,10,11,12,13,12,13,13,15,
        14,12,12,13,13,15, 9,10,10,12,13,10,11,11,12,13,
        10,11,11,13,13,12,13,13,14,15,12,13,12,15,14,11,
        12,11,14,13,12,13,13,15,14,11,11,12,13,14,14,15,
        14,16,15,13,12,14,13,16,11,12,12,13,14,12,13,13,
        14,15,11,12,11,14,14,14,14,14,15,16,13,15,12,16,
        12,
};

static const static_codebook _44u5__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u5__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u5__p4_0,
        0
};

static const long _vq_quantlist__44u5__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u5__p5_0[] = {
         2, 3, 3, 6, 6, 8, 8,10,10, 4, 5, 5, 8, 7, 8, 8,
        11,10, 3, 5, 5, 7, 8, 8, 8,10,11, 6, 8, 7,10, 9,
        10,10,11,11, 6, 7, 8, 9, 9, 9,10,11,12, 8, 8, 8,
        10,10,11,11,13,12, 8, 8, 9, 9,10,11,11,12,13,10,
        11,10,12,11,13,12,14,14,10,10,11,11,12,12,13,14,
        14,
};

static const static_codebook _44u5__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u5__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u5__p5_0,
        0
};

static const long _vq_quantlist__44u5__p6_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u5__p6_0[] = {
         3, 4, 4, 5, 5, 7, 7, 9, 9, 4, 5, 4, 6, 6, 7, 7,
         9, 9, 4, 4, 5, 6, 6, 7, 7, 9, 9, 5, 6, 6, 7, 7,
         8, 8,10,10, 6, 6, 6, 7, 7, 8, 8,10,10, 7, 7, 7,
         8, 8, 9, 9,11,10, 7, 7, 7, 8, 8, 9, 9,10,11, 9,
         9, 9,10,10,11,10,11,11, 9, 9, 9,10,10,11,10,11,
        11,
};

static const static_codebook _44u5__p6_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u5__p6_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u5__p6_0,
        0
};

static const long _vq_quantlist__44u5__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u5__p7_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 9, 9, 8,11,10, 7,
        11,10, 5, 9, 9, 7,10,10, 8,10,11, 4, 9, 9, 9,12,
        12, 9,12,12, 8,12,12,11,12,12,10,12,13, 7,12,12,
        11,12,12,10,12,13, 4, 9, 9, 9,12,12, 9,12,12, 7,
        12,11,10,13,13,11,12,12, 7,12,12,10,13,13,11,12,
        12,
};

static const static_codebook _44u5__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u5__p7_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__44u5__p7_0,
        0
};

static const long _vq_quantlist__44u5__p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u5__p7_1[] = {
         2, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8, 4, 5, 5, 7, 7,
         8, 8, 9, 8, 8, 9, 4, 5, 5, 7, 7, 8, 8, 9, 9, 8,
         9, 6, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 6, 7, 7, 8,
         8, 9, 9, 9, 9, 9, 9, 7, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 7, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9,
         9, 9, 9, 9,10,10,10,10, 8, 9, 9, 9, 9, 9, 9,10,
        10,10,10, 8, 9, 9, 9, 9, 9, 9,10,10,10,10, 8, 9,
         9, 9, 9, 9, 9,10,10,10,10,
};

static const static_codebook _44u5__p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u5__p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u5__p7_1,
        0
};

static const long _vq_quantlist__44u5__p8_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u5__p8_0[] = {
         1, 4, 4, 6, 6, 8, 8, 9, 9,10,10, 4, 6, 6, 7, 7,
         9, 9,10,10,11,11, 4, 6, 6, 7, 7, 9, 9,10,10,11,
        11, 6, 8, 7, 9, 9,10,10,11,11,13,12, 6, 8, 8, 9,
         9,10,10,11,11,12,13, 8, 9, 9,10,10,12,12,13,12,
        14,13, 8, 9, 9,10,10,12,12,13,13,14,14, 9,11,11,
        12,12,13,13,14,14,15,14, 9,11,11,12,12,13,13,14,
        14,15,14,11,12,12,13,13,14,14,15,14,15,14,11,11,
        12,13,13,14,14,14,14,15,15,
};

static const static_codebook _44u5__p8_0 = {
        2, 121,
        (char *)_vq_lengthlist__44u5__p8_0,
        1, -524582912, 1618345984, 4, 0,
        (long *)_vq_quantlist__44u5__p8_0,
        0
};

static const long _vq_quantlist__44u5__p8_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u5__p8_1[] = {
         3, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 5, 6, 5, 7, 6,
         7, 7, 8, 8, 8, 8, 5, 5, 5, 6, 6, 7, 7, 8, 8, 8,
         8, 6, 7, 6, 7, 7, 8, 8, 8, 8, 8, 8, 6, 6, 7, 7,
         7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8,
         8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44u5__p8_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u5__p8_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u5__p8_1,
        0
};

static const long _vq_quantlist__44u5__p9_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u5__p9_0[] = {
         1, 3, 2,12,10,13,13,13,13,13,13,13,13, 4, 9, 9,
        13,13,13,13,13,13,13,13,13,13, 5,10, 9,13,13,13,
        13,13,13,13,13,13,13,12,13,13,13,13,13,13,13,13,
        13,13,13,13,11,13,13,13,13,13,13,13,13,13,13,13,
        13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
        13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
        13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
        13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
        13,13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,
        12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
        12,12,12,12,12,12,12,12,12,
};

static const static_codebook _44u5__p9_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u5__p9_0,
        1, -514332672, 1627381760, 4, 0,
        (long *)_vq_quantlist__44u5__p9_0,
        0
};

static const long _vq_quantlist__44u5__p9_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u5__p9_1[] = {
         1, 4, 4, 7, 7, 8, 8, 8, 7, 8, 7, 9, 8, 9, 9, 4,
         7, 6, 9, 8,10,10, 9, 8, 9, 9, 9, 9, 9, 8, 5, 6,
         6, 8, 9,10,10, 9, 9, 9,10,10,10,10,11, 7, 8, 8,
        10,10,11,11,10,10,11,11,11,12,11,11, 7, 8, 8,10,
        10,11,11,10,10,11,11,12,11,11,11, 8, 9, 9,11,11,
        12,12,11,11,12,11,12,12,12,12, 8, 9,10,11,11,12,
        12,11,11,12,12,12,12,12,12, 8, 9, 9,10,10,12,11,
        12,12,12,12,12,12,12,13, 8, 9, 9,11,11,11,11,12,
        12,12,12,13,12,13,13, 9,10,10,11,11,12,12,12,13,
        12,13,13,13,14,13, 9,10,10,11,11,12,12,12,13,13,
        12,13,13,14,13, 9,11,10,12,11,13,12,12,13,13,13,
        13,13,13,14, 9,10,10,12,12,12,12,12,13,13,13,13,
        13,14,14,10,11,11,12,12,12,13,13,13,14,14,13,14,
        14,14,10,11,11,12,12,12,12,13,12,13,14,13,14,14,
        14,
};

static const static_codebook _44u5__p9_1 = {
        2, 225,
        (char *)_vq_lengthlist__44u5__p9_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__44u5__p9_1,
        0
};

static const long _vq_quantlist__44u5__p9_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u5__p9_2[] = {
         2, 5, 5, 7, 7, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 5, 6, 6, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 5, 6, 6, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 7, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 7, 7, 7, 8, 8, 9, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
         9,10, 9,10,10,10, 8, 8, 8, 9, 8, 9, 9, 9, 9, 9,
         9, 9,10, 9,10, 9,10, 8, 9, 9, 9, 9, 9, 9, 9, 9,
         9,10, 9,10,10,10,10,10, 8, 9, 9, 9, 9, 9, 9,10,
         9,10, 9,10,10,10,10,10,10, 9, 9, 9, 9, 9,10, 9,
        10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9,
         9,10, 9,10, 9,10,10,10,10,10,10, 9, 9, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
         9, 9,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
         9,10,10, 9,10,10,10,10,10,10,10,10,10,10, 9, 9,
         9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10, 9,
         9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10,
         9, 9, 9,10, 9,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _44u5__p9_2 = {
        2, 289,
        (char *)_vq_lengthlist__44u5__p9_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u5__p9_2,
        0
};

static const char _huff_lengthlist__44u5__short[] = {
         4,10,17,13,17,13,17,17,17,17, 3, 6, 8, 9,11, 9,
        15,12,16,17, 6, 5, 5, 7, 7, 8,10,11,17,17, 7, 8,
         7, 9, 9,10,13,13,17,17, 8, 6, 5, 7, 4, 7, 5, 8,
        14,17, 9, 9, 8, 9, 7, 9, 8,10,16,17,12,10, 7, 8,
         4, 7, 4, 7,16,17,12,11, 9,10, 6, 9, 5, 7,14,17,
        14,13,10,15, 4, 8, 3, 5,14,17,17,14,11,15, 6,10,
         6, 8,15,17,
};

static const static_codebook _huff_book__44u5__short = {
        2, 100,
        (char *)_huff_lengthlist__44u5__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u6__long[] = {
         3, 9,14,13,14,13,16,12,13,14, 5, 4, 6, 6, 8, 9,
        11,10,12,15,10, 5, 5, 6, 6, 8,10,10,13,16,10, 6,
         6, 6, 6, 8, 9, 9,12,14,13, 7, 6, 6, 4, 6, 6, 7,
        11,14,10, 7, 7, 7, 6, 6, 6, 7,10,13,15,10, 9, 8,
         5, 6, 5, 6,10,14,10, 9, 8, 8, 6, 6, 5, 4, 6,11,
        11,11,12,11,10, 9, 9, 5, 5, 9,10,12,15,13,13,13,
        13, 8, 7, 7,
};

static const static_codebook _huff_book__44u6__long = {
        2, 100,
        (char *)_huff_lengthlist__44u6__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u6__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u6__p1_0[] = {
         1, 4, 4, 4, 8, 7, 5, 7, 7, 5, 8, 8, 8,10,10, 7,
         9,10, 5, 8, 8, 7,10, 9, 8,10,10, 5, 8, 8, 8,10,
        10, 8,10,10, 8,10,10,10,12,13,10,13,13, 7,10,10,
        10,13,11,10,13,13, 5, 8, 8, 8,11,10, 8,10,10, 7,
        10,10,10,13,13,10,11,13, 8,10,11,10,13,13,10,13,
        12,
};

static const static_codebook _44u6__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u6__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u6__p1_0,
        0
};

static const long _vq_quantlist__44u6__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u6__p2_0[] = {
         3, 4, 4, 5, 6, 6, 5, 6, 6, 5, 6, 6, 6, 8, 8, 6,
         7, 8, 5, 6, 6, 6, 8, 7, 6, 8, 8, 5, 6, 6, 6, 8,
         8, 6, 8, 8, 6, 8, 8, 8, 9, 9, 8, 9, 9, 6, 7, 7,
         7, 9, 8, 8, 9, 9, 5, 6, 6, 6, 8, 7, 6, 8, 8, 6,
         8, 8, 8, 9, 9, 7, 8, 9, 6, 8, 8, 8, 9, 9, 8, 9,
         9,
};

static const static_codebook _44u6__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u6__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u6__p2_0,
        0
};

static const long _vq_quantlist__44u6__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u6__p3_0[] = {
         2, 5, 4, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 8,
         9, 9,13,12, 8, 9,10,12,13, 5, 7, 7,10, 9, 7, 9,
         9,11,11, 7, 8, 9,11,11,10,11,11,14,14, 9,10,11,
        13,14, 5, 7, 7, 9,10, 6, 9, 8,11,11, 7, 9, 9,11,
        11, 9,11,10,14,13,10,11,11,14,13, 8,10,10,13,13,
        10,11,11,15,15, 9,11,11,14,14,13,14,14,17,16,12,
        13,14,16,16, 8,10,10,13,14, 9,11,11,14,15,10,11,
        12,14,15,12,14,13,16,15,13,14,14,15,17, 5, 7, 7,
        10,10, 7, 9, 9,11,11, 7, 9, 9,11,11,10,12,11,14,
        14,10,11,11,14,14, 7, 9, 9,12,11, 9,11,11,13,13,
         9,11,11,13,13,11,13,13,14,15,11,12,13,15,16, 6,
         9, 9,11,12, 8,11,10,13,12, 9,11,11,13,14,11,13,
        12,16,14,11,13,13,15,16,10,12,11,14,15,11,13,13,
        15,17,11,13,13,17,16,15,15,16,17,16,14,15,16,18,
         0, 9,11,11,14,15,10,12,12,16,15,11,13,13,16,16,
        13,15,14,18,15,14,16,16, 0, 0, 5, 7, 7,10,10, 7,
         9, 9,11,11, 7, 9, 9,11,11,10,11,11,14,14,10,11,
        12,14,14, 6, 9, 9,11,11, 9,11,11,13,13, 8,10,11,
        12,13,11,13,13,16,15,11,12,13,14,16, 7, 9, 9,11,
        12, 9,11,11,13,13, 9,11,11,13,13,11,13,13,16,15,
        11,13,12,15,15, 9,11,11,15,14,11,13,13,17,16,10,
        12,13,15,16,14,16,16, 0,18,14,14,15,15,17,10,11,
        12,15,15,11,13,13,16,16,11,13,13,16,16,14,16,16,
        19,17,14,15,15,17,17, 8,10,10,14,14,10,12,11,15,
        15,10,11,12,16,15,14,15,15,18,20,13,14,16,17,18,
         9,11,11,15,16,11,13,13,17,17,11,13,13,17,16,15,
        16,16, 0, 0,15,16,16, 0, 0, 9,11,11,15,15,10,13,
        12,17,15,11,13,13,17,16,15,17,15,20,19,15,16,16,
        19, 0,13,15,14, 0,17,14,15,16, 0,20,15,16,16, 0,
        19,17,18, 0, 0, 0,16,17,18, 0, 0,12,14,14,19,18,
        13,15,14, 0,17,14,15,16,19,19,16,18,16, 0,19,19,
        20,17,20, 0, 8,10,10,13,14,10,11,11,15,15,10,12,
        12,15,16,14,15,14,19,16,14,15,15, 0,18, 9,11,11,
        16,15,11,13,13, 0,16,11,12,13,16,17,14,16,17, 0,
        19,15,16,16,18, 0, 9,11,11,15,16,11,13,13,16,16,
        11,14,13,18,17,15,16,16,18,20,15,17,19, 0, 0,12,
        14,14,17,17,14,16,15, 0, 0,13,14,15,19, 0,16,18,
        20, 0, 0,16,16,18,18, 0,12,14,14,17,20,14,16,16,
        19, 0,14,16,14, 0,20,16,20,17, 0, 0,17, 0,15, 0,
        19,
};

static const static_codebook _44u6__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u6__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u6__p3_0,
        0
};

static const long _vq_quantlist__44u6__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u6__p4_0[] = {
         4, 5, 5, 8, 8, 6, 7, 6, 9, 9, 6, 6, 7, 9, 9, 8,
         9, 9,11,11, 8, 9, 9,11,11, 6, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 7, 8, 9,10, 9,10,10,11,11, 9, 9,10,
        11,12, 6, 7, 7, 9, 9, 7, 8, 7,10, 9, 7, 8, 8,10,
        10, 9,10, 9,12,11, 9,10,10,12,11, 8, 9, 9,11,11,
         9,10,10,12,12, 9,10,10,12,12,11,12,12,14,13,11,
        11,12,13,13, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
        10,12,12,11,12,11,13,12,11,12,12,13,13, 5, 7, 7,
         9, 9, 7, 8, 7,10,10, 7, 7, 8,10,10, 9,10,10,12,
        11, 9,10,10,11,12, 7, 8, 8,10,10, 8, 8, 9,11,11,
         8, 9, 9,11,11,10,10,11,12,13,10,10,11,12,12, 6,
         7, 7,10,10, 7, 9, 8,11,10, 8, 8, 9,10,11,10,11,
        10,13,11,10,11,11,12,12, 9,10,10,12,12,10,10,11,
        13,13,10,11,11,12,13,12,12,12,13,14,12,12,13,14,
        14, 9,10,10,12,12, 9,10,10,13,12,10,11,11,13,13,
        11,12,11,14,12,12,13,13,14,14, 6, 7, 7, 9, 9, 7,
         8, 7,10,10, 7, 8, 8,10,10, 9,10,10,12,11, 9,10,
        10,11,12, 6, 7, 7,10,10, 8, 9, 8,11,10, 7, 8, 9,
        10,11,10,11,11,12,12,10,10,11,11,13, 7, 8, 8,10,
        10, 8, 9, 9,11,11, 8, 9, 8,11,11,10,11,10,13,12,
        10,11,11,13,12, 9,10,10,12,12,10,11,11,13,12, 9,
        10,10,12,13,12,13,12,14,14,11,11,12,12,14, 9,10,
        10,12,12,10,11,11,13,13,10,11,10,13,12,12,12,12,
        14,14,12,13,12,14,13, 8, 9, 9,11,11, 9,10,10,12,
        12, 9,10,10,12,12,11,12,12,14,13,11,12,12,13,14,
         9,10,10,12,12,10,11,11,13,13,10,11,11,13,13,12,
        12,13,14,15,12,12,13,14,14, 9,10,10,12,12, 9,11,
        10,13,12,10,10,11,12,13,12,13,12,14,13,12,12,13,
        14,15,11,12,12,14,13,11,12,12,14,14,12,13,13,14,
        14,13,13,14,14,16,13,14,14,15,15,11,12,11,13,13,
        11,12,11,14,13,12,12,13,14,15,12,14,12,15,12,13,
        14,15,15,16, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
        10,12,12,11,12,12,14,13,11,12,12,13,13, 9,10,10,
        12,12,10,11,10,13,12, 9,10,11,12,13,12,13,12,14,
        14,12,12,13,13,14, 9,10,10,12,12,10,11,11,13,13,
        10,11,11,13,13,12,13,12,14,14,12,13,13,14,14,11,
        11,11,13,13,12,13,12,14,14,11,11,12,13,14,14,14,
        14,16,15,12,12,14,12,15,11,12,12,13,14,12,13,13,
        14,15,11,12,12,14,14,13,14,14,16,16,13,14,13,16,
        13,
};

static const static_codebook _44u6__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u6__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u6__p4_0,
        0
};

static const long _vq_quantlist__44u6__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u6__p5_0[] = {
         2, 3, 3, 6, 6, 8, 8,10,10, 4, 5, 5, 8, 7, 8, 8,
        11,11, 3, 5, 5, 7, 8, 8, 8,11,11, 6, 8, 7, 9, 9,
        10, 9,12,11, 6, 7, 8, 9, 9, 9,10,11,12, 8, 8, 8,
        10, 9,12,11,13,13, 8, 8, 9, 9,10,11,12,13,13,10,
        11,11,12,12,13,13,14,14,10,10,11,11,12,13,13,14,
        14,
};

static const static_codebook _44u6__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u6__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u6__p5_0,
        0
};

static const long _vq_quantlist__44u6__p6_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u6__p6_0[] = {
         3, 4, 4, 5, 5, 7, 7, 9, 9, 4, 5, 4, 6, 6, 7, 7,
         9, 9, 4, 4, 5, 6, 6, 7, 8, 9, 9, 5, 6, 6, 7, 7,
         8, 8,10,10, 5, 6, 6, 7, 7, 8, 8,10,10, 7, 8, 7,
         8, 8,10, 9,11,11, 7, 7, 8, 8, 8, 9,10,10,11, 9,
         9, 9,10,10,11,11,12,11, 9, 9, 9,10,10,11,11,11,
        12,
};

static const static_codebook _44u6__p6_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u6__p6_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u6__p6_0,
        0
};

static const long _vq_quantlist__44u6__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u6__p7_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 9, 8, 7,10,10, 8,
        10,10, 5, 8, 9, 7,10,10, 7,10, 9, 4, 8, 8, 9,11,
        11, 8,11,11, 7,11,11,10,10,13,10,13,13, 7,11,11,
        10,13,12,10,13,13, 5, 9, 8, 8,11,11, 9,11,11, 7,
        11,11,10,13,13,10,12,13, 7,11,11,10,13,13, 9,13,
        10,
};

static const static_codebook _44u6__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u6__p7_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__44u6__p7_0,
        0
};

static const long _vq_quantlist__44u6__p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u6__p7_1[] = {
         3, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8, 4, 5, 5, 7, 6,
         8, 8, 8, 8, 8, 8, 4, 5, 5, 6, 7, 8, 8, 8, 8, 8,
         8, 6, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 6, 7, 7, 7,
         7, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 9, 9,
         9, 9, 7, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8, 8,
         8, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9,
         9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 8, 8,
         8, 8, 8, 9, 9, 9, 9, 9, 9,
};

static const static_codebook _44u6__p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u6__p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u6__p7_1,
        0
};

static const long _vq_quantlist__44u6__p8_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u6__p8_0[] = {
         1, 4, 4, 6, 6, 8, 8, 9, 9,10,10, 4, 6, 6, 7, 7,
         9, 9,10,10,11,11, 4, 6, 6, 7, 7, 9, 9,10,10,11,
        11, 6, 8, 8, 9, 9,10,10,11,11,12,12, 6, 8, 8, 9,
         9,10,10,11,11,12,12, 8, 9, 9,10,10,11,11,12,12,
        13,13, 8, 9, 9,10,10,11,11,12,12,13,13,10,10,10,
        11,11,13,13,13,13,15,14, 9,10,10,12,11,12,13,13,
        13,14,15,11,12,12,13,13,13,13,15,14,15,15,11,11,
        12,13,13,14,14,14,15,15,15,
};

static const static_codebook _44u6__p8_0 = {
        2, 121,
        (char *)_vq_lengthlist__44u6__p8_0,
        1, -524582912, 1618345984, 4, 0,
        (long *)_vq_quantlist__44u6__p8_0,
        0
};

static const long _vq_quantlist__44u6__p8_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u6__p8_1[] = {
         3, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 5, 6, 5, 7, 7,
         7, 7, 8, 7, 8, 8, 5, 5, 6, 6, 7, 7, 7, 7, 7, 8,
         8, 6, 7, 7, 7, 7, 8, 7, 8, 8, 8, 8, 6, 6, 7, 7,
         7, 7, 8, 8, 8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8,
         8, 8, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7,
         8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44u6__p8_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u6__p8_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u6__p8_1,
        0
};

static const long _vq_quantlist__44u6__p9_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u6__p9_0[] = {
         1, 3, 2, 9, 8,15,15,15,15,15,15,15,15,15,15, 4,
         8, 9,13,14,14,14,14,14,14,14,14,14,14,14, 5, 8,
         9,14,14,14,14,14,14,14,14,14,14,14,14,11,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,11,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,
        14,
};

static const static_codebook _44u6__p9_0 = {
        2, 225,
        (char *)_vq_lengthlist__44u6__p9_0,
        1, -514071552, 1627381760, 4, 0,
        (long *)_vq_quantlist__44u6__p9_0,
        0
};

static const long _vq_quantlist__44u6__p9_1[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u6__p9_1[] = {
         1, 4, 4, 7, 7, 8, 9, 8, 8, 9, 8, 9, 8, 9, 9, 4,
         7, 6, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 4, 7,
         6, 9, 9,10,10, 9, 9,10,10,10,10,11,11, 7, 9, 8,
        10,10,11,11,10,10,11,11,11,11,11,11, 7, 8, 9,10,
        10,11,11,10,10,11,11,11,11,11,12, 8,10,10,11,11,
        12,12,11,11,12,12,12,12,13,12, 8,10,10,11,11,12,
        11,11,11,11,12,12,12,12,13, 8, 9, 9,11,10,11,11,
        12,12,12,12,13,12,13,12, 8, 9, 9,11,11,11,11,12,
        12,12,12,12,13,13,13, 9,10,10,11,12,12,12,12,12,
        13,13,13,13,13,13, 9,10,10,11,11,12,12,12,12,13,
        13,13,13,14,13,10,10,10,12,11,12,12,13,13,13,13,
        13,13,13,13,10,10,11,11,11,12,12,13,13,13,13,13,
        13,13,13,10,11,11,12,12,13,12,12,13,13,13,13,13,
        13,14,10,11,11,12,12,13,12,13,13,13,14,13,13,14,
        13,
};

static const static_codebook _44u6__p9_1 = {
        2, 225,
        (char *)_vq_lengthlist__44u6__p9_1,
        1, -522338304, 1620115456, 4, 0,
        (long *)_vq_quantlist__44u6__p9_1,
        0
};

static const long _vq_quantlist__44u6__p9_2[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u6__p9_2[] = {
         3, 5, 5, 7, 7, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 9,
         9, 5, 6, 6, 7, 7, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9,
         9, 9, 5, 6, 6, 7, 7, 8, 8, 8, 8, 8, 8, 9, 9, 9,
         9, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10, 9, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9,10,10, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10, 9, 9, 9,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9,10, 9, 9, 9,10, 9, 9,10, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10,10,10, 9,10, 9,10,10,
         9, 9, 9, 9, 9, 9, 9, 9, 9,10,10, 9,10,10, 9, 9,
        10,
};

static const static_codebook _44u6__p9_2 = {
        2, 289,
        (char *)_vq_lengthlist__44u6__p9_2,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u6__p9_2,
        0
};

static const char _huff_lengthlist__44u6__short[] = {
         4,11,16,13,17,13,17,16,17,17, 4, 7, 9, 9,13,10,
        16,12,16,17, 7, 6, 5, 7, 8, 9,12,12,16,17, 6, 9,
         7, 9,10,10,15,15,17,17, 6, 7, 5, 7, 5, 7, 7,10,
        16,17, 7, 9, 8, 9, 8,10,11,11,15,17, 7, 7, 7, 8,
         5, 8, 8, 9,15,17, 8, 7, 9, 9, 7, 8, 7, 2, 7,15,
        14,13,13,15, 5,10, 4, 3, 6,17,17,15,13,17, 7,11,
         7, 6, 9,16,
};

static const static_codebook _huff_book__44u6__short = {
        2, 100,
        (char *)_huff_lengthlist__44u6__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u7__long[] = {
         3, 9,14,13,15,14,16,13,13,14, 5, 5, 7, 7, 8, 9,
        11,10,12,15,10, 6, 5, 6, 6, 9,10,10,13,16,10, 6,
         6, 6, 6, 8, 9, 9,12,15,14, 7, 6, 6, 5, 6, 6, 8,
        12,15,10, 8, 7, 7, 6, 7, 7, 7,11,13,14,10, 9, 8,
         5, 6, 4, 5, 9,12,10, 9, 9, 8, 6, 6, 5, 3, 6,11,
        12,11,12,12,10, 9, 8, 5, 5, 8,10,11,15,13,13,13,
        12, 8, 6, 7,
};

static const static_codebook _huff_book__44u7__long = {
        2, 100,
        (char *)_huff_lengthlist__44u7__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u7__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u7__p1_0[] = {
         1, 4, 4, 4, 7, 7, 5, 7, 7, 5, 8, 8, 8,10,10, 7,
        10,10, 5, 8, 8, 7,10,10, 8,10,10, 5, 8, 8, 8,11,
        10, 8,10,10, 8,10,10,10,12,13,10,13,13, 7,10,10,
        10,13,12,10,13,13, 5, 8, 8, 8,11,10, 8,10,11, 7,
        10,10,10,13,13,10,12,13, 8,11,11,10,13,13,10,13,
        12,
};

static const static_codebook _44u7__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u7__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u7__p1_0,
        0
};

static const long _vq_quantlist__44u7__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u7__p2_0[] = {
         3, 4, 4, 5, 6, 6, 5, 6, 6, 5, 6, 6, 6, 8, 8, 6,
         7, 8, 5, 6, 6, 6, 8, 7, 6, 8, 8, 5, 6, 6, 6, 8,
         7, 6, 8, 8, 6, 8, 8, 8, 9, 9, 8, 9, 9, 6, 8, 7,
         7, 9, 8, 8, 9, 9, 5, 6, 6, 6, 8, 7, 6, 8, 8, 6,
         8, 8, 8, 9, 9, 7, 8, 9, 6, 8, 8, 8, 9, 9, 8, 9,
         9,
};

static const static_codebook _44u7__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u7__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u7__p2_0,
        0
};

static const long _vq_quantlist__44u7__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u7__p3_0[] = {
         2, 5, 4, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 8,
         9, 9,13,12, 8, 9,10,12,13, 5, 7, 7,10, 9, 7, 9,
         9,11,11, 6, 8, 9,11,11,10,11,11,14,14, 9,10,11,
        13,14, 5, 7, 7, 9, 9, 7, 9, 8,11,11, 7, 9, 9,11,
        11, 9,11,10,14,13,10,11,11,14,14, 8,10,10,14,13,
        10,11,12,15,14, 9,11,11,15,14,13,14,14,16,16,12,
        13,14,17,16, 8,10,10,13,13, 9,11,11,14,15,10,11,
        12,14,15,12,14,13,16,16,13,14,15,15,17, 5, 7, 7,
        10,10, 7, 9, 9,11,11, 7, 9, 9,11,11,10,12,11,15,
        14,10,11,12,14,14, 7, 9, 9,12,12, 9,11,11,13,13,
         9,11,11,13,13,11,13,13,14,17,11,13,13,15,16, 6,
         9, 9,11,11, 8,11,10,13,12, 9,11,11,13,13,11,13,
        12,16,14,11,13,13,16,16,10,12,12,15,15,11,13,13,
        16,16,11,13,13,16,15,14,16,17,17,19,14,16,16,18,
         0, 9,11,11,14,15,10,13,12,16,15,11,13,13,16,16,
        14,15,14, 0,16,14,16,16,18, 0, 5, 7, 7,10,10, 7,
         9, 9,12,11, 7, 9, 9,11,12,10,11,11,15,14,10,11,
        12,14,14, 6, 9, 9,11,11, 9,11,11,13,13, 8,10,11,
        12,13,11,13,13,17,15,11,12,13,14,15, 7, 9, 9,11,
        12, 9,11,11,13,13, 9,11,11,13,13,11,13,12,16,16,
        11,13,13,15,14, 9,11,11,14,15,11,13,13,16,15,10,
        12,13,16,16,15,16,16, 0, 0,14,13,15,16,18,10,11,
        11,15,15,11,13,14,16,18,11,13,13,16,15,15,16,16,
        19, 0,14,15,15,16,16, 8,10,10,13,13,10,12,11,16,
        15,10,11,11,16,15,13,15,16,18, 0,13,14,15,17,17,
         9,11,11,15,15,11,13,13,16,18,11,13,13,16,17,15,
        16,16, 0, 0,15,18,16, 0,17, 9,11,11,15,15,11,13,
        12,17,15,11,13,14,16,17,15,18,15, 0,17,15,16,16,
        18,19,13,15,14, 0,18,14,16,16,19,18,14,16,15,19,
        19,16,18,19, 0, 0,16,17, 0, 0, 0,12,14,14,17,17,
        13,16,14, 0,18,14,16,15,18, 0,16,18,16,19,17,18,
        19,17, 0, 0, 8,10,10,14,14, 9,12,11,15,15,10,11,
        12,15,17,13,15,15,18,16,14,16,15,18,17, 9,11,11,
        16,15,11,13,13, 0,16,11,12,13,16,15,15,16,16, 0,
        17,15,15,16,18,17, 9,12,11,15,17,11,13,13,16,16,
        11,14,13,16,16,15,15,16,18,19,16,18,16, 0, 0,12,
        14,14, 0,16,14,16,16, 0,18,13,14,15,16, 0,17,16,
        18, 0, 0,16,16,17,19, 0,13,14,14,17, 0,14,17,16,
         0,19,14,15,15,18,19,17,16,18, 0, 0,15,19,16, 0,
         0,
};

static const static_codebook _44u7__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u7__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u7__p3_0,
        0
};

static const long _vq_quantlist__44u7__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u7__p4_0[] = {
         4, 5, 5, 8, 8, 6, 7, 6, 9, 9, 6, 6, 7, 9, 9, 8,
         9, 9,11,11, 8, 9, 9,10,11, 6, 7, 7, 9, 9, 7, 8,
         8,10,10, 6, 7, 8, 9,10, 9,10,10,12,12, 9, 9,10,
        11,12, 6, 7, 7, 9, 9, 6, 8, 7,10, 9, 7, 8, 8,10,
        10, 9,10, 9,12,11, 9,10,10,12,11, 8, 9, 9,11,11,
         9,10,10,12,12, 9,10,10,12,12,11,12,12,13,14,11,
        11,12,13,13, 8, 9, 9,11,11, 9,10,10,12,11, 9,10,
        10,12,12,11,12,11,13,13,11,12,12,13,13, 6, 7, 7,
         9, 9, 7, 8, 7,10,10, 7, 7, 8,10,10, 9,10,10,12,
        11, 9,10,10,12,12, 7, 8, 8,10,10, 8, 8, 9,11,11,
         8, 9, 9,11,11,10,11,11,12,12,10,10,11,12,13, 6,
         7, 7,10,10, 7, 9, 8,11,10, 8, 8, 9,10,11,10,11,
        10,13,11,10,11,11,12,12, 9,10,10,12,12,10,10,11,
        13,13,10,11,11,13,12,12,12,13,13,14,12,12,13,14,
        14, 9,10,10,12,12, 9,10,10,12,12,10,11,11,13,13,
        11,12,11,14,12,12,13,13,14,14, 6, 7, 7, 9, 9, 7,
         8, 7,10,10, 7, 7, 8,10,10, 9,10,10,12,11, 9,10,
        10,11,12, 6, 7, 7,10,10, 8, 9, 8,11,10, 7, 8, 9,
        10,11,10,11,11,13,12,10,10,11,11,13, 7, 8, 8,10,
        10, 8, 9, 9,11,11, 8, 9, 9,11,11,10,11,10,13,12,
        10,11,11,12,12, 9,10,10,12,12,10,11,11,13,12, 9,
        10,10,12,13,12,13,12,14,14,11,11,12,12,14, 9,10,
        10,12,12,10,11,11,13,13,10,11,11,13,13,12,13,12,
        14,14,12,13,12,14,13, 8, 9, 9,11,11, 9,10,10,12,
        12, 9,10,10,12,12,11,12,12,14,13,11,12,12,13,13,
         9,10,10,12,12,10,11,11,13,13,10,11,11,13,12,12,
        13,13,14,14,12,12,13,14,14, 9,10,10,12,12, 9,11,
        10,13,12,10,10,11,12,13,11,13,12,14,13,12,12,13,
        14,14,11,12,12,13,13,11,12,13,14,14,12,13,13,14,
        14,13,13,14,14,16,13,14,14,16,16,11,11,11,13,13,
        11,12,11,14,13,12,12,13,14,15,13,14,12,16,13,14,
        14,14,15,16, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
        10,12,12,11,12,12,14,13,11,12,12,13,14, 9,10,10,
        12,12,10,11,10,13,12, 9,10,11,12,13,12,13,12,14,
        14,12,12,13,13,14, 9,10,10,12,12,10,11,11,12,13,
        10,11,11,13,13,12,13,12,14,14,12,13,13,14,14,11,
        12,12,13,13,12,13,12,14,14,11,11,12,13,14,13,15,
        14,16,15,13,12,14,13,16,11,12,12,13,13,12,13,13,
        14,14,12,12,12,14,14,13,14,14,15,15,13,14,13,16,
        14,
};

static const static_codebook _44u7__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u7__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u7__p4_0,
        0
};

static const long _vq_quantlist__44u7__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u7__p5_0[] = {
         2, 3, 3, 6, 6, 7, 8,10,10, 4, 5, 5, 8, 7, 8, 8,
        11,11, 3, 5, 5, 7, 7, 8, 9,11,11, 6, 8, 7, 9, 9,
        10,10,12,12, 6, 7, 8, 9,10,10,10,12,12, 8, 8, 8,
        10,10,12,11,13,13, 8, 8, 9,10,10,11,11,13,13,10,
        11,11,12,12,13,13,14,14,10,11,11,12,12,13,13,14,
        14,
};

static const static_codebook _44u7__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u7__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u7__p5_0,
        0
};

static const long _vq_quantlist__44u7__p6_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u7__p6_0[] = {
         3, 4, 4, 5, 5, 7, 7, 9, 9, 4, 5, 4, 6, 6, 8, 7,
         9, 9, 4, 4, 5, 6, 6, 7, 7, 9, 9, 5, 6, 6, 7, 7,
         8, 8,10,10, 5, 6, 6, 7, 7, 8, 8,10,10, 7, 8, 7,
         8, 8,10, 9,11,11, 7, 7, 8, 8, 8, 9,10,11,11, 9,
         9, 9,10,10,11,10,12,11, 9, 9, 9,10,10,11,11,11,
        12,
};

static const static_codebook _44u7__p6_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u7__p6_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u7__p6_0,
        0
};

static const long _vq_quantlist__44u7__p7_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u7__p7_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 9, 8, 8, 9, 9, 7,
        10,10, 5, 8, 9, 7, 9,10, 8, 9, 9, 4, 9, 9, 9,11,
        10, 8,10,10, 7,11,10,10,10,12,10,12,12, 7,10,10,
        10,12,11,10,12,12, 5, 9, 9, 8,10,10, 9,11,11, 7,
        11,10,10,12,12,10,11,12, 7,10,11,10,12,12,10,12,
        10,
};

static const static_codebook _44u7__p7_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u7__p7_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__44u7__p7_0,
        0
};

static const long _vq_quantlist__44u7__p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u7__p7_1[] = {
         3, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8, 4, 5, 5, 6, 6,
         8, 7, 8, 8, 8, 8, 4, 5, 5, 6, 6, 7, 8, 8, 8, 8,
         8, 6, 7, 6, 7, 7, 8, 8, 9, 9, 9, 9, 6, 6, 7, 7,
         7, 8, 8, 9, 9, 9, 9, 7, 8, 7, 8, 8, 9, 9, 9, 9,
         9, 9, 7, 7, 8, 8, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8,
         9, 9, 9, 9,10, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9,10, 8, 8, 8, 9, 9, 9, 9,10, 9,10,10, 8, 8,
         8, 9, 9, 9, 9, 9,10,10,10,
};

static const static_codebook _44u7__p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u7__p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u7__p7_1,
        0
};

static const long _vq_quantlist__44u7__p8_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u7__p8_0[] = {
         1, 4, 4, 6, 6, 8, 8,10,10,11,11, 4, 6, 6, 7, 7,
         9, 9,11,10,12,12, 5, 6, 5, 7, 7, 9, 9,10,11,12,
        12, 6, 7, 7, 8, 8,10,10,11,11,13,13, 6, 7, 7, 8,
         8,10,10,11,12,13,13, 8, 9, 9,10,10,11,11,12,12,
        14,14, 8, 9, 9,10,10,11,11,12,12,14,14,10,10,10,
        11,11,13,12,14,14,15,15,10,10,10,12,12,13,13,14,
        14,15,15,11,12,12,13,13,14,14,15,14,16,15,11,12,
        12,13,13,14,14,15,15,15,16,
};

static const static_codebook _44u7__p8_0 = {
        2, 121,
        (char *)_vq_lengthlist__44u7__p8_0,
        1, -524582912, 1618345984, 4, 0,
        (long *)_vq_quantlist__44u7__p8_0,
        0
};

static const long _vq_quantlist__44u7__p8_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u7__p8_1[] = {
         4, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 5, 6, 6, 7, 7,
         7, 7, 7, 7, 7, 7, 5, 6, 6, 6, 7, 7, 7, 7, 7, 7,
         7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 6, 7, 7, 7,
         7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 7, 8, 8,
         8, 8, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 7, 7, 7,
         7, 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 8, 8, 8,
         8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7,
         7, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44u7__p8_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u7__p8_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u7__p8_1,
        0
};

static const long _vq_quantlist__44u7__p9_0[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u7__p9_0[] = {
         1, 3, 3,10,10,10,10,10,10,10,10, 4,10,10,10,10,
        10,10,10,10,10,10, 4,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9,
};

static const static_codebook _44u7__p9_0 = {
        2, 121,
        (char *)_vq_lengthlist__44u7__p9_0,
        1, -512171520, 1630791680, 4, 0,
        (long *)_vq_quantlist__44u7__p9_0,
        0
};

static const long _vq_quantlist__44u7__p9_1[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u7__p9_1[] = {
         1, 4, 4, 6, 5, 8, 6, 9, 8,10, 9,11,10, 4, 6, 6,
         8, 8, 9, 9,11,10,11,11,11,11, 4, 6, 6, 8, 8,10,
         9,11,11,11,11,11,12, 6, 8, 8,10,10,11,11,12,12,
        13,12,13,13, 6, 8, 8,10,10,11,11,12,12,12,13,14,
        13, 8,10,10,11,11,12,13,14,14,14,14,15,15, 8,10,
        10,11,12,12,13,13,14,14,14,14,15, 9,11,11,13,13,
        14,14,15,14,16,15,17,15, 9,11,11,12,13,14,14,15,
        14,15,15,15,16,10,12,12,13,14,15,15,15,15,16,17,
        16,17,10,13,12,13,14,14,16,16,16,16,15,16,17,11,
        13,13,14,15,14,17,15,16,17,17,17,17,11,13,13,14,
        15,15,15,15,17,17,16,17,16,
};

static const static_codebook _44u7__p9_1 = {
        2, 169,
        (char *)_vq_lengthlist__44u7__p9_1,
        1, -518889472, 1622704128, 4, 0,
        (long *)_vq_quantlist__44u7__p9_1,
        0
};

static const long _vq_quantlist__44u7__p9_2[] = {
        24,
        23,
        25,
        22,
        26,
        21,
        27,
        20,
        28,
        19,
        29,
        18,
        30,
        17,
        31,
        16,
        32,
        15,
        33,
        14,
        34,
        13,
        35,
        12,
        36,
        11,
        37,
        10,
        38,
        9,
        39,
        8,
        40,
        7,
        41,
        6,
        42,
        5,
        43,
        4,
        44,
        3,
        45,
        2,
        46,
        1,
        47,
        0,
        48,
};

static const char _vq_lengthlist__44u7__p9_2[] = {
         2, 4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6,
         6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8,
         8,
};

static const static_codebook _44u7__p9_2 = {
        1, 49,
        (char *)_vq_lengthlist__44u7__p9_2,
        1, -526909440, 1611661312, 6, 0,
        (long *)_vq_quantlist__44u7__p9_2,
        0
};

static const char _huff_lengthlist__44u7__short[] = {
         5,12,17,16,16,17,17,17,17,17, 4, 7,11,11,12, 9,
        17,10,17,17, 7, 7, 8, 9, 7, 9,11,10,15,17, 7, 9,
        10,11,10,12,14,12,16,17, 7, 8, 5, 7, 4, 7, 7, 8,
        16,16, 6,10, 9,10, 7,10,11,11,16,17, 6, 8, 8, 9,
         5, 7, 5, 8,16,17, 5, 5, 8, 7, 6, 7, 7, 6, 6,14,
        12,10,12,11, 7,11, 4, 4, 2, 7,17,15,15,15, 8,15,
         6, 8, 5, 9,
};

static const static_codebook _huff_book__44u7__short = {
        2, 100,
        (char *)_huff_lengthlist__44u7__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u8__long[] = {
         3, 9,13,14,14,15,14,14,15,15, 5, 4, 6, 8,10,12,
        12,14,15,15, 9, 5, 4, 5, 8,10,11,13,16,16,10, 7,
         4, 3, 5, 7, 9,11,13,13,10, 9, 7, 4, 4, 6, 8,10,
        12,14,13,11, 9, 6, 5, 5, 6, 8,12,14,13,11,10, 8,
         7, 6, 6, 7,10,14,13,11,12,10, 8, 7, 6, 6, 9,13,
        12,11,14,12,11, 9, 8, 7, 9,11,11,12,14,13,14,11,
        10, 8, 8, 9,
};

static const static_codebook _huff_book__44u8__long = {
        2, 100,
        (char *)_huff_lengthlist__44u8__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u8__short[] = {
         6,14,18,18,17,17,17,17,17,17, 4, 7, 9, 9,10,13,
        15,17,17,17, 6, 7, 5, 6, 8,11,16,17,16,17, 5, 7,
         5, 4, 6,10,14,17,17,17, 6, 6, 6, 5, 7,10,13,16,
        17,17, 7, 6, 7, 7, 7, 8, 7,10,15,16,12, 9, 9, 6,
         6, 5, 3, 5,11,15,14,14,13, 5, 5, 7, 3, 4, 8,15,
        17,17,13, 7, 7,10, 6, 6,10,15,17,17,16,10,11,14,
        10,10,15,17,
};

static const static_codebook _huff_book__44u8__short = {
        2, 100,
        (char *)_huff_lengthlist__44u8__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u8_p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u8_p1_0[] = {
         1, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 8, 9, 9, 7,
         9, 9, 5, 7, 7, 7, 9, 9, 8, 9, 9, 5, 7, 7, 7, 9,
         9, 7, 9, 9, 7, 9, 9, 9,10,11, 9,11,10, 7, 9, 9,
         9,11,10, 9,10,11, 5, 7, 7, 7, 9, 9, 7, 9, 9, 7,
         9, 9, 9,11,10, 9,10,10, 8, 9, 9, 9,11,11, 9,11,
        10,
};

static const static_codebook _44u8_p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u8_p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u8_p1_0,
        0
};

static const long _vq_quantlist__44u8_p2_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u8_p2_0[] = {
         4, 5, 5, 8, 8, 5, 7, 6, 9, 9, 5, 6, 7, 9, 9, 8,
         9, 9,11,11, 8, 9, 9,11,11, 5, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 8, 8,10,10, 9,10,10,12,12, 9,10,10,
        11,12, 5, 7, 7, 9, 9, 7, 8, 7,10,10, 7, 8, 8,10,
        10, 9,10, 9,12,11, 9,10,10,12,12, 8, 9, 9,12,11,
         9,10,10,12,12, 9,10,10,12,12,11,12,12,14,14,11,
        11,12,13,14, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
        10,12,12,11,12,11,13,13,11,12,12,14,14, 5, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,
        12, 9,10,10,11,12, 7, 8, 8,10,10, 8, 9, 9,11,11,
         8, 9, 9,11,11,10,11,11,12,13,10,11,11,12,13, 6,
         8, 8,10,10, 8, 9, 8,11,10, 8, 9, 9,11,11,10,11,
        10,13,12,10,11,11,13,13, 9,10,10,12,12,10,11,11,
        13,13,10,11,11,13,13,12,12,13,13,14,12,13,13,14,
        14, 9,10,10,12,12,10,11,10,13,12,10,11,11,13,13,
        11,13,12,14,13,12,13,13,14,14, 5, 7, 7, 9, 9, 7,
         8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,12, 9,10,
        10,12,12, 7, 8, 8,10,10, 8, 9, 9,11,11, 8, 8, 9,
        10,11,10,11,11,13,13,10,10,11,12,13, 7, 8, 8,10,
        10, 8, 9, 9,11,11, 8, 9, 9,11,11,10,11,11,13,13,
        10,11,11,13,12, 9,10,10,12,12,10,11,11,13,13,10,
        10,11,12,13,12,13,13,14,14,12,12,13,13,14, 9,10,
        10,12,12,10,11,11,13,13,10,11,11,13,13,12,13,13,
        15,14,12,13,13,14,13, 8, 9, 9,11,11, 9,10,10,12,
        12, 9,10,10,12,12,12,12,12,14,13,11,12,12,14,14,
         9,10,10,12,12,10,11,11,13,13,10,11,11,13,13,12,
        13,13,14,15,12,13,13,14,15, 9,10,10,12,12,10,11,
        10,13,12,10,11,11,13,13,12,13,12,15,14,12,13,13,
        14,15,11,12,12,14,14,12,13,13,14,14,12,13,13,15,
        14,14,14,14,14,16,14,14,15,16,16,11,12,12,14,14,
        11,12,12,14,14,12,13,13,14,15,13,14,13,16,14,14,
        14,14,16,16, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
        10,12,12,11,12,12,14,13,11,12,12,14,14, 9,10,10,
        12,12,10,11,11,13,13,10,10,11,12,13,12,13,13,15,
        14,12,12,13,13,14, 9,10,10,12,12,10,11,11,13,13,
        10,11,11,13,13,12,13,13,14,14,12,13,13,15,14,11,
        12,12,14,13,12,13,13,15,14,11,12,12,13,14,14,15,
        14,16,15,13,13,14,13,16,11,12,12,14,14,12,13,13,
        14,15,12,13,12,15,14,14,14,14,16,15,14,15,13,16,
        14,
};

static const static_codebook _44u8_p2_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u8_p2_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u8_p2_0,
        0
};

static const long _vq_quantlist__44u8_p3_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u8_p3_0[] = {
         3, 4, 4, 5, 5, 7, 7, 9, 9, 4, 5, 4, 6, 6, 7, 7,
         9, 9, 4, 4, 5, 6, 6, 7, 7, 9, 9, 5, 6, 6, 7, 7,
         8, 8,10,10, 6, 6, 6, 7, 7, 8, 8,10,10, 7, 7, 7,
         8, 8, 9, 9,11,10, 7, 7, 7, 8, 8, 9, 9,10,11, 9,
         9, 9,10,10,11,10,12,11, 9, 9, 9, 9,10,11,11,11,
        12,
};

static const static_codebook _44u8_p3_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u8_p3_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u8_p3_0,
        0
};

static const long _vq_quantlist__44u8_p4_0[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u8_p4_0[] = {
         4, 4, 4, 6, 6, 7, 7, 8, 8, 8, 8,10,10,11,11,11,
        11, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9,10,10,11,11,
        12,12, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9,10,10,11,
        11,12,12, 6, 6, 6, 7, 7, 8, 8, 9, 9, 9, 9,10,10,
        11,11,12,12, 6, 6, 6, 7, 7, 8, 8, 9, 9, 9, 9,10,
        10,11,11,12,12, 7, 7, 7, 8, 8, 9, 8,10, 9,10, 9,
        11,10,12,11,13,12, 7, 7, 7, 8, 8, 8, 9, 9,10, 9,
        10,10,11,11,12,12,13, 8, 8, 8, 9, 9, 9, 9,10,10,
        11,10,11,11,12,12,13,13, 8, 8, 8, 9, 9, 9,10,10,
        10,10,11,11,11,12,12,12,13, 8, 9, 9, 9, 9,10, 9,
        11,10,11,11,12,11,13,12,13,13, 8, 9, 9, 9, 9, 9,
        10,10,11,11,11,11,12,12,13,13,13,10,10,10,10,10,
        11,10,11,11,12,11,13,12,13,13,14,13,10,10,10,10,
        10,10,11,11,11,11,12,12,13,13,13,13,14,11,11,11,
        11,11,12,11,12,12,13,12,13,13,14,13,14,14,11,11,
        11,11,11,11,12,12,12,12,13,13,13,13,14,14,14,11,
        12,12,12,12,13,12,13,12,13,13,14,13,14,14,14,14,
        11,12,12,12,12,12,12,13,13,13,13,13,14,14,14,14,
        14,
};

static const static_codebook _44u8_p4_0 = {
        2, 289,
        (char *)_vq_lengthlist__44u8_p4_0,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u8_p4_0,
        0
};

static const long _vq_quantlist__44u8_p5_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u8_p5_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 8, 8, 9, 9, 7,
         9, 9, 5, 8, 8, 7, 9, 9, 8, 9, 9, 5, 8, 8, 8,10,
        10, 8,10,10, 7,10,10, 9,10,12, 9,12,11, 7,10,10,
         9,11,10, 9,11,12, 5, 8, 8, 8,10,10, 8,10,10, 7,
        10,10, 9,11,11, 9,10,11, 7,10,10, 9,11,11,10,12,
        10,
};

static const static_codebook _44u8_p5_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u8_p5_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__44u8_p5_0,
        0
};

static const long _vq_quantlist__44u8_p5_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u8_p5_1[] = {
         4, 5, 5, 6, 6, 7, 7, 7, 7, 8, 8, 5, 5, 5, 6, 6,
         7, 7, 8, 8, 8, 8, 5, 5, 5, 6, 6, 7, 7, 7, 8, 8,
         8, 6, 6, 6, 7, 7, 7, 7, 8, 8, 8, 8, 6, 6, 6, 7,
         7, 7, 7, 8, 8, 8, 8, 7, 7, 7, 7, 7, 8, 8, 8, 8,
         8, 8, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 7, 8, 7,
         8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 8, 8,
         8, 8, 8, 8, 8, 8, 8, 9, 9,
};

static const static_codebook _44u8_p5_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u8_p5_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u8_p5_1,
        0
};

static const long _vq_quantlist__44u8_p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u8_p6_0[] = {
         2, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9,10,10, 4, 6, 5,
         7, 7, 8, 8, 8, 8, 9, 9,10,10, 4, 6, 6, 7, 7, 8,
         8, 8, 8, 9, 9,10,10, 6, 7, 7, 7, 8, 8, 8, 8, 9,
         9,10,10,10, 6, 7, 7, 8, 8, 8, 8, 9, 8,10, 9,11,
        10, 7, 8, 8, 8, 8, 8, 9, 9, 9,10,10,11,11, 7, 8,
         8, 8, 8, 9, 8, 9, 9,10,10,11,11, 8, 8, 8, 9, 9,
         9, 9, 9,10,10,10,11,11, 8, 8, 8, 9, 9, 9, 9,10,
         9,10,10,11,11, 9, 9, 9, 9,10,10,10,10,10,10,11,
        11,12, 9, 9, 9,10, 9,10,10,10,10,11,10,12,11,10,
        10,10,10,10,11,11,11,11,11,12,12,12,10,10,10,10,
        11,11,11,11,11,12,11,12,12,
};

static const static_codebook _44u8_p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u8_p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u8_p6_0,
        0
};

static const long _vq_quantlist__44u8_p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u8_p6_1[] = {
         3, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5,
         5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44u8_p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u8_p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u8_p6_1,
        0
};

static const long _vq_quantlist__44u8_p7_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u8_p7_0[] = {
         1, 4, 5, 6, 6, 7, 7, 8, 8,10,10,11,11, 5, 6, 6,
         7, 7, 8, 8, 9, 9,11,10,12,11, 5, 6, 6, 7, 7, 8,
         8, 9, 9,10,11,11,12, 6, 7, 7, 8, 8, 9, 9,10,10,
        11,11,12,12, 6, 7, 7, 8, 8, 9, 9,10,10,11,12,13,
        12, 7, 8, 8, 9, 9,10,10,11,11,12,12,13,13, 8, 8,
         8, 9, 9,10,10,11,11,12,12,13,13, 9, 9, 9,10,10,
        11,11,12,12,13,13,14,14, 9, 9, 9,10,10,11,11,12,
        12,13,13,14,14,10,11,11,12,11,13,12,13,13,14,14,
        15,15,10,11,11,11,12,12,13,13,14,14,14,15,15,11,
        12,12,13,13,14,13,15,14,15,15,16,15,11,11,12,13,
        13,13,14,14,14,15,15,15,16,
};

static const static_codebook _44u8_p7_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u8_p7_0,
        1, -523206656, 1618345984, 4, 0,
        (long *)_vq_quantlist__44u8_p7_0,
        0
};

static const long _vq_quantlist__44u8_p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u8_p7_1[] = {
         4, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 5, 6, 6, 7, 7,
         7, 7, 7, 7, 7, 7, 5, 6, 6, 7, 7, 7, 7, 7, 7, 7,
         7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 6, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 8, 8,
         8, 8, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 7, 7, 7,
         8, 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 8, 8, 8,
         8, 8, 8, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7,
         7, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44u8_p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u8_p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u8_p7_1,
        0
};

static const long _vq_quantlist__44u8_p8_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u8_p8_0[] = {
         1, 4, 4, 7, 7, 8, 8, 8, 7, 9, 8,10, 9,11,10, 4,
         6, 6, 8, 8,10, 9, 9, 9,10,10,11,10,12,10, 4, 6,
         6, 8, 8,10,10, 9, 9,10,10,11,11,11,12, 7, 8, 8,
        10,10,11,11,11,10,12,11,12,12,13,11, 7, 8, 8,10,
        10,11,11,10,10,11,11,12,12,13,13, 8,10,10,11,11,
        12,11,12,11,13,12,13,12,14,13, 8,10, 9,11,11,12,
        12,12,12,12,12,13,13,14,13, 8, 9, 9,11,10,12,11,
        13,12,13,13,14,13,14,13, 8, 9, 9,10,11,12,12,12,
        12,13,13,14,15,14,14, 9,10,10,12,11,13,12,13,13,
        14,13,14,14,14,14, 9,10,10,12,12,12,12,13,13,14,
        14,14,15,14,14,10,11,11,13,12,13,12,14,14,14,14,
        14,14,15,15,10,11,11,12,12,13,13,14,14,14,15,15,
        14,16,15,11,12,12,13,12,14,14,14,13,15,14,15,15,
        15,17,11,12,12,13,13,14,14,14,15,15,14,15,15,14,
        17,
};

static const static_codebook _44u8_p8_0 = {
        2, 225,
        (char *)_vq_lengthlist__44u8_p8_0,
        1, -520986624, 1620377600, 4, 0,
        (long *)_vq_quantlist__44u8_p8_0,
        0
};

static const long _vq_quantlist__44u8_p8_1[] = {
        10,
        9,
        11,
        8,
        12,
        7,
        13,
        6,
        14,
        5,
        15,
        4,
        16,
        3,
        17,
        2,
        18,
        1,
        19,
        0,
        20,
};

static const char _vq_lengthlist__44u8_p8_1[] = {
         4, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 6, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 5, 6, 6, 7, 7, 8,
         8, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 7,
         7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9,10,10, 9,10, 8, 8,
         8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,10, 9,10,
        10, 9,10, 8, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9,10, 9,
        10,10,10,10,10,10,10,10, 8, 9, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9,10,10,10,10, 9,10,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10, 9,10,10,10,10,10,10,
        10,10, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,10, 9,10,
        10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9,10, 9,
        10,10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,
        10, 9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,
         9, 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
         9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10, 9, 9, 9,10, 9,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,10,
         9,10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9,
         9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10, 9, 9, 9,10, 9,10, 9,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,
};

static const static_codebook _44u8_p8_1 = {
        2, 441,
        (char *)_vq_lengthlist__44u8_p8_1,
        1, -529268736, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u8_p8_1,
        0
};

static const long _vq_quantlist__44u8_p9_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u8_p9_0[] = {
         1, 3, 3, 9, 9, 9, 9, 9, 9, 4, 9, 9, 9, 9, 9, 9,
         9, 9, 5, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8,
         8,
};

static const static_codebook _44u8_p9_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u8_p9_0,
        1, -511895552, 1631393792, 4, 0,
        (long *)_vq_quantlist__44u8_p9_0,
        0
};

static const long _vq_quantlist__44u8_p9_1[] = {
        9,
        8,
        10,
        7,
        11,
        6,
        12,
        5,
        13,
        4,
        14,
        3,
        15,
        2,
        16,
        1,
        17,
        0,
        18,
};

static const char _vq_lengthlist__44u8_p9_1[] = {
         1, 4, 4, 7, 7, 8, 7, 8, 6, 9, 7,10, 8,11,10,11,
        11,11,11, 4, 7, 6, 9, 9,10, 9, 9, 9,10,10,11,10,
        11,10,11,11,13,11, 4, 7, 7, 9, 9, 9, 9, 9, 9,10,
        10,11,10,11,11,11,12,11,12, 7, 9, 8,11,11,11,11,
        10,10,11,11,12,12,12,12,12,12,14,13, 7, 8, 9,10,
        11,11,11,10,10,11,11,11,11,12,12,14,12,13,14, 8,
         9, 9,11,11,11,11,11,11,12,12,14,12,15,14,14,14,
        15,14, 8, 9, 9,11,11,11,11,12,11,12,12,13,13,13,
        13,13,13,14,14, 8, 9, 9,11,10,12,11,12,12,13,13,
        13,13,15,14,14,14,16,16, 8, 9, 9,10,11,11,12,12,
        12,13,13,13,14,14,14,15,16,15,15, 9,10,10,11,12,
        12,13,13,13,14,14,16,14,14,16,16,16,16,15, 9,10,
        10,11,11,12,13,13,14,15,14,16,14,15,16,16,16,16,
        15,10,11,11,12,13,13,14,15,15,15,15,15,16,15,16,
        15,16,15,15,10,11,11,13,13,14,13,13,15,14,15,15,
        16,15,15,15,16,15,16,10,12,12,14,14,14,14,14,16,
        16,15,15,15,16,16,16,16,16,16,11,12,12,14,14,14,
        14,15,15,16,15,16,15,16,15,16,16,16,16,12,12,13,
        14,14,15,16,16,16,16,16,16,15,16,16,16,16,16,16,
        12,13,13,14,14,14,14,15,16,15,16,16,16,16,16,16,
        16,16,16,12,13,14,14,14,16,15,16,15,16,16,16,16,
        16,16,16,16,16,16,12,14,13,14,15,15,15,16,15,16,
        16,15,16,16,16,16,16,16,16,
};

static const static_codebook _44u8_p9_1 = {
        2, 361,
        (char *)_vq_lengthlist__44u8_p9_1,
        1, -518287360, 1622704128, 5, 0,
        (long *)_vq_quantlist__44u8_p9_1,
        0
};

static const long _vq_quantlist__44u8_p9_2[] = {
        24,
        23,
        25,
        22,
        26,
        21,
        27,
        20,
        28,
        19,
        29,
        18,
        30,
        17,
        31,
        16,
        32,
        15,
        33,
        14,
        34,
        13,
        35,
        12,
        36,
        11,
        37,
        10,
        38,
        9,
        39,
        8,
        40,
        7,
        41,
        6,
        42,
        5,
        43,
        4,
        44,
        3,
        45,
        2,
        46,
        1,
        47,
        0,
        48,
};

static const char _vq_lengthlist__44u8_p9_2[] = {
         2, 3, 4, 5, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6,
         6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7,
};

static const static_codebook _44u8_p9_2 = {
        1, 49,
        (char *)_vq_lengthlist__44u8_p9_2,
        1, -526909440, 1611661312, 6, 0,
        (long *)_vq_quantlist__44u8_p9_2,
        0
};

static const char _huff_lengthlist__44u9__long[] = {
         3, 9,13,13,14,15,14,14,15,15, 5, 5, 9,10,12,12,
        13,14,16,15,10, 6, 6, 6, 8,11,12,13,16,15,11, 7,
         5, 3, 5, 8,10,12,15,15,10,10, 7, 4, 3, 5, 8,10,
        12,12,12,12, 9, 7, 5, 4, 6, 8,10,13,13,12,11, 9,
         7, 5, 5, 6, 9,12,14,12,12,10, 8, 6, 6, 6, 7,11,
        13,12,14,13,10, 8, 7, 7, 7,10,11,11,12,13,12,11,
        10, 8, 8, 9,
};

static const static_codebook _huff_book__44u9__long = {
        2, 100,
        (char *)_huff_lengthlist__44u9__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const char _huff_lengthlist__44u9__short[] = {
         9,16,18,18,17,17,17,17,17,17, 5, 8,11,12,11,12,
        17,17,16,16, 6, 6, 8, 8, 9,10,14,15,16,16, 6, 7,
         7, 4, 6, 9,13,16,16,16, 6, 6, 7, 4, 5, 8,11,15,
        17,16, 7, 6, 7, 6, 6, 8, 9,10,14,16,11, 8, 8, 7,
         6, 6, 3, 4,10,15,14,12,12,10, 5, 6, 3, 3, 8,13,
        15,17,15,11, 6, 8, 6, 6, 9,14,17,15,15,12, 8,10,
         9, 9,12,15,
};

static const static_codebook _huff_book__44u9__short = {
        2, 100,
        (char *)_huff_lengthlist__44u9__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44u9_p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u9_p1_0[] = {
         1, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 9, 9, 7,
         9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 9, 5, 7, 7, 7, 9,
         9, 7, 9, 9, 8, 9, 9, 9,10,11, 9,11,11, 7, 9, 9,
         9,11,10, 9,11,11, 5, 7, 7, 7, 9, 9, 8, 9,10, 7,
         9, 9, 9,11,11, 9,10,11, 7, 9,10, 9,11,11, 9,11,
        10,
};

static const static_codebook _44u9_p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u9_p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44u9_p1_0,
        0
};

static const long _vq_quantlist__44u9_p2_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u9_p2_0[] = {
         3, 5, 5, 8, 8, 5, 7, 7, 9, 9, 6, 7, 7, 9, 9, 8,
         9, 9,11,10, 8, 9, 9,11,11, 6, 7, 7, 9, 9, 7, 8,
         8,10,10, 7, 8, 8, 9,10, 9,10,10,11,11, 9, 9,10,
        11,11, 6, 7, 7, 9, 9, 7, 8, 8,10, 9, 7, 8, 8,10,
        10, 9,10, 9,11,11, 9,10,10,11,11, 8, 9, 9,11,11,
         9,10,10,12,11, 9,10,10,11,12,11,11,11,13,13,11,
        11,11,12,13, 8, 9, 9,11,11, 9,10,10,11,11, 9,10,
        10,12,11,11,12,11,13,12,11,11,12,13,13, 6, 7, 7,
         9, 9, 7, 8, 8,10,10, 7, 8, 8,10,10, 9,10,10,12,
        11, 9,10,10,11,12, 7, 8, 8,10,10, 8, 9, 9,11,11,
         8, 9, 9,10,10,10,11,11,12,12,10,10,11,12,12, 7,
         8, 8,10,10, 8, 9, 8,10,10, 8, 9, 9,10,10,10,11,
        10,12,11,10,10,11,12,12, 9,10,10,11,12,10,11,11,
        12,12,10,11,10,12,12,12,12,12,13,13,11,12,12,13,
        13, 9,10,10,11,11, 9,10,10,12,12,10,11,11,12,13,
        11,12,11,13,12,12,12,12,13,14, 6, 7, 7, 9, 9, 7,
         8, 8,10,10, 7, 8, 8,10,10, 9,10,10,11,11, 9,10,
        10,11,12, 7, 8, 8,10,10, 8, 9, 9,11,10, 8, 8, 9,
        10,10,10,11,10,12,12,10,10,11,11,12, 7, 8, 8,10,
        10, 8, 9, 9,10,10, 8, 9, 9,10,10,10,11,10,12,12,
        10,11,10,12,12, 9,10,10,12,11,10,11,11,12,12, 9,
        10,10,12,12,12,12,12,13,13,11,11,12,12,14, 9,10,
        10,11,12,10,11,11,12,12,10,11,11,12,12,11,12,12,
        14,14,12,12,12,13,13, 8, 9, 9,11,11, 9,10,10,12,
        11, 9,10,10,12,12,11,12,11,13,13,11,11,12,13,13,
         9,10,10,12,12,10,11,11,12,12,10,11,11,12,12,12,
        12,12,14,14,12,12,12,13,13, 9,10,10,12,11,10,11,
        10,12,12,10,11,11,12,12,11,12,12,14,13,12,12,12,
        13,14,11,12,11,13,13,11,12,12,13,13,12,12,12,14,
        14,13,13,13,13,15,13,13,14,15,15,11,11,11,13,13,
        11,12,11,13,13,11,12,12,13,13,12,13,12,15,13,13,
        13,14,14,15, 8, 9, 9,11,11, 9,10,10,11,12, 9,10,
        10,11,12,11,12,11,13,13,11,12,12,13,13, 9,10,10,
        11,12,10,11,10,12,12,10,10,11,12,13,12,12,12,14,
        13,11,12,12,13,14, 9,10,10,12,12,10,11,11,12,12,
        10,11,11,12,12,12,12,12,14,13,12,12,12,14,13,11,
        11,11,13,13,11,12,12,14,13,11,11,12,13,13,13,13,
        13,15,14,12,12,13,13,15,11,12,12,13,13,12,12,12,
        13,14,11,12,12,13,13,13,13,14,14,15,13,13,13,14,
        14,
};

static const static_codebook _44u9_p2_0 = {
        4, 625,
        (char *)_vq_lengthlist__44u9_p2_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u9_p2_0,
        0
};

static const long _vq_quantlist__44u9_p3_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44u9_p3_0[] = {
         3, 4, 4, 5, 5, 7, 7, 8, 8, 4, 5, 5, 6, 6, 7, 7,
         9, 9, 4, 4, 5, 6, 6, 7, 7, 9, 9, 5, 6, 6, 7, 7,
         8, 8, 9, 9, 5, 6, 6, 7, 7, 8, 8, 9, 9, 7, 7, 7,
         8, 8, 9, 9,10,10, 7, 7, 7, 8, 8, 9, 9,10,10, 8,
         9, 9,10, 9,10,10,11,11, 8, 9, 9, 9,10,10,10,11,
        11,
};

static const static_codebook _44u9_p3_0 = {
        2, 81,
        (char *)_vq_lengthlist__44u9_p3_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u9_p3_0,
        0
};

static const long _vq_quantlist__44u9_p4_0[] = {
        8,
        7,
        9,
        6,
        10,
        5,
        11,
        4,
        12,
        3,
        13,
        2,
        14,
        1,
        15,
        0,
        16,
};

static const char _vq_lengthlist__44u9_p4_0[] = {
         4, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9,10,10,11,
        11, 5, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9,10,10,
        11,11, 5, 5, 5, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9,10,
        10,11,11, 6, 6, 6, 7, 6, 7, 7, 8, 8, 9, 9,10,10,
        11,11,12,11, 6, 6, 6, 6, 7, 7, 7, 8, 8, 9, 9,10,
        10,11,11,11,12, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9, 9,
        10,10,11,11,12,12, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9,
         9,10,10,11,11,12,12, 8, 8, 8, 8, 8, 9, 8,10, 9,
        10,10,11,10,12,11,13,12, 8, 8, 8, 8, 8, 9, 9, 9,
        10,10,10,10,11,11,12,12,12, 8, 8, 8, 9, 9, 9, 9,
        10,10,11,10,12,11,12,12,13,12, 8, 8, 8, 9, 9, 9,
         9,10,10,10,11,11,11,12,12,12,13, 9, 9, 9,10,10,
        10,10,11,10,11,11,12,11,13,12,13,13, 9, 9,10,10,
        10,10,10,10,11,11,11,11,12,12,13,13,13,10,11,10,
        11,11,11,11,12,11,12,12,13,12,13,13,14,13,10,10,
        10,11,11,11,11,11,12,12,12,12,13,13,13,13,14,11,
        11,11,12,11,12,12,12,12,13,13,13,13,14,13,14,14,
        11,11,11,11,12,12,12,12,12,12,13,13,13,13,14,14,
        14,
};

static const static_codebook _44u9_p4_0 = {
        2, 289,
        (char *)_vq_lengthlist__44u9_p4_0,
        1, -529530880, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u9_p4_0,
        0
};

static const long _vq_quantlist__44u9_p5_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44u9_p5_0[] = {
         1, 4, 4, 5, 7, 7, 5, 7, 7, 5, 8, 8, 8, 9, 9, 7,
         9, 9, 5, 8, 8, 7, 9, 9, 8, 9, 9, 5, 8, 8, 8,10,
        10, 8,10,10, 7,10,10, 9,10,12, 9,11,11, 7,10,10,
         9,11,10, 9,11,12, 5, 8, 8, 8,10,10, 8,10,10, 7,
        10,10, 9,12,11, 9,10,11, 7,10,10, 9,11,11,10,12,
        10,
};

static const static_codebook _44u9_p5_0 = {
        4, 81,
        (char *)_vq_lengthlist__44u9_p5_0,
        1, -529137664, 1618345984, 2, 0,
        (long *)_vq_quantlist__44u9_p5_0,
        0
};

static const long _vq_quantlist__44u9_p5_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u9_p5_1[] = {
         5, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 5, 6, 6, 6, 6,
         7, 7, 7, 7, 8, 7, 5, 6, 6, 6, 6, 7, 7, 7, 7, 7,
         7, 6, 6, 6, 7, 7, 7, 7, 7, 7, 8, 8, 6, 6, 6, 7,
         7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 7, 8, 8,
         8, 8, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 7, 7, 7,
         8, 7, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 8, 8, 8, 8,
         8, 8, 8, 7, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8,
         8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44u9_p5_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u9_p5_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u9_p5_1,
        0
};

static const long _vq_quantlist__44u9_p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u9_p6_0[] = {
         2, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9,10,10, 4, 6, 5,
         7, 7, 8, 8, 8, 8, 9, 9,10,10, 4, 5, 6, 7, 7, 8,
         8, 8, 8, 9, 9,10,10, 6, 7, 7, 8, 8, 8, 8, 9, 9,
        10,10,10,10, 6, 7, 7, 8, 8, 8, 8, 9, 9,10,10,10,
        10, 7, 8, 8, 8, 8, 9, 9, 9, 9,10,10,11,11, 7, 8,
         8, 8, 8, 9, 9, 9, 9,10,10,11,11, 8, 8, 8, 9, 9,
         9, 9, 9,10,10,10,11,11, 8, 8, 8, 9, 9, 9, 9,10,
         9,10,10,11,11, 9, 9, 9,10,10,10,10,10,11,11,11,
        11,12, 9, 9, 9,10,10,10,10,10,10,11,10,12,11,10,
        10,10,10,10,11,11,11,11,11,12,12,12,10,10,10,10,
        10,11,11,11,11,12,11,12,12,
};

static const static_codebook _44u9_p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u9_p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44u9_p6_0,
        0
};

static const long _vq_quantlist__44u9_p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44u9_p6_1[] = {
         4, 4, 4, 5, 5, 4, 5, 4, 5, 5, 4, 4, 5, 5, 5, 5,
         5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44u9_p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44u9_p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44u9_p6_1,
        0
};

static const long _vq_quantlist__44u9_p7_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44u9_p7_0[] = {
         1, 4, 5, 6, 6, 7, 7, 8, 9,10,10,11,11, 5, 6, 6,
         7, 7, 8, 8, 9, 9,10,10,11,11, 5, 6, 6, 7, 7, 8,
         8, 9, 9,10,10,11,11, 6, 7, 7, 8, 8, 9, 9,10,10,
        11,11,12,12, 6, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
        12, 8, 8, 8, 9, 9,10,10,11,11,12,12,13,13, 8, 8,
         8, 9, 9,10,10,11,11,12,12,13,13, 9, 9, 9,10,10,
        11,11,12,12,13,13,13,13, 9, 9, 9,10,10,11,11,12,
        12,13,13,14,14,10,10,10,11,11,12,12,13,13,14,13,
        15,14,10,10,10,11,11,12,12,13,13,14,14,14,14,11,
        11,12,12,12,13,13,14,14,14,14,15,15,11,11,12,12,
        12,13,13,14,14,14,15,15,15,
};

static const static_codebook _44u9_p7_0 = {
        2, 169,
        (char *)_vq_lengthlist__44u9_p7_0,
        1, -523206656, 1618345984, 4, 0,
        (long *)_vq_quantlist__44u9_p7_0,
        0
};

static const long _vq_quantlist__44u9_p7_1[] = {
        5,
        4,
        6,
        3,
        7,
        2,
        8,
        1,
        9,
        0,
        10,
};

static const char _vq_lengthlist__44u9_p7_1[] = {
         5, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 6, 7, 7,
         7, 7, 7, 7, 7, 7, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 8, 7, 8, 8, 7, 7,
         7, 7, 7, 7, 7, 8, 8, 8, 8,
};

static const static_codebook _44u9_p7_1 = {
        2, 121,
        (char *)_vq_lengthlist__44u9_p7_1,
        1, -531365888, 1611661312, 4, 0,
        (long *)_vq_quantlist__44u9_p7_1,
        0
};

static const long _vq_quantlist__44u9_p8_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u9_p8_0[] = {
         1, 4, 4, 7, 7, 8, 8, 8, 8, 9, 9,10, 9,11,10, 4,
         6, 6, 8, 8, 9, 9, 9, 9,10,10,11,10,12,10, 4, 6,
         6, 8, 8, 9,10, 9, 9,10,10,11,11,12,12, 7, 8, 8,
        10,10,11,11,10,10,11,11,12,12,13,12, 7, 8, 8,10,
        10,11,11,10,10,11,11,12,12,12,13, 8,10, 9,11,11,
        12,12,11,11,12,12,13,13,14,13, 8, 9, 9,11,11,12,
        12,11,12,12,12,13,13,14,13, 8, 9, 9,10,10,12,11,
        13,12,13,13,14,13,15,14, 8, 9, 9,10,10,11,12,12,
        12,13,13,13,14,14,14, 9,10,10,12,11,13,12,13,13,
        14,13,14,14,14,15, 9,10,10,11,12,12,12,13,13,14,
        14,14,15,15,15,10,11,11,12,12,13,13,14,14,14,14,
        15,14,16,15,10,11,11,12,12,13,13,13,14,14,14,14,
        14,15,16,11,12,12,13,13,14,13,14,14,15,14,15,16,
        16,16,11,12,12,13,13,14,13,14,14,15,15,15,16,15,
        15,
};

static const static_codebook _44u9_p8_0 = {
        2, 225,
        (char *)_vq_lengthlist__44u9_p8_0,
        1, -520986624, 1620377600, 4, 0,
        (long *)_vq_quantlist__44u9_p8_0,
        0
};

static const long _vq_quantlist__44u9_p8_1[] = {
        10,
        9,
        11,
        8,
        12,
        7,
        13,
        6,
        14,
        5,
        15,
        4,
        16,
        3,
        17,
        2,
        18,
        1,
        19,
        0,
        20,
};

static const char _vq_lengthlist__44u9_p8_1[] = {
         4, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 6, 6, 6, 7, 7, 8, 8, 8, 8, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 6, 6, 6, 7, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 7,
         7, 7, 8, 8, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 7, 7, 7, 8, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 8, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9, 9,10, 9,10,10,10, 8, 8,
         8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9,10,10, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
        10, 9,10, 9,10,10,10,10, 8, 8, 8, 9, 9, 9, 9, 9,
         9, 9, 9, 9, 9,10,10, 9,10,10,10,10,10, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9, 9,10, 9,10,10,10,10,10,10,
        10,10, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,10,10,10,
        10,10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9, 9, 9,
         9, 9,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9,
         9, 9, 9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,
        10, 9, 9, 9, 9, 9, 9, 9,10, 9,10,10,10,10,10,10,
        10,10,10,10,10,10, 9, 9, 9, 9, 9, 9, 9, 9,10,10,
        10,10,10,10,10,10,10,10,10,10,10, 9, 9, 9, 9, 9,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
         9, 9, 9, 9,10, 9, 9,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10, 9, 9, 9,10, 9,10, 9,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10, 9, 9, 9,10, 9,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10, 9,
         9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10, 9, 9, 9,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,
};

static const static_codebook _44u9_p8_1 = {
        2, 441,
        (char *)_vq_lengthlist__44u9_p8_1,
        1, -529268736, 1611661312, 5, 0,
        (long *)_vq_quantlist__44u9_p8_1,
        0
};

static const long _vq_quantlist__44u9_p9_0[] = {
        7,
        6,
        8,
        5,
        9,
        4,
        10,
        3,
        11,
        2,
        12,
        1,
        13,
        0,
        14,
};

static const char _vq_lengthlist__44u9_p9_0[] = {
         1, 3, 3,11,11,11,11,11,11,11,11,11,11,11,11, 4,
        10,11,11,11,11,11,11,11,11,11,11,11,11,11, 4,10,
        10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _44u9_p9_0 = {
        2, 225,
        (char *)_vq_lengthlist__44u9_p9_0,
        1, -510036736, 1631393792, 4, 0,
        (long *)_vq_quantlist__44u9_p9_0,
        0
};

static const long _vq_quantlist__44u9_p9_1[] = {
        9,
        8,
        10,
        7,
        11,
        6,
        12,
        5,
        13,
        4,
        14,
        3,
        15,
        2,
        16,
        1,
        17,
        0,
        18,
};

static const char _vq_lengthlist__44u9_p9_1[] = {
         1, 4, 4, 7, 7, 8, 7, 8, 7, 9, 8,10, 9,10,10,11,
        11,12,12, 4, 7, 6, 9, 9,10, 9, 9, 8,10,10,11,10,
        12,10,13,12,13,12, 4, 6, 6, 9, 9, 9, 9, 9, 9,10,
        10,11,11,11,12,12,12,12,12, 7, 9, 8,11,10,10,10,
        11,10,11,11,12,12,13,12,13,13,13,13, 7, 8, 9,10,
        10,11,11,10,10,11,11,11,12,13,13,13,13,14,14, 8,
         9, 9,11,11,12,11,12,12,13,12,12,13,13,14,15,14,
        14,14, 8, 9, 9,10,11,11,11,12,12,13,12,13,13,14,
        14,14,15,14,16, 8, 9, 9,11,10,12,12,12,12,15,13,
        13,13,17,14,15,15,15,14, 8, 9, 9,10,11,11,12,13,
        12,13,13,13,14,15,14,14,14,16,15, 9,11,10,12,12,
        13,13,13,13,14,14,16,15,14,14,14,15,15,17, 9,10,
        10,11,11,13,13,13,14,14,13,15,14,15,14,15,16,15,
        16,10,11,11,12,12,13,14,15,14,15,14,14,15,17,16,
        15,15,17,17,10,12,11,13,12,14,14,13,14,15,15,15,
        15,16,17,17,15,17,16,11,12,12,14,13,15,14,15,16,
        17,15,17,15,17,15,15,16,17,15,11,11,12,14,14,14,
        14,14,15,15,16,15,17,17,17,16,17,16,15,12,12,13,
        14,14,14,15,14,15,15,16,16,17,16,17,15,17,17,16,
        12,14,12,14,14,15,15,15,14,14,16,16,16,15,16,16,
        15,17,15,12,13,13,14,15,14,15,17,15,17,16,17,17,
        17,16,17,16,17,17,12,13,13,14,16,15,15,15,16,15,
        17,17,15,17,15,17,16,16,17,
};

static const static_codebook _44u9_p9_1 = {
        2, 361,
        (char *)_vq_lengthlist__44u9_p9_1,
        1, -518287360, 1622704128, 5, 0,
        (long *)_vq_quantlist__44u9_p9_1,
        0
};

static const long _vq_quantlist__44u9_p9_2[] = {
        24,
        23,
        25,
        22,
        26,
        21,
        27,
        20,
        28,
        19,
        29,
        18,
        30,
        17,
        31,
        16,
        32,
        15,
        33,
        14,
        34,
        13,
        35,
        12,
        36,
        11,
        37,
        10,
        38,
        9,
        39,
        8,
        40,
        7,
        41,
        6,
        42,
        5,
        43,
        4,
        44,
        3,
        45,
        2,
        46,
        1,
        47,
        0,
        48,
};

static const char _vq_lengthlist__44u9_p9_2[] = {
         2, 4, 4, 5, 4, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6,
         6, 6, 6, 7, 6, 7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
         7,
};

static const static_codebook _44u9_p9_2 = {
        1, 49,
        (char *)_vq_lengthlist__44u9_p9_2,
        1, -526909440, 1611661312, 6, 0,
        (long *)_vq_quantlist__44u9_p9_2,
        0
};

static const char _huff_lengthlist__44un1__long[] = {
         5, 6,12, 9,14, 9, 9,19, 6, 1, 5, 5, 8, 7, 9,19,
        12, 4, 4, 7, 7, 9,11,18, 9, 5, 6, 6, 8, 7, 8,17,
        14, 8, 7, 8, 8,10,12,18, 9, 6, 8, 6, 8, 6, 8,18,
         9, 8,11, 8,11, 7, 5,15,16,18,18,18,17,15,11,18,
};

static const static_codebook _huff_book__44un1__long = {
        2, 64,
        (char *)_huff_lengthlist__44un1__long,
        0, 0, 0, 0, 0,
        NULL,
        0
};

static const long _vq_quantlist__44un1__p1_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44un1__p1_0[] = {
         1, 4, 4, 5, 8, 7, 5, 7, 8, 5, 8, 8, 8,10,11, 8,
        10,11, 5, 8, 8, 8,11,10, 8,11,10, 4, 9, 9, 8,11,
        11, 8,11,11, 8,12,11,10,12,14,11,13,13, 7,11,11,
        10,13,11,11,13,14, 4, 8, 9, 8,11,11, 8,11,12, 7,
        11,11,11,14,13,10,11,13, 8,11,12,11,13,13,10,14,
        12,
};

static const static_codebook _44un1__p1_0 = {
        4, 81,
        (char *)_vq_lengthlist__44un1__p1_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44un1__p1_0,
        0
};

static const long _vq_quantlist__44un1__p2_0[] = {
        1,
        0,
        2,
};

static const char _vq_lengthlist__44un1__p2_0[] = {
         2, 4, 4, 5, 6, 6, 5, 6, 6, 5, 7, 7, 7, 8, 8, 6,
         7, 9, 5, 7, 7, 6, 8, 7, 7, 9, 8, 4, 7, 7, 7, 9,
         8, 7, 8, 8, 7, 9, 8, 8, 8,10, 9,10,10, 6, 8, 8,
         7,10, 8, 9,10,10, 5, 7, 7, 7, 8, 8, 7, 8, 9, 6,
         8, 8, 9,10,10, 7, 8,10, 6, 8, 9, 9,10,10, 8,10,
         8,
};

static const static_codebook _44un1__p2_0 = {
        4, 81,
        (char *)_vq_lengthlist__44un1__p2_0,
        1, -535822336, 1611661312, 2, 0,
        (long *)_vq_quantlist__44un1__p2_0,
        0
};

static const long _vq_quantlist__44un1__p3_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44un1__p3_0[] = {
         1, 5, 5, 8, 8, 5, 8, 7, 9, 9, 5, 7, 8, 9, 9, 9,
        10, 9,12,12, 9, 9,10,11,12, 6, 8, 8,10,10, 8,10,
        10,11,11, 8, 9,10,11,11,10,11,11,13,13,10,11,11,
        12,13, 6, 8, 8,10,10, 8,10, 9,11,11, 8,10,10,11,
        11,10,11,11,13,12,10,11,11,13,12, 9,11,11,15,13,
        10,12,11,15,13,10,11,11,15,14,12,14,13,16,15,12,
        13,13,17,16, 9,11,11,13,15,10,11,12,14,15,10,11,
        12,14,15,12,13,13,15,16,12,13,13,16,16, 5, 8, 8,
        11,11, 8,10,10,12,12, 8,10,10,12,12,11,12,12,14,
        14,11,12,12,14,14, 8,11,10,13,12,10,11,12,12,13,
        10,12,12,13,13,12,12,13,13,15,11,12,13,15,14, 7,
        10,10,12,12, 9,12,11,13,12,10,12,12,13,14,12,13,
        12,15,13,11,13,12,14,15,10,12,12,16,14,11,12,12,
        16,15,11,13,12,17,16,13,13,15,15,17,13,15,15,20,
        17,10,12,12,14,16,11,12,12,15,15,11,13,13,15,18,
        13,14,13,15,15,13,15,14,16,16, 5, 8, 8,11,11, 8,
        10,10,12,12, 8,10,10,12,12,11,12,12,14,14,11,12,
        12,14,15, 7,10,10,13,12,10,12,12,14,13, 9,10,12,
        12,13,11,13,13,15,15,11,12,13,13,15, 8,10,10,12,
        13,10,12,12,13,13,10,12,11,13,13,11,13,12,15,15,
        12,13,12,15,13,10,12,12,16,14,11,12,12,16,15,10,
        12,12,16,14,14,15,14,18,16,13,13,14,15,16,10,12,
        12,14,16,11,13,13,16,16,11,13,12,14,16,13,15,15,
        18,18,13,15,13,16,14, 8,11,11,16,16,10,13,13,17,
        16,10,12,12,16,15,14,16,15,20,17,13,14,14,17,17,
         9,12,12,16,16,11,13,14,16,17,11,13,13,16,16,15,
        15,19,18, 0,14,15,15,18,18, 9,12,12,17,16,11,13,
        12,17,16,11,12,13,15,17,15,16,15, 0,19,14,15,14,
        19,18,12,14,14, 0,16,13,14,14,19,18,13,15,16,17,
        16,15,15,17,18, 0,14,16,16,19, 0,12,14,14,16,18,
        13,15,13,17,18,13,15,14,17,18,15,18,14,18,18,16,
        17,16, 0,17, 8,11,11,15,15,10,12,12,16,16,10,13,
        13,16,16,13,15,14,17,17,14,15,17,17,18, 9,12,12,
        16,15,11,13,13,16,16,11,12,13,17,17,14,14,15,17,
        17,14,15,16, 0,18, 9,12,12,16,17,11,13,13,16,17,
        11,14,13,18,17,14,16,14,17,17,15,17,17,18,18,12,
        14,14, 0,16,13,15,15,19, 0,12,13,15, 0, 0,14,17,
        16,19, 0,16,15,18,18, 0,12,14,14,17, 0,13,14,14,
        17, 0,13,15,14, 0,18,15,16,16, 0,18,15,18,15, 0,
        17,
};

static const static_codebook _44un1__p3_0 = {
        4, 625,
        (char *)_vq_lengthlist__44un1__p3_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44un1__p3_0,
        0
};

static const long _vq_quantlist__44un1__p4_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44un1__p4_0[] = {
         3, 5, 5, 9, 9, 5, 6, 6,10, 9, 5, 6, 6, 9,10,10,
        10,10,12,11, 9,10,10,12,12, 5, 7, 7,10,10, 7, 7,
         8,10,11, 7, 7, 8,10,11,10,10,11,11,13,10,10,11,
        11,13, 6, 7, 7,10,10, 7, 8, 7,11,10, 7, 8, 7,10,
        10,10,11, 9,13,11,10,11,10,13,11,10,10,10,14,13,
        10,11,11,14,13,10,10,11,13,14,12,12,13,15,15,12,
        12,13,13,14,10,10,10,12,13,10,11,10,13,13,10,11,
        11,13,13,12,13,12,14,13,12,13,13,14,13, 5, 7, 7,
        10,10, 7, 8, 8,11,10, 7, 8, 8,10,10,11,11,11,13,
        13,10,11,11,12,12, 7, 8, 8,11,11, 7, 8, 9,10,12,
         8, 9, 9,11,11,11,10,12,11,14,11,11,12,13,13, 6,
         8, 8,10,11, 7, 9, 7,12,10, 8, 9,10,11,12,10,12,
        10,14,11,11,12,11,13,13,10,11,11,14,14,10,10,11,
        13,14,11,12,12,15,13,12,11,14,12,16,12,13,14,15,
        16,10,10,11,13,14,10,11,10,14,12,11,12,12,13,14,
        12,13,11,15,12,14,14,14,15,15, 5, 7, 7,10,10, 7,
         8, 8,10,10, 7, 8, 8,10,11,10,11,10,12,12,10,11,
        11,12,13, 6, 8, 8,11,11, 8, 9, 9,12,11, 7, 7, 9,
        10,12,11,11,11,12,13,11,10,12,11,15, 7, 8, 8,11,
        11, 8, 9, 9,11,11, 7, 9, 8,12,10,11,12,11,13,12,
        11,12,10,15,11,10,11,10,14,12,11,12,11,14,13,10,
        10,11,13,14,13,13,13,17,15,12,11,14,12,15,10,10,
        11,13,14,11,12,12,14,14,10,11,10,14,13,13,14,13,
        16,17,12,14,11,16,12, 9,10,10,14,13,10,11,10,14,
        14,10,11,11,13,13,13,14,14,16,15,12,13,13,14,14,
         9,11,10,14,13,10,10,12,13,14,11,12,11,14,13,13,
        14,14,14,15,13,14,14,15,15, 9,10,11,13,14,10,11,
        10,15,13,11,11,12,12,15,13,14,12,15,14,13,13,14,
        14,15,12,13,12,16,14,11,11,12,15,14,13,15,13,16,
        14,13,12,15,12,17,15,16,15,16,16,12,12,13,13,15,
        11,13,11,15,14,13,13,14,15,17,13,14,12, 0,13,14,
        15,14,15, 0, 9,10,10,13,13,10,11,11,13,13,10,11,
        11,13,13,12,13,12,14,14,13,14,14,15,17, 9,10,10,
        13,13,11,12,11,15,12,10,10,11,13,16,13,14,13,15,
        14,13,13,14,15,16,10,10,11,13,14,11,11,12,13,14,
        10,12,11,14,14,13,13,13,14,15,13,15,13,16,15,12,
        13,12,15,13,12,15,13,15,15,11,11,13,14,15,15,15,
        15,15,17,13,12,14,13,17,12,12,14,14,15,13,13,14,
        14,16,11,13,11,16,15,14,16,16,17, 0,14,13,11,16,
        12,
};

static const static_codebook _44un1__p4_0 = {
        4, 625,
        (char *)_vq_lengthlist__44un1__p4_0,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44un1__p4_0,
        0
};

static const long _vq_quantlist__44un1__p5_0[] = {
        4,
        3,
        5,
        2,
        6,
        1,
        7,
        0,
        8,
};

static const char _vq_lengthlist__44un1__p5_0[] = {
         1, 4, 4, 7, 7, 8, 8, 9, 9, 4, 6, 5, 8, 7, 8, 8,
        10, 9, 4, 6, 6, 8, 8, 8, 8,10,10, 7, 8, 7, 9, 9,
         9, 9,11,10, 7, 8, 8, 9, 9, 9, 9,10,11, 8, 8, 8,
         9, 9,10,10,11,11, 8, 8, 8, 9, 9,10,10,11,11, 9,
        10,10,11,10,11,11,12,12, 9,10,10,10,11,11,11,12,
        12,
};

static const static_codebook _44un1__p5_0 = {
        2, 81,
        (char *)_vq_lengthlist__44un1__p5_0,
        1, -531628032, 1611661312, 4, 0,
        (long *)_vq_quantlist__44un1__p5_0,
        0
};

static const long _vq_quantlist__44un1__p6_0[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44un1__p6_0[] = {
         1, 4, 4, 6, 6, 8, 8,10,10,11,11,15,15, 4, 5, 5,
         8, 8, 9, 9,11,11,12,12,16,16, 4, 5, 6, 8, 8, 9,
         9,11,11,12,12,14,14, 7, 8, 8, 9, 9,10,10,11,12,
        13,13,16,17, 7, 8, 8, 9, 9,10,10,12,12,12,13,15,
        15, 9,10,10,10,10,11,11,12,12,13,13,15,16, 9, 9,
         9,10,10,11,11,13,12,13,13,17,17,10,11,11,11,12,
        12,12,13,13,14,15, 0,18,10,11,11,12,12,12,13,14,
        13,14,14,17,16,11,12,12,13,13,14,14,14,14,15,16,
        17,16,11,12,12,13,13,14,14,14,14,15,15,17,17,14,
        15,15,16,16,16,17,17,16, 0,17, 0,18,14,15,15,16,
        16, 0,15,18,18, 0,16, 0, 0,
};

static const static_codebook _44un1__p6_0 = {
        2, 169,
        (char *)_vq_lengthlist__44un1__p6_0,
        1, -526516224, 1616117760, 4, 0,
        (long *)_vq_quantlist__44un1__p6_0,
        0
};

static const long _vq_quantlist__44un1__p6_1[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44un1__p6_1[] = {
         2, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 6, 5, 5,
         6, 5, 6, 6, 5, 6, 6, 6, 6,
};

static const static_codebook _44un1__p6_1 = {
        2, 25,
        (char *)_vq_lengthlist__44un1__p6_1,
        1, -533725184, 1611661312, 3, 0,
        (long *)_vq_quantlist__44un1__p6_1,
        0
};

static const long _vq_quantlist__44un1__p7_0[] = {
        2,
        1,
        3,
        0,
        4,
};

static const char _vq_lengthlist__44un1__p7_0[] = {
         1, 5, 3,11,11,11,11,11,11,11, 8,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,10,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,10,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11, 8,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,10,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11, 7,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,10,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
        10,
};

static const static_codebook _44un1__p7_0 = {
        4, 625,
        (char *)_vq_lengthlist__44un1__p7_0,
        1, -518709248, 1626677248, 3, 0,
        (long *)_vq_quantlist__44un1__p7_0,
        0
};

static const long _vq_quantlist__44un1__p7_1[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44un1__p7_1[] = {
         1, 4, 4, 6, 6, 6, 6, 9, 8, 9, 8, 8, 8, 5, 7, 7,
         7, 7, 8, 8, 8,10, 8,10, 8, 9, 5, 7, 7, 8, 7, 7,
         8,10,10,11,10,12,11, 7, 8, 8, 9, 9, 9,10,11,11,
        11,11,11,11, 7, 8, 8, 8, 9, 9, 9,10,10,10,11,11,
        12, 7, 8, 8, 9, 9,10,11,11,12,11,12,11,11, 7, 8,
         8, 9, 9,10,10,11,11,11,12,12,11, 8,10,10,10,10,
        11,11,14,11,12,12,12,13, 9,10,10,10,10,12,11,14,
        11,14,11,12,13,10,11,11,11,11,13,11,14,14,13,13,
        13,14,11,11,11,12,11,12,12,12,13,14,14,13,14,12,
        11,12,12,12,12,13,13,13,14,13,14,14,11,12,12,14,
        12,13,13,12,13,13,14,14,14,
};

static const static_codebook _44un1__p7_1 = {
        2, 169,
        (char *)_vq_lengthlist__44un1__p7_1,
        1, -523010048, 1618608128, 4, 0,
        (long *)_vq_quantlist__44un1__p7_1,
        0
};

static const long _vq_quantlist__44un1__p7_2[] = {
        6,
        5,
        7,
        4,
        8,
        3,
        9,
        2,
        10,
        1,
        11,
        0,
        12,
};

static const char _vq_lengthlist__44un1__p7_2[] = {
         3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 9, 8, 4, 5, 5,
         6, 6, 8, 8, 9, 8, 9, 9, 9, 9, 4, 5, 5, 7, 6, 8,
         8, 8, 8, 9, 8, 9, 8, 6, 7, 7, 7, 8, 8, 8, 9, 9,
         9, 9, 9, 9, 6, 7, 7, 7, 7, 8, 8, 9, 9, 9, 9, 9,
         9, 7, 8, 8, 8, 8, 9, 8, 9, 9,10, 9, 9,10, 7, 8,
         8, 8, 8, 9, 9, 9, 9, 9, 9,10,10, 8, 9, 9, 9, 9,
         9, 9, 9, 9,10,10, 9,10, 8, 9, 9, 9, 9, 9, 9, 9,
         9, 9, 9,10,10, 9, 9, 9,10, 9, 9,10, 9, 9,10,10,
        10,10, 9, 9, 9, 9, 9, 9, 9,10, 9,10,10,10,10, 9,
         9, 9,10, 9, 9,10,10, 9,10,10,10,10, 9, 9, 9,10,
         9, 9, 9,10,10,10,10,10,10,
};

static const static_codebook _44un1__p7_2 = {
        2, 169,
        (char *)_vq_lengthlist__44un1__p7_2,
        1, -531103744, 1611661312, 4, 0,
        (long *)_vq_quantlist__44un1__p7_2,
        0
};

static const char _huff_lengthlist__44un1__short[] = {
        12,12,14,12,14,14,14,14,12, 6, 6, 8, 9, 9,11,14,
        12, 4, 2, 6, 6, 7,11,14,13, 6, 5, 7, 8, 9,11,14,
        13, 8, 5, 8, 6, 8,12,14,12, 7, 7, 8, 8, 8,10,14,
        12, 6, 3, 4, 4, 4, 7,14,11, 7, 4, 6, 6, 6, 8,14,
};

static const static_codebook _huff_book__44un1__short = {
        2, 64,
        (char *)_huff_lengthlist__44un1__short,
        0, 0, 0, 0, 0,
        NULL,
        0
};

