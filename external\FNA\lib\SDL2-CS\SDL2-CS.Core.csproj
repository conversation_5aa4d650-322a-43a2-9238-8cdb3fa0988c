<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net40;netstandard2.0;net7.0</TargetFrameworks>
		<Platforms>x64</Platforms>
	</PropertyGroup>
	<PropertyGroup>
		<EnableDefaultItems>false</EnableDefaultItems>
		<AssemblyName>SDL2-CS</AssemblyName>
		<RootNamespace>SDL2</RootNamespace>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>
	<PropertyGroup Condition=" '$(TargetFramework)' != 'net40' ">
		<IsTrimmable>true</IsTrimmable>
		<EnableAOTAnalyzer>true</EnableAOTAnalyzer>
	</PropertyGroup>
	<PropertyGroup>
		<SDLSettingsPropsFilePath>$(SolutionDir)SDL2-CS.Settings.props</SDLSettingsPropsFilePath>
	</PropertyGroup>
	<Import Project="$(SDLSettingsPropsFilePath)" Condition="Exists('$(SDLSettingsPropsFilePath)')" />
	<Target Name="ValidatePropsFilePath" BeforeTargets="BeforeBuild">
		<Message Importance="High" Text="No property overrides found at '$(SDLSettingsPropsFilePath)'" Condition="!Exists('$(SDLSettingsPropsFilePath)')" />
		<Message Importance="High" Text="Loaded property overrides from '$(SDLSettingsPropsFilePath)'" Condition="Exists('$(SDLSettingsPropsFilePath)')" />
	</Target>	
	<ItemGroup>
		<Compile Include="src\SDL2.cs" />
		<Compile Include="src\SDL2_gfx.cs" />
		<Compile Include="src\SDL2_image.cs" />
		<Compile Include="src\SDL2_mixer.cs" />
		<Compile Include="src\SDL2_ttf.cs" />
	</ItemGroup>
	<ItemGroup>
		<None Include="app.config" />
	</ItemGroup>
</Project>
