using System.Reflection;
using System.Runtime.CompilerServices;
[assembly: AssemblyVersion("4.0.0.0")]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.SignedInEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.SignedOutEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.InviteAcceptedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.Gamer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.FriendGamer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerPrivilegeSetting))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerPrivileges))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerServicesNotAvailableException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.SignedInGamerCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.AchievementCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.PropertyDictionary))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardIdentity))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardReader))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardEntry))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.Guide))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.MessageBoxIcon))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.NotificationPosition))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.ControllerSensitivity))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GameDifficulty))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.RacingCameraAngle))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerZone))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerPresenceMode))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardKey))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardOutcome))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.FriendCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.NetworkException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.NetworkNotAvailableException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GameDefaults))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerPresence))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerProfile))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerServicesDispatcher))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerPrivilegeException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GameUpdateRequiredException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GuideAlreadyVisibleException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.SignedInGamer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.Achievement))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.LeaderboardWriter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.GamerServices.GamerCollection<Microsoft.Xna.Framework.GamerServices.Gamer>))]
