﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<LangVersion>Latest</LangVersion>
  </PropertyGroup>


	<!-- To set FileEmbed_MaxEmbedSize, you must first make it visible to the compiler -->
	<ItemGroup>
		<CompilerVisibleProperty Include="FileEmbed_MaxEmbedSize" />
	</ItemGroup>
	<PropertyGroup>
		<FileEmbed_MaxEmbedSize>1048576</FileEmbed_MaxEmbedSize>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>


	<ItemGroup>
		<ProjectReference Include="..\FileEmbed\FileEmbed.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
	</ItemGroup>
</Project>