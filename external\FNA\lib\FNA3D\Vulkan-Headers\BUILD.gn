# Copyright (C) 2018-2019 The ANGLE Project Authors.
# Copyright (C) 2019 LunarG, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build_overrides/vulkan_headers.gni")

config("vulkan_headers_config") {
  include_dirs = [ "include" ]

  if (is_win) {
    defines = [ "VK_USE_PLATFORM_WIN32_KHR" ]
  }
  if (defined(vulkan_use_x11) && vulkan_use_x11) {
    defines = [ "VK_USE_PLATFORM_XCB_KHR" ]
  }
  if (is_android) {
    defines = [ "VK_USE_PLATFORM_ANDROID_KHR" ]
  }
  if (is_fuchsia) {
    defines = [ "VK_USE_PLATFORM_FUCHSIA" ]
  }
  if (is_mac) {
    defines = [ "VK_USE_PLATFORM_METAL_EXT" ]
  }
  if (defined(is_ggp) && is_ggp) {
    defines = [ "VK_USE_PLATFORM_GGP" ]
  }
}

# Vulkan headers only, no compiled sources.
source_set("vulkan_headers") {
  sources = [
    "include/vulkan/vk_icd.h",
    "include/vulkan/vk_layer.h",
    "include/vulkan/vk_platform.h",
    "include/vulkan/vk_sdk_platform.h",
    "include/vulkan/vulkan.h",
    "include/vulkan/vulkan.hpp",
    "include/vulkan/vulkan_core.h",
  ]
  public_configs = [ ":vulkan_headers_config" ]
}

