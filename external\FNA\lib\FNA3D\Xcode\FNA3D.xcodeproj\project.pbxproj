// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		021FF3972A4A764500A63B55 /* FNA3D_Memory.c in Sources */ = {isa = PBXBuildFile; fileRef = 021FF3962A4A764500A63B55 /* FNA3D_Memory.c */; };
		021FF3982A4A764500A63B55 /* FNA3D_Memory.c in Sources */ = {isa = PBXBuildFile; fileRef = 021FF3962A4A764500A63B55 /* FNA3D_Memory.c */; };
		02FD608F2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 02FD608E2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c */; };
		02FD60902A7B227300E1D7A5 /* FNA3D_CommandBuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 02FD608E2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c */; };
		7B8B6CBE24452690001C08D6 /* mojoshader_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CB824452690001C08D6 /* mojoshader_common.c */; };
		7B8B6CBF24452690001C08D6 /* mojoshader_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CB824452690001C08D6 /* mojoshader_common.c */; };
		7B8B6CC224452690001C08D6 /* mojoshader.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBA24452690001C08D6 /* mojoshader.c */; };
		7B8B6CC324452690001C08D6 /* mojoshader.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBA24452690001C08D6 /* mojoshader.c */; };
		7B8B6CC424452690001C08D6 /* mojoshader_effects.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBB24452690001C08D6 /* mojoshader_effects.c */; };
		7B8B6CC524452690001C08D6 /* mojoshader_effects.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBB24452690001C08D6 /* mojoshader_effects.c */; };
		7B8B6CC9244526A7001C08D6 /* mojoshader_profile_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CC6244526A7001C08D6 /* mojoshader_profile_common.c */; };
		7B8B6CCA244526A7001C08D6 /* mojoshader_profile_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CC6244526A7001C08D6 /* mojoshader_profile_common.c */; };
		7B9905852B434B8E00AEA00E /* libSDL2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 7B81CE342B434A04007EC76D /* libSDL2.dylib */; };
		7BC01C042B4348CD00941563 /* mojoshader_opengl.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BC01C032B4348CD00941563 /* mojoshader_opengl.c */; };
		7BC01C062B4348ED00941563 /* mojoshader_profile_glsl.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BC01C052B4348EC00941563 /* mojoshader_profile_glsl.c */; };
		7BC01C072B4348F300941563 /* mojoshader_profile_spirv.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9F275C046100050413 /* mojoshader_profile_spirv.c */; };
		7BC01C082B4348F300941563 /* mojoshader.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBA24452690001C08D6 /* mojoshader.c */; };
		7BC01C092B4348F300941563 /* mojoshader_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CB824452690001C08D6 /* mojoshader_common.c */; };
		7BC01C0A2B4348F300941563 /* mojoshader_vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9C275C044E00050413 /* mojoshader_vulkan.c */; };
		7BC01C0B2B4348F300941563 /* mojoshader_profile_common.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CC6244526A7001C08D6 /* mojoshader_profile_common.c */; };
		7BC01C0C2B4348F300941563 /* mojoshader_effects.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B8B6CBB24452690001C08D6 /* mojoshader_effects.c */; };
		7BC01C0D2B4348F700941563 /* FNA3D_Driver_Vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B99275C042200050413 /* FNA3D_Driver_Vulkan.c */; };
		7BC01C0E2B4348F700941563 /* FNA3D_CommandBuffer.c in Sources */ = {isa = PBXBuildFile; fileRef = 02FD608E2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c */; };
		7BC01C0F2B4348F700941563 /* FNA3D_Image.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206C2445254300736AB0 /* FNA3D_Image.c */; };
		7BC01C102B4348F700941563 /* FNA3D_PipelineCache.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206E2445254300736AB0 /* FNA3D_PipelineCache.c */; };
		7BC01C112B4348F700941563 /* FNA3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF820682445254300736AB0 /* FNA3D.c */; };
		7BC01C122B4348F700941563 /* FNA3D_Memory.c in Sources */ = {isa = PBXBuildFile; fileRef = 021FF3962A4A764500A63B55 /* FNA3D_Memory.c */; };
		7BC01C142B43490100941563 /* FNA3D_Driver_OpenGL.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BC01C132B43490100941563 /* FNA3D_Driver_OpenGL.c */; };
		7BF820702445254300736AB0 /* FNA3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF820682445254300736AB0 /* FNA3D.c */; };
		7BF820712445254300736AB0 /* FNA3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF820682445254300736AB0 /* FNA3D.c */; };
		7BF820782445254300736AB0 /* FNA3D_Image.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206C2445254300736AB0 /* FNA3D_Image.c */; };
		7BF820792445254300736AB0 /* FNA3D_Image.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206C2445254300736AB0 /* FNA3D_Image.c */; };
		7BF8207C2445254300736AB0 /* FNA3D_PipelineCache.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206E2445254300736AB0 /* FNA3D_PipelineCache.c */; };
		7BF8207D2445254300736AB0 /* FNA3D_PipelineCache.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF8206E2445254300736AB0 /* FNA3D_PipelineCache.c */; };
		7BF94B9A275C042200050413 /* FNA3D_Driver_Vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B99275C042200050413 /* FNA3D_Driver_Vulkan.c */; };
		7BF94B9B275C042200050413 /* FNA3D_Driver_Vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B99275C042200050413 /* FNA3D_Driver_Vulkan.c */; };
		7BF94B9D275C044E00050413 /* mojoshader_vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9C275C044E00050413 /* mojoshader_vulkan.c */; };
		7BF94B9E275C044E00050413 /* mojoshader_vulkan.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9C275C044E00050413 /* mojoshader_vulkan.c */; };
		7BF94BA0275C046100050413 /* mojoshader_profile_spirv.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9F275C046100050413 /* mojoshader_profile_spirv.c */; };
		7BF94BA1275C046100050413 /* mojoshader_profile_spirv.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BF94B9F275C046100050413 /* mojoshader_profile_spirv.c */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7B81CE252B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF66C0761BA81005FE872;
			remoteInfo = Framework;
		};
		7B81CE272B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88B5423E2437C00DCD162;
			remoteInfo = "Framework-iOS";
		};
		7B81CE292B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88D1523E24BED00DCD162;
			remoteInfo = "Framework-tvOS";
		};
		7B81CE2B2B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = E2D187CF28A5673500D2B4F1;
			remoteInfo = "xcFramework-iOS";
		};
		7B81CE2D2B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6B30761BA81005FE872;
			remoteInfo = "Static Library";
		};
		7B81CE2F2B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88E5423E24D3B00DCD162;
			remoteInfo = "Static Library-iOS";
		};
		7B81CE312B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A769B23D23E259AE00872273;
			remoteInfo = "Static Library-tvOS";
		};
		7B81CE332B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = DB31407717554B71006C0E22;
			remoteInfo = "Shared Library";
		};
		7B81CE352B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A75FCEB323E25AB700529352;
			remoteInfo = "Shared Library-iOS";
		};
		7B81CE372B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A75FD06C23E25AC700529352;
			remoteInfo = "Shared Library-tvOS";
		};
		7B81CE392B434A04007EC76D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6BE0761BA81005FE872;
			remoteInfo = "Standard DMG";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7B1CDD432190C0A200175C7B /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E140B2190E0CB00616654 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		021FF3962A4A764500A63B55 /* FNA3D_Memory.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_Memory.c; path = ../src/FNA3D_Memory.c; sourceTree = "<group>"; };
		02FD608D2A7B226300E1D7A5 /* FNA3D_CommandBuffer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = FNA3D_CommandBuffer.h; path = ../src/FNA3D_CommandBuffer.h; sourceTree = "<group>"; };
		02FD608E2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_CommandBuffer.c; path = ../src/FNA3D_CommandBuffer.c; sourceTree = "<group>"; };
		7B1CDD452190C0A200175C7B /* libFNA3D.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libFNA3D.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7B7E140D2190E0CB00616654 /* libFNA3D.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libFNA3D.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7B81CE172B434A04007EC76D /* SDL.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL.xcodeproj; path = ../../SDL2/Xcode/SDL/SDL.xcodeproj; sourceTree = "<group>"; };
		7B8B6CB824452690001C08D6 /* mojoshader_common.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_common.c; path = ../MojoShader/mojoshader_common.c; sourceTree = "<group>"; };
		7B8B6CBA24452690001C08D6 /* mojoshader.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader.c; path = ../MojoShader/mojoshader.c; sourceTree = "<group>"; };
		7B8B6CBB24452690001C08D6 /* mojoshader_effects.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_effects.c; path = ../MojoShader/mojoshader_effects.c; sourceTree = "<group>"; };
		7B8B6CC6244526A7001C08D6 /* mojoshader_profile_common.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_profile_common.c; path = ../MojoShader/profiles/mojoshader_profile_common.c; sourceTree = "<group>"; };
		7BC01BFE2B4346D400941563 /* libFNA3D.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libFNA3D.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		7BC01C032B4348CD00941563 /* mojoshader_opengl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_opengl.c; path = ../MojoShader/mojoshader_opengl.c; sourceTree = "<group>"; };
		7BC01C052B4348EC00941563 /* mojoshader_profile_glsl.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_profile_glsl.c; path = ../MojoShader/profiles/mojoshader_profile_glsl.c; sourceTree = "<group>"; };
		7BC01C132B43490100941563 /* FNA3D_Driver_OpenGL.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_Driver_OpenGL.c; path = ../src/FNA3D_Driver_OpenGL.c; sourceTree = "<group>"; };
		7BF820652445251D00736AB0 /* FNA3D_SysRenderer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FNA3D_SysRenderer.h; path = ../include/FNA3D_SysRenderer.h; sourceTree = "<group>"; };
		7BF820662445251D00736AB0 /* FNA3D_Image.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FNA3D_Image.h; path = ../include/FNA3D_Image.h; sourceTree = "<group>"; };
		7BF820672445251D00736AB0 /* FNA3D.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FNA3D.h; path = ../include/FNA3D.h; sourceTree = "<group>"; };
		7BF820682445254300736AB0 /* FNA3D.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D.c; path = ../src/FNA3D.c; sourceTree = "<group>"; };
		7BF8206C2445254300736AB0 /* FNA3D_Image.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_Image.c; path = ../src/FNA3D_Image.c; sourceTree = "<group>"; };
		7BF8206E2445254300736AB0 /* FNA3D_PipelineCache.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_PipelineCache.c; path = ../src/FNA3D_PipelineCache.c; sourceTree = "<group>"; };
		7BF94B99275C042200050413 /* FNA3D_Driver_Vulkan.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FNA3D_Driver_Vulkan.c; path = ../src/FNA3D_Driver_Vulkan.c; sourceTree = "<group>"; };
		7BF94B9C275C044E00050413 /* mojoshader_vulkan.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_vulkan.c; path = ../MojoShader/mojoshader_vulkan.c; sourceTree = "<group>"; };
		7BF94B9F275C046100050413 /* mojoshader_profile_spirv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = mojoshader_profile_spirv.c; path = ../MojoShader/profiles/mojoshader_profile_spirv.c; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7B1CDD422190C0A200175C7B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E140A2190E0CB00616654 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7BC01BFC2B4346D400941563 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7B9905852B434B8E00AEA00E /* libSDL2.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7B1CDD3C2190C0A200175C7B = {
			isa = PBXGroup;
			children = (
				7B81CE172B434A04007EC76D /* SDL.xcodeproj */,
				7B8B6CB624452656001C08D6 /* MojoShader */,
				7B1CDDA72190C52900175C7B /* Public Headers */,
				7B1CDDA62190C50300175C7B /* Library Source */,
				7B1CDD462190C0A200175C7B /* Products */,
				7B9905792B434B8E00AEA00E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7B1CDD462190C0A200175C7B /* Products */ = {
			isa = PBXGroup;
			children = (
				7B1CDD452190C0A200175C7B /* libFNA3D.a */,
				7B7E140D2190E0CB00616654 /* libFNA3D.a */,
				7BC01BFE2B4346D400941563 /* libFNA3D.dylib */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7B1CDDA62190C50300175C7B /* Library Source */ = {
			isa = PBXGroup;
			children = (
				02FD608E2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c */,
				021FF3962A4A764500A63B55 /* FNA3D_Memory.c */,
				7BF94B99275C042200050413 /* FNA3D_Driver_Vulkan.c */,
				7BC01C132B43490100941563 /* FNA3D_Driver_OpenGL.c */,
				7BF8206C2445254300736AB0 /* FNA3D_Image.c */,
				7BF8206E2445254300736AB0 /* FNA3D_PipelineCache.c */,
				7BF820682445254300736AB0 /* FNA3D.c */,
			);
			name = "Library Source";
			sourceTree = "<group>";
		};
		7B1CDDA72190C52900175C7B /* Public Headers */ = {
			isa = PBXGroup;
			children = (
				02FD608D2A7B226300E1D7A5 /* FNA3D_CommandBuffer.h */,
				7BF820652445251D00736AB0 /* FNA3D_SysRenderer.h */,
				7BF820662445251D00736AB0 /* FNA3D_Image.h */,
				7BF820672445251D00736AB0 /* FNA3D.h */,
			);
			name = "Public Headers";
			sourceTree = "<group>";
		};
		7B81CE182B434A04007EC76D /* Products */ = {
			isa = PBXGroup;
			children = (
				7B81CE262B434A04007EC76D /* SDL2.framework */,
				7B81CE282B434A04007EC76D /* SDL2.framework */,
				7B81CE2A2B434A04007EC76D /* SDL2.framework */,
				7B81CE2C2B434A04007EC76D /* SDL2.framework */,
				7B81CE2E2B434A04007EC76D /* libSDL2.a */,
				7B81CE302B434A04007EC76D /* libSDL2.a */,
				7B81CE322B434A04007EC76D /* libSDL2.a */,
				7B81CE342B434A04007EC76D /* libSDL2.dylib */,
				7B81CE362B434A04007EC76D /* libSDL2.dylib */,
				7B81CE382B434A04007EC76D /* libSDL2.dylib */,
				7B81CE3A2B434A04007EC76D /* SDL2 */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7B8B6CB624452656001C08D6 /* MojoShader */ = {
			isa = PBXGroup;
			children = (
				7BC01C052B4348EC00941563 /* mojoshader_profile_glsl.c */,
				7BC01C032B4348CD00941563 /* mojoshader_opengl.c */,
				7BF94B9F275C046100050413 /* mojoshader_profile_spirv.c */,
				7BF94B9C275C044E00050413 /* mojoshader_vulkan.c */,
				7B8B6CC6244526A7001C08D6 /* mojoshader_profile_common.c */,
				7B8B6CB824452690001C08D6 /* mojoshader_common.c */,
				7B8B6CBB24452690001C08D6 /* mojoshader_effects.c */,
				7B8B6CBA24452690001C08D6 /* mojoshader.c */,
			);
			name = MojoShader;
			sourceTree = "<group>";
		};
		7B9905792B434B8E00AEA00E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		7BC01BFA2B4346D400941563 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		7B1CDD442190C0A200175C7B /* FNA3D-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B1CDD4E2190C0A200175C7B /* Build configuration list for PBXNativeTarget "FNA3D-iOS" */;
			buildPhases = (
				7B1CDD412190C0A200175C7B /* Sources */,
				7B1CDD422190C0A200175C7B /* Frameworks */,
				7B1CDD432190C0A200175C7B /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FNA3D-iOS";
			productName = "FAudio-iOS";
			productReference = 7B1CDD452190C0A200175C7B /* libFNA3D.a */;
			productType = "com.apple.product-type.library.static";
		};
		7B7E140C2190E0CB00616654 /* FNA3D-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B7E14132190E0CB00616654 /* Build configuration list for PBXNativeTarget "FNA3D-tvOS" */;
			buildPhases = (
				7B7E14092190E0CB00616654 /* Sources */,
				7B7E140A2190E0CB00616654 /* Frameworks */,
				7B7E140B2190E0CB00616654 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FNA3D-tvOS";
			productName = "FAudio-tv";
			productReference = 7B7E140D2190E0CB00616654 /* libFNA3D.a */;
			productType = "com.apple.product-type.library.static";
		};
		7BC01BFD2B4346D400941563 /* FNA3D-macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7BC01BFF2B4346D400941563 /* Build configuration list for PBXNativeTarget "FNA3D-macOS" */;
			buildPhases = (
				7BC01BFA2B4346D400941563 /* Headers */,
				7BC01BFB2B4346D400941563 /* Sources */,
				7BC01BFC2B4346D400941563 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FNA3D-macOS";
			productName = "FNA3D-macOS";
			productReference = 7BC01BFE2B4346D400941563 /* libFNA3D.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7B1CDD3D2190C0A200175C7B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					7B1CDD442190C0A200175C7B = {
						CreatedOnToolsVersion = 10.1;
					};
					7B7E140C2190E0CB00616654 = {
						CreatedOnToolsVersion = 10.1;
					};
					7BC01BFD2B4346D400941563 = {
						CreatedOnToolsVersion = 15.1;
					};
				};
			};
			buildConfigurationList = 7B1CDD402190C0A200175C7B /* Build configuration list for PBXProject "FNA3D" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 7B1CDD3C2190C0A200175C7B;
			productRefGroup = 7B1CDD462190C0A200175C7B /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 7B81CE182B434A04007EC76D /* Products */;
					ProjectRef = 7B81CE172B434A04007EC76D /* SDL.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				7B1CDD442190C0A200175C7B /* FNA3D-iOS */,
				7B7E140C2190E0CB00616654 /* FNA3D-tvOS */,
				7BC01BFD2B4346D400941563 /* FNA3D-macOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		7B81CE262B434A04007EC76D /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7B81CE252B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE282B434A04007EC76D /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7B81CE272B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE2A2B434A04007EC76D /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7B81CE292B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE2C2B434A04007EC76D /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7B81CE2B2B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE2E2B434A04007EC76D /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7B81CE2D2B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE302B434A04007EC76D /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7B81CE2F2B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE322B434A04007EC76D /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7B81CE312B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE342B434A04007EC76D /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7B81CE332B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE362B434A04007EC76D /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7B81CE352B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE382B434A04007EC76D /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7B81CE372B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7B81CE3A2B434A04007EC76D /* SDL2 */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.executable";
			path = SDL2;
			remoteRef = 7B81CE392B434A04007EC76D /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXSourcesBuildPhase section */
		7B1CDD412190C0A200175C7B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				021FF3972A4A764500A63B55 /* FNA3D_Memory.c in Sources */,
				7BF94BA0275C046100050413 /* mojoshader_profile_spirv.c in Sources */,
				7B8B6CC9244526A7001C08D6 /* mojoshader_profile_common.c in Sources */,
				7BF94B9A275C042200050413 /* FNA3D_Driver_Vulkan.c in Sources */,
				7B8B6CC224452690001C08D6 /* mojoshader.c in Sources */,
				7B8B6CC424452690001C08D6 /* mojoshader_effects.c in Sources */,
				02FD608F2A7B227300E1D7A5 /* FNA3D_CommandBuffer.c in Sources */,
				7BF94B9D275C044E00050413 /* mojoshader_vulkan.c in Sources */,
				7BF820702445254300736AB0 /* FNA3D.c in Sources */,
				7B8B6CBE24452690001C08D6 /* mojoshader_common.c in Sources */,
				7BF8207C2445254300736AB0 /* FNA3D_PipelineCache.c in Sources */,
				7BF820782445254300736AB0 /* FNA3D_Image.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E14092190E0CB00616654 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				021FF3982A4A764500A63B55 /* FNA3D_Memory.c in Sources */,
				7BF94BA1275C046100050413 /* mojoshader_profile_spirv.c in Sources */,
				7B8B6CCA244526A7001C08D6 /* mojoshader_profile_common.c in Sources */,
				7BF94B9B275C042200050413 /* FNA3D_Driver_Vulkan.c in Sources */,
				7B8B6CC324452690001C08D6 /* mojoshader.c in Sources */,
				7B8B6CC524452690001C08D6 /* mojoshader_effects.c in Sources */,
				02FD60902A7B227300E1D7A5 /* FNA3D_CommandBuffer.c in Sources */,
				7BF94B9E275C044E00050413 /* mojoshader_vulkan.c in Sources */,
				7BF820712445254300736AB0 /* FNA3D.c in Sources */,
				7B8B6CBF24452690001C08D6 /* mojoshader_common.c in Sources */,
				7BF8207D2445254300736AB0 /* FNA3D_PipelineCache.c in Sources */,
				7BF820792445254300736AB0 /* FNA3D_Image.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7BC01BFB2B4346D400941563 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BC01C0C2B4348F300941563 /* mojoshader_effects.c in Sources */,
				7BC01C0B2B4348F300941563 /* mojoshader_profile_common.c in Sources */,
				7BC01C0E2B4348F700941563 /* FNA3D_CommandBuffer.c in Sources */,
				7BC01C112B4348F700941563 /* FNA3D.c in Sources */,
				7BC01C0F2B4348F700941563 /* FNA3D_Image.c in Sources */,
				7BC01C122B4348F700941563 /* FNA3D_Memory.c in Sources */,
				7BC01C042B4348CD00941563 /* mojoshader_opengl.c in Sources */,
				7BC01C082B4348F300941563 /* mojoshader.c in Sources */,
				7BC01C0D2B4348F700941563 /* FNA3D_Driver_Vulkan.c in Sources */,
				7BC01C142B43490100941563 /* FNA3D_Driver_OpenGL.c in Sources */,
				7BC01C102B4348F700941563 /* FNA3D_PipelineCache.c in Sources */,
				7BC01C062B4348ED00941563 /* mojoshader_profile_glsl.c in Sources */,
				7BC01C072B4348F300941563 /* mojoshader_profile_spirv.c in Sources */,
				7BC01C0A2B4348F300941563 /* mojoshader_vulkan.c in Sources */,
				7BC01C092B4348F300941563 /* mojoshader_common.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7B1CDD4C2190C0A200175C7B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					FNA3D_DRIVER_VULKAN,
					MOJOSHADER_NO_VERSION_INCLUDE,
					MOJOSHADER_USE_SDL_STDLIB,
					MOJOSHADER_EFFECT_SUPPORT,
					MOJOSHADER_DEPTH_CLIPPING,
					MOJOSHADER_FLIP_RENDERTARGET,
					MOJOSHADER_XNA4_VERTEX_TEXTURES,
					"\"COMPILER_SUPPORT=0\"",
					"\"SUPPORT_PROFILE_ARB1=0\"",
					"\"SUPPORT_PROFILE_ARB1_NV=0\"",
					"\"SUPPORT_PROFILE_BYTECODE=0\"",
					"\"SUPPORT_PROFILE_D3D=0\"",
					"\"SUPPORT_PROFILE_GLSPIRV=0\"",
					"\"SUPPORT_PROFILE_METAL=0\"",
					"\"SUPPORT_PROFILE_SPIRV=1\"",
					"\"SUPPORT_PROFILE_HLSL=0\"",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../../SDL2/include,
					../mojoshader/,
					"../Vulkan-Headers/include",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = FNA3D;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		7B1CDD4D2190C0A200175C7B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					FNA3D_DRIVER_VULKAN,
					MOJOSHADER_NO_VERSION_INCLUDE,
					MOJOSHADER_USE_SDL_STDLIB,
					MOJOSHADER_EFFECT_SUPPORT,
					MOJOSHADER_DEPTH_CLIPPING,
					MOJOSHADER_FLIP_RENDERTARGET,
					MOJOSHADER_XNA4_VERTEX_TEXTURES,
					"\"COMPILER_SUPPORT=0\"",
					"\"SUPPORT_PROFILE_ARB1=0\"",
					"\"SUPPORT_PROFILE_ARB1_NV=0\"",
					"\"SUPPORT_PROFILE_BYTECODE=0\"",
					"\"SUPPORT_PROFILE_D3D=0\"",
					"\"SUPPORT_PROFILE_GLSPIRV=0\"",
					"\"SUPPORT_PROFILE_METAL=0\"",
					"\"SUPPORT_PROFILE_SPIRV=1\"",
					"\"SUPPORT_PROFILE_HLSL=0\"",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../../SDL2/include,
					../mojoshader/,
					"../Vulkan-Headers/include",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = FNA3D;
				TVOS_DEPLOYMENT_TARGET = 9.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7B1CDD4F2190C0A200175C7B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"\"SUPPORT_PROFILE_GLSL=0\"",
					"\"SUPPORT_PROFILE_GLSL120=0\"",
					"\"SUPPORT_PROFILE_GLSLES=0\"",
					"\"SUPPORT_PROFILE_GLSLES3=0\"",
					"$(inherited)",
				);
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7B1CDD502190C0A200175C7B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"\"SUPPORT_PROFILE_GLSL=0\"",
					"\"SUPPORT_PROFILE_GLSL120=0\"",
					"\"SUPPORT_PROFILE_GLSLES=0\"",
					"\"SUPPORT_PROFILE_GLSLES3=0\"",
					"$(inherited)",
				);
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7B7E14142190E0CB00616654 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"\"SUPPORT_PROFILE_GLSL=0\"",
					"\"SUPPORT_PROFILE_GLSL120=0\"",
					"\"SUPPORT_PROFILE_GLSLES=0\"",
					"\"SUPPORT_PROFILE_GLSLES3=0\"",
					"$(inherited)",
				);
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Debug;
		};
		7B7E14152190E0CB00616654 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"\"SUPPORT_PROFILE_GLSL=0\"",
					"\"SUPPORT_PROFILE_GLSL120=0\"",
					"\"SUPPORT_PROFILE_GLSLES=0\"",
					"\"SUPPORT_PROFILE_GLSLES3=0\"",
					"$(inherited)",
				);
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Release;
		};
		7BC01C002B4346D400941563 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 78E9RL28VG;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					FNA3D_DRIVER_OPENGL,
					"$(inherited)",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		7BC01C012B4346D400941563 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 78E9RL28VG;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					FNA3D_DRIVER_OPENGL,
					"$(inherited)",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7B1CDD402190C0A200175C7B /* Build configuration list for PBXProject "FNA3D" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B1CDD4C2190C0A200175C7B /* Debug */,
				7B1CDD4D2190C0A200175C7B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B1CDD4E2190C0A200175C7B /* Build configuration list for PBXNativeTarget "FNA3D-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B1CDD4F2190C0A200175C7B /* Debug */,
				7B1CDD502190C0A200175C7B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B7E14132190E0CB00616654 /* Build configuration list for PBXNativeTarget "FNA3D-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B7E14142190E0CB00616654 /* Debug */,
				7B7E14152190E0CB00616654 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7BC01BFF2B4346D400941563 /* Build configuration list for PBXNativeTarget "FNA3D-macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7BC01C002B4346D400941563 /* Debug */,
				7BC01C012B4346D400941563 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7B1CDD3D2190C0A200175C7B /* Project object */;
}
