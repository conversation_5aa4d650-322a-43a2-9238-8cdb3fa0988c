using System.Reflection;
using System.Runtime.CompilerServices;
[assembly: AssemblyVersion("4.0.0.0")]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.Keyboard))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.Mouse))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentSerializerAttribute))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentSerializerCollectionItemNameAttribute))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentSerializerIgnoreAttribute))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentSerializerTypeVersionAttribute))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentSerializerRuntimeTypeAttribute))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentLoadException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentManager))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ResourceContentManager))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentTypeReader))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentTypeReader<int>))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.FrameworkDispatcher))]
#if TODO_MEDIASTUB
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaLibrary))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaSourceType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaSource))]
#endif
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.VisualizationData))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.TitleContainer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.MicrophoneState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.Microphone))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.AudioChannels))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.SoundEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.SoundState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.SoundEffectInstance))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.DynamicSoundEffectInstance))]
#if TODO_MEDIASTUB
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Album))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.AlbumCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Artist))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.ArtistCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Genre))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.GenreCollection))]
#endif
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaPlayer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.MediaQueue))]
#if TODO_MEDIASTUB
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Picture))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.PictureAlbum))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.PictureAlbumCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.PictureCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Playlist))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.PlaylistCollection))]
#endif
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.Song))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Media.SongCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentReader))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Content.ContentTypeReaderManager))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.AudioEmitter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.AudioListener))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.NoMicrophoneConnectedException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.PlayerIndex))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.Buttons))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.ButtonState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePad))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadDeadZone))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.Keys))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.KeyState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.KeyboardState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.MouseState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadButtons))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadDPad))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadThumbSticks))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadTriggers))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Input.GamePadCapabilities))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.MathHelper))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.MathTypeConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.PointConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.RectangleConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.Vector2Converter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.Vector3Converter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.Vector4Converter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.QuaternionConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.MatrixConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.BoundingBoxConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.BoundingSphereConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.PlaneConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.RayConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Design.ColorConverter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.IPackedVector))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.IPackedVector<int>))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Alpha8))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Bgr565))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Bgra5551))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Bgra4444))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Byte4))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.HalfSingle))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.HalfVector2))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.HalfVector4))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.NormalizedByte2))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.NormalizedByte4))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.NormalizedShort2))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.NormalizedShort4))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Rg32))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Rgba1010102))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Rgba64))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Short2))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PackedVector.Short4))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.DisplayOrientation))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.NoAudioHardwareException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Audio.InstancePlayLimitException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.ContainmentType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.BoundingBox))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.BoundingFrustum))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.BoundingSphere))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.CurveLoopType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.CurveTangent))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Curve))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.CurveContinuity))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.CurveKey))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.CurveKeyCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Matrix))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Color))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.PlaneIntersectionType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Plane))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Point))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Quaternion))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Ray))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Rectangle))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Vector2))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Vector3))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Vector4))]
