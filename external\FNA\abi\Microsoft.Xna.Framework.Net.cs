using System.Reflection;
using System.Runtime.CompilerServices;
[assembly: AssemblyVersion("4.0.0.0")]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionEndedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.GamerJoinedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.GamerLeftEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.GameStartedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.GameEndedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.HostChangedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.WriteLeaderboardsEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.PacketReader))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.PacketWriter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.QualityOfService))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionEndReason))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.SendDataOptions))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.AvailableNetworkSession))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.AvailableNetworkSessionCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkGamer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.LocalNetworkGamer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkMachine))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSession))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionProperties))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionJoinError))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Net.NetworkSessionJoinException))]
