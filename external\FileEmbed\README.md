# C# FileEmbed

A source generator for embedding resource files directly into your assembly. Access them as a `ReadOnlySpan<byte>`, with no allocations or reflection needed.

Add the package to your application using

```
dotnet add package NFH.FileEmbed
```

You will probably wish to add `PrivateAssets="all"` to the `PackageReference` for this package in your csproj, to prevent this becoming a dependency (as it's only needed at compile time).

## Example Usage
```csharp
//attribute is in this namespace
using FileEmbed;

namespace EmbedExample;

//partial methods must be in partial types
public static partial class Program
{
    //Place the attribute on a static partial method that returns a ReadOnlySpan<byte>
    [FileEmbed(@"Capture.PNG")]
    public static partial ReadOnlySpan<byte> Bytes();


    //works in any type that can contain a static method
    private partial record struct MyStruct
    {
        //Path is relative to your project directory (specifically, the ProjectDir MSBuild property)
        [FileEmbed(@"Resources\Capture.7z")]
        internal static partial ReadOnlySpan<byte> StructBytes();
    }

    public partial interface IExampleInterface
    {
        //two optional arguments, Offset and Length, allow you to embed a slice of the file
        [FileEmbed(@"Resources\Capture.7z", 4, 8)]
        internal static partial ReadOnlySpan<byte> InterfaceBytes();
    }

    public static void Main()
    {
        Console.WriteLine($"{Bytes().Length} bytes");
        Console.WriteLine($"{MyStruct.StructBytes().Length} bytes");
        Console.WriteLine($"{IExampleInterface.InterfaceBytes().Length} bytes");
    }
}
```
Autogenerated code for `IExampleInterface.InterfaceBytes`:
```csharp
namespace EmbedExample
{
	partial class Program
	{
		partial interface IExampleInterface
		{
			[global::System.CodeDom.Compiler.GeneratedCodeAttribute("FileEmbed", "0.1.0.0")]
			internal static partial global::System.ReadOnlySpan<byte> InterfaceBytes() => new byte[] { 39,28,0,4,115,228,9,158, };
		}
	}
}
```

## Limitations
This doesn't work so well for input files larger than 1MB unfortunately. The source generator itself can handle large files just fine, but, in my experience, Visual Studio chokes on the generated output, using huge amounts of memory after the files are generated. I have not yet investigated why. To protect users of this source generator from accidently locking up VS, it imposes a 1MB limit on input files. This can be overriden if you wish to try using this with larger files. To do so, put this in your csproj file:
```xml
<!-- To set FileEmbed_MaxEmbedSize, you must first make it visible to the compiler -->
<ItemGroup>
	<CompilerVisibleProperty Include="FileEmbed_MaxEmbedSize" />
</ItemGroup>
<PropertyGroup>
	<FileEmbed_MaxEmbedSize>SIZE_IN_BYTES_GOES_HERE</FileEmbed_MaxEmbedSize>
</PropertyGroup>
```
