name: CI

on: [push, pull_request]

jobs:
  linux:
    name: Linux
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true

    - name: Build
      run: make

  macos:
    name: macOS
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true

    - name: Build
      run: make

  windows-mingw:
    name: Windows (MinGW)
    runs-on: windows-latest
    defaults:
      run:
        shell: msys2 {0}
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true

    - name: Set up MSYS2
      uses: msys2/setup-msys2@v2
      with:
        msystem: mingw32
        install: >-
          mingw-w64-i686-cc
          mingw-w64-i686-make

    - name: Build
      run: mingw32-make

  freebsd:
    runs-on: ubuntu-latest
    name: FreeBSD
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true
    - name: Build
      uses: cross-platform-actions/action@v0.19.1
      with:
        operating_system: freebsd
        version: '13.2'
        run: |
          sudo pkg update
          sudo pkg install -y gmake
          gmake
