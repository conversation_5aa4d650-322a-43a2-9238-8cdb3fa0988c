using System.Reflection;
using System.Runtime.CompilerServices;
[assembly: AssemblyVersion("4.0.0.0")]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.RenderTargetBinding))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexBufferBinding))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.GraphicsDevice))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IGraphicsDeviceService))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.GraphicsResource))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.BlendState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DepthStencilState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectAnnotation))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectAnnotationCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectPass))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectPassCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectParameter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectParameterCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectTechnique))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectTechniqueCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Effect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Texture))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.TextureCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Texture2D))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.TextureCube))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Texture3D))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SamplerState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SamplerStateCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.GraphicsAdapter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.RasterizerState))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexDeclaration))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IVertexType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexBuffer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DynamicVertexBuffer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IndexBuffer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DynamicIndexBuffer))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.RenderTarget2D))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.RenderTargetCube))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.OcclusionQuery))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Model))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelBone))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelBoneCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelEffectCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelMesh))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelMeshCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelMeshPart))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ModelMeshPartCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectMaterial))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IEffectMatrices))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IEffectLights))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IEffectFog))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SpriteFont))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SpriteBatch))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DirectionalLight))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.BasicEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DualTextureEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.AlphaTestEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EnvironmentMapEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexPositionColor))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexPositionTexture))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexPositionColorTexture))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexPositionNormalTexture))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SkinnedEffect))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexElement))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DisplayMode))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DisplayModeCollection))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Viewport))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ResourceCreatedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ResourceDestroyedEventArgs))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PresentationParameters))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DeviceLostException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DeviceNotResetException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.NoSuitableGraphicsDeviceException))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.GraphicsProfile))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SurfaceFormat))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.DepthFormat))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.GraphicsDeviceStatus))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PresentInterval))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ClearOptions))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.RenderTargetUsage))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SetDataOptions))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.CubeMapFace))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.BufferUsage))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.IndexElementSize))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.PrimitiveType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexElementFormat))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.VertexElementUsage))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.TextureFilter))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectParameterClass))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.EffectParameterType))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.BlendFunction))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.Blend))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.CompareFunction))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.ColorWriteChannels))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.CullMode))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.StencilOperation))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.FillMode))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.TextureAddressMode))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SpriteEffects))]
[assembly: TypeForwardedToAttribute(typeof(Microsoft.Xna.Framework.Graphics.SpriteSortMode))]