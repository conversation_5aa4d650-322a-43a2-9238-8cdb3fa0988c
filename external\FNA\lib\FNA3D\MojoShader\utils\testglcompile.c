// A simple program to see if the GL implementation will accept a shader
//  generated by <PERSON><PERSON><PERSON><PERSON><PERSON>, as they could also fail to compile what
//  we produce.

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

#include "SDL.h"
#include "mojoshader.h"

static void * MOJOSHADERCALL get_proc_addr(const char *fnname, void *data)
{
    return SDL_GL_GetProcAddress(fnname);
}

int main(int argc, char **argv)
{
    int retval = 1;
    SDL_Window *sdlwindow = NULL;
    MOJOSHADER_glContext *mojo = NULL;
    int i;

    printf("MojoShader testglcompile\n");
    printf("Compiled against changeset %s\n", MOJOSHADER_CHANGESET);
    printf("Linked against changeset %s\n", MOJOSHADER_changeset());
    printf("\n");

    if (argc <= 2)
        printf("\n\nUSAGE: %s <profile> [file1] ... [fileN]\n\n", argv[0]);
    else if (SDL_Init(SDL_INIT_VIDEO) == -1)
        fprintf(stderr, "SDL_Init() error: %s\n", SDL_GetError());
    else if (SDL_GL_LoadLibrary(NULL) == -1)
        fprintf(stderr, "SDL_GL_LoadLibrary() error: %s\n", SDL_GetError());
    else if ((sdlwindow = SDL_CreateWindow(argv[0], SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED, 640, 480, SDL_WINDOW_OPENGL)) == NULL)
        fprintf(stderr, "SDL_CreateWindow() error: %s\n", SDL_GetError());
    else if (SDL_GL_CreateContext(sdlwindow) == NULL)
        fprintf(stderr, "SDL_GL_CreateContext() error: %s\n", SDL_GetError());
    else if ((mojo = MOJOSHADER_glCreateContext(argv[1], get_proc_addr, NULL, NULL, NULL, NULL)) == NULL)
        fprintf(stderr, "MOJOSHADER_glCreateContext() error: %s\n", MOJOSHADER_glGetError());
    else
        retval = 0;

    if (retval != 0)
    {
        SDL_Quit();
        return retval;
    } // if

    MOJOSHADER_glMakeContextCurrent(mojo);

    for (i = 2; i < argc; i++) {
        const char *fname = argv[i];
        static unsigned char buffer[1024 * 512];
        MOJOSHADER_glShader *shader;
        MOJOSHADER_glProgram *program;
        const MOJOSHADER_parseData *pd;
        int isvshader;
        FILE *io;
        size_t br;

        io = fopen(fname, "rb");
        if (!io) {
            fprintf(stderr, "Failed to open %s: %s\n", fname, strerror(errno));
            retval = 1;
            continue;
        }

        br = fread(buffer, 1, sizeof (buffer), io);
        fclose(io);

        shader = MOJOSHADER_glCompileShader(buffer, (unsigned int) br, NULL, 0, NULL, 0);
        if (!shader) {
            fprintf(stderr, "Compiling %s failed: %s\n", fname, MOJOSHADER_glGetError());
            retval = 1;
            continue;
        }

        shader = MOJOSHADER_glCompileShader(buffer, (unsigned int) br, NULL, 0, NULL, 0);
        if (!shader) {
            fprintf(stderr, "Compiling %s failed: %s\n", fname, MOJOSHADER_glGetError());
            retval = 1;
            continue;
        }

        pd = MOJOSHADER_glGetShaderParseData(shader);
        isvshader = (pd->shader_type == MOJOSHADER_TYPE_VERTEX);
        program = MOJOSHADER_glLinkProgram(isvshader ? shader : NULL, isvshader ? NULL : shader);
        if (!program) {
            fprintf(stderr, "Linking %s failed: %s\n", fname, MOJOSHADER_glGetError());
            retval = 1;
            MOJOSHADER_glDeleteShader(shader);
            continue;
        }

        printf("%s: okay\n", fname);

        MOJOSHADER_glDeleteProgram(program);
        MOJOSHADER_glDeleteShader(shader);
    }

    MOJOSHADER_glDestroyContext(mojo);
    SDL_Quit();

    return retval;
}

// end of testglcompile.c ...

