/* Generated by re2c 3.0 */
/**
 * MojoShader; generate shader programs from bytecode of compiled
 *  Direct3D shaders.
 *
 * Please see the file LICENSE.txt in the source's root directory.
 *
 *  This file written by <PERSON>.
 */

// This was originally based on examples/pp-c.re from re2c: http://re2c.org/
//   re2c is public domain code.
//
// You build mojoshader_lexer.c from the .re file with re2c...
// re2c -is -o mojoshader_lexer.c mojoshader_lexer.re
//
// Changes to the lexer are done to the .re file, not the C code!
//
// Please note that this isn't a perfect C lexer, since it is used for both
//  HLSL and shader assembly language, and follows the quirks of Microsoft's
//  tools.

#define __MOJOSHADER_INTERNAL__ 1
#include "mojoshader_internal.h"

typedef unsigned char uchar;

#define YYMAXFILL 8

#define RET(t) return update_state(s, eoi, cursor, token, (Token) t)
#define YYCTYPE uchar
#define YYCURSOR cursor
#define YYLIMIT limit
#define YYMARKER s->lexer_marker
#define YYFILL(n) { if ((n) == 1) { cursor = sentinel; limit = cursor + YYMAXFILL; eoi = 1; } }

static uchar sentinel[YYMAXFILL];

static Token update_state(IncludeState *s, int eoi, const uchar *cur,
                          const uchar *tok, const Token val)
{
    if (eoi)
    {
        s->bytes_left = 0;
        s->source = (const char *) s->source_base + s->orig_length;
        if ( (tok >= sentinel) && (tok < (sentinel+YYMAXFILL)) )
            s->token = s->source;
        else
            s->token = (const char *) tok;
    } // if
    else
    {
        s->bytes_left -= (unsigned int) (cur - ((const uchar *) s->source));
        s->source = (const char *) cur;
        s->token = (const char *) tok;
    } // else
    s->tokenlen = (unsigned int) (s->source - s->token);
    s->tokenval = val;
    return val;
} // update_state

Token preprocessor_lexer(IncludeState *s)
{
    const uchar *cursor = (const uchar *) s->source;
    const uchar *token = cursor;
    const uchar *matchptr;
    const uchar *limit = cursor + s->bytes_left;
    int eoi = 0;



    // preprocessor directives are only valid at start of line.
    if (s->tokenval == ((Token) '\n'))
        goto ppdirective;  // may jump back to scanner_loop.

scanner_loop:
    if (YYLIMIT == YYCURSOR) YYFILL(1);
    token = cursor;


{
	YYCTYPE yych;
	unsigned int yyaccept = 0;
	if ((YYLIMIT - YYCURSOR) < 4) YYFILL(4);
	yych = *YYCURSOR;
	switch (yych) {
		case 0x00: goto yy1;
		case '\t':
		case '\v':
		case '\f':
		case ' ': goto yy4;
		case '\n': goto yy5;
		case '\r': goto yy7;
		case '!': goto yy8;
		case '"': goto yy9;
		case '#': goto yy10;
		case '%': goto yy11;
		case '&': goto yy12;
		case '\'': goto yy13;
		case '(': goto yy14;
		case ')': goto yy15;
		case '*': goto yy16;
		case '+': goto yy17;
		case ',': goto yy18;
		case '-': goto yy19;
		case '.': goto yy20;
		case '/': goto yy22;
		case '0': goto yy23;
		case '1':
		case '2':
		case '3':
		case '4':
		case '5':
		case '6':
		case '7':
		case '8':
		case '9': goto yy25;
		case ':': goto yy27;
		case ';': goto yy28;
		case '<': goto yy29;
		case '=': goto yy31;
		case '>': goto yy32;
		case '?': goto yy34;
		case 'A':
		case 'B':
		case 'C':
		case 'D':
		case 'E':
		case 'F':
		case 'G':
		case 'H':
		case 'I':
		case 'J':
		case 'K':
		case 'L':
		case 'M':
		case 'N':
		case 'O':
		case 'P':
		case 'Q':
		case 'R':
		case 'S':
		case 'T':
		case 'U':
		case 'V':
		case 'W':
		case 'X':
		case 'Y':
		case 'Z':
		case '_':
		case 'a':
		case 'b':
		case 'c':
		case 'd':
		case 'e':
		case 'f':
		case 'g':
		case 'h':
		case 'i':
		case 'j':
		case 'k':
		case 'l':
		case 'm':
		case 'n':
		case 'o':
		case 'p':
		case 'q':
		case 'r':
		case 's':
		case 't':
		case 'u':
		case 'v':
		case 'w':
		case 'x':
		case 'y':
		case 'z': goto yy35;
		case '[': goto yy37;
		case '\\': goto yy38;
		case ']': goto yy39;
		case '^': goto yy40;
		case '{': goto yy41;
		case '|': goto yy42;
		case '}': goto yy43;
		case '~': goto yy44;
		default: goto yy2;
	}
yy1:
	++YYCURSOR;
	{ if (eoi) { RET(TOKEN_EOI); } goto bad_chars; }
yy2:
	++YYCURSOR;
yy3:
	{ goto bad_chars; }
yy4:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= '\n') {
		if (yych == '\t') goto yy4;
	} else {
		if (yych <= '\f') goto yy4;
		if (yych == ' ') goto yy4;
	}
	{ if (s->report_whitespace) RET(' '); goto scanner_loop; }
yy5:
	++YYCURSOR;
yy6:
	{ s->line++; RET('\n'); }
yy7:
	yych = *++YYCURSOR;
	if (yych == '\n') goto yy5;
	goto yy6;
yy8:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy45;
	{ RET('!'); }
yy9:
	yyaccept = 0;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych == '\n') goto yy3;
	if (yych == '\r') goto yy3;
	goto yy47;
yy10:
	yych = *++YYCURSOR;
	if (yych == '#') goto yy51;
	{ RET(TOKEN_HASH); }
yy11:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy52;
	{ RET('%'); }
yy12:
	yych = *++YYCURSOR;
	if (yych == '&') goto yy53;
	if (yych == '=') goto yy54;
	{ RET('&'); }
yy13:
	yyaccept = 0;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych == '\n') goto yy3;
	if (yych == '\r') goto yy3;
	goto yy56;
yy14:
	++YYCURSOR;
	{ RET('('); }
yy15:
	++YYCURSOR;
	{ RET(')'); }
yy16:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy58;
	{ RET('*'); }
yy17:
	yych = *++YYCURSOR;
	if (yych == '+') goto yy59;
	if (yych == '=') goto yy60;
	{ RET('+'); }
yy18:
	++YYCURSOR;
	{ RET(','); }
yy19:
	yych = *++YYCURSOR;
	if (yych == '-') goto yy61;
	if (yych == '=') goto yy62;
	{ RET('-'); }
yy20:
	yych = *++YYCURSOR;
	if (yych <= '/') goto yy21;
	if (yych <= '9') goto yy63;
yy21:
	{ RET('.'); }
yy22:
	yych = *++YYCURSOR;
	if (yych <= '.') {
		if (yych == '*') goto yy65;
	} else {
		if (yych <= '/') goto yy66;
		if (yych == '=') goto yy67;
	}
	{ RET('/'); }
yy23:
	yyaccept = 1;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych == 'X') goto yy70;
	if (yych == 'x') goto yy70;
	goto yy26;
yy24:
	{ RET(TOKEN_INT_LITERAL); }
yy25:
	yyaccept = 1;
	YYMARKER = ++YYCURSOR;
	if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
	yych = *YYCURSOR;
yy26:
	if (yych <= 'L') {
		if (yych <= '9') {
			if (yych == '.') goto yy63;
			if (yych <= '/') goto yy24;
			goto yy25;
		} else {
			if (yych == 'E') goto yy68;
			if (yych <= 'K') goto yy24;
			goto yy69;
		}
	} else {
		if (yych <= 'e') {
			if (yych == 'U') goto yy69;
			if (yych <= 'd') goto yy24;
			goto yy68;
		} else {
			if (yych <= 'l') {
				if (yych <= 'k') goto yy24;
				goto yy69;
			} else {
				if (yych == 'u') goto yy69;
				goto yy24;
			}
		}
	}
yy27:
	++YYCURSOR;
	{ RET(':'); }
yy28:
	++YYCURSOR;
	{ if (s->asm_comments) goto singlelinecomment; RET(';'); }
yy29:
	yych = *++YYCURSOR;
	if (yych <= ';') goto yy30;
	if (yych <= '<') goto yy71;
	if (yych <= '=') goto yy72;
yy30:
	{ RET('<'); }
yy31:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy73;
	{ RET('='); }
yy32:
	yych = *++YYCURSOR;
	if (yych <= '<') goto yy33;
	if (yych <= '=') goto yy74;
	if (yych <= '>') goto yy75;
yy33:
	{ RET('>'); }
yy34:
	++YYCURSOR;
	{ RET('?'); }
yy35:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'Z') {
		if (yych <= '/') goto yy36;
		if (yych <= '9') goto yy35;
		if (yych >= 'A') goto yy35;
	} else {
		if (yych <= '_') {
			if (yych >= '_') goto yy35;
		} else {
			if (yych <= '`') goto yy36;
			if (yych <= 'z') goto yy35;
		}
	}
yy36:
	{ RET(TOKEN_IDENTIFIER); }
yy37:
	++YYCURSOR;
	{ RET('['); }
yy38:
	yyaccept = 0;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych <= 0x08) goto yy3;
	if (yych <= '\r') goto yy77;
	if (yych == ' ') goto yy77;
	goto yy3;
yy39:
	++YYCURSOR;
	{ RET(']'); }
yy40:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy81;
	{ RET('^'); }
yy41:
	++YYCURSOR;
	{ RET('{'); }
yy42:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy82;
	if (yych == '|') goto yy83;
	{ RET('|'); }
yy43:
	++YYCURSOR;
	{ RET('}'); }
yy44:
	++YYCURSOR;
	{ RET('~'); }
yy45:
	++YYCURSOR;
	{ RET(TOKEN_NEQ); }
yy46:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
yy47:
	if (yych <= '\r') {
		if (yych == '\n') goto yy48;
		if (yych <= '\f') goto yy46;
	} else {
		if (yych <= '"') {
			if (yych <= '!') goto yy46;
			goto yy49;
		} else {
			if (yych == '\\') goto yy50;
			goto yy46;
		}
	}
yy48:
	YYCURSOR = YYMARKER;
	if (yyaccept <= 1) {
		if (yyaccept == 0) {
			goto yy3;
		} else {
			goto yy24;
		}
	} else {
		goto yy64;
	}
yy49:
	++YYCURSOR;
	{ RET(TOKEN_STRING_LITERAL); }
yy50:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'b') {
		if (yych <= '7') {
			if (yych <= '&') {
				if (yych == '"') goto yy46;
				goto yy48;
			} else {
				if (yych <= '\'') goto yy46;
				if (yych <= '/') goto yy48;
				goto yy46;
			}
		} else {
			if (yych <= '[') {
				if (yych == '?') goto yy46;
				goto yy48;
			} else {
				if (yych <= '\\') goto yy46;
				if (yych <= '`') goto yy48;
				goto yy46;
			}
		}
	} else {
		if (yych <= 'r') {
			if (yych <= 'm') {
				if (yych == 'f') goto yy46;
				goto yy48;
			} else {
				if (yych <= 'n') goto yy46;
				if (yych <= 'q') goto yy48;
				goto yy46;
			}
		} else {
			if (yych <= 'u') {
				if (yych == 't') goto yy46;
				goto yy48;
			} else {
				if (yych <= 'v') goto yy46;
				if (yych == 'x') goto yy84;
				goto yy48;
			}
		}
	}
yy51:
	++YYCURSOR;
	{ RET(TOKEN_HASHHASH); }
yy52:
	++YYCURSOR;
	{ RET(TOKEN_MODASSIGN); }
yy53:
	++YYCURSOR;
	{ RET(TOKEN_ANDAND); }
yy54:
	++YYCURSOR;
	{ RET(TOKEN_ANDASSIGN); }
yy55:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
yy56:
	if (yych <= '\r') {
		if (yych == '\n') goto yy48;
		if (yych <= '\f') goto yy55;
		goto yy48;
	} else {
		if (yych <= '\'') {
			if (yych <= '&') goto yy55;
		} else {
			if (yych == '\\') goto yy57;
			goto yy55;
		}
	}
	++YYCURSOR;
	goto yy24;
yy57:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'b') {
		if (yych <= '7') {
			if (yych <= '&') {
				if (yych == '"') goto yy55;
				goto yy48;
			} else {
				if (yych <= '\'') goto yy55;
				if (yych <= '/') goto yy48;
				goto yy55;
			}
		} else {
			if (yych <= '[') {
				if (yych == '?') goto yy55;
				goto yy48;
			} else {
				if (yych <= '\\') goto yy55;
				if (yych <= '`') goto yy48;
				goto yy55;
			}
		}
	} else {
		if (yych <= 'r') {
			if (yych <= 'm') {
				if (yych == 'f') goto yy55;
				goto yy48;
			} else {
				if (yych <= 'n') goto yy55;
				if (yych <= 'q') goto yy48;
				goto yy55;
			}
		} else {
			if (yych <= 'u') {
				if (yych == 't') goto yy55;
				goto yy48;
			} else {
				if (yych <= 'v') goto yy55;
				if (yych == 'x') goto yy85;
				goto yy48;
			}
		}
	}
yy58:
	++YYCURSOR;
	{ RET(TOKEN_MULTASSIGN); }
yy59:
	++YYCURSOR;
	{ RET(TOKEN_INCREMENT); }
yy60:
	++YYCURSOR;
	{ RET(TOKEN_ADDASSIGN); }
yy61:
	++YYCURSOR;
	{ RET(TOKEN_DECREMENT); }
yy62:
	++YYCURSOR;
	{ RET(TOKEN_SUBASSIGN); }
yy63:
	yyaccept = 2;
	YYMARKER = ++YYCURSOR;
	if ((YYLIMIT - YYCURSOR) < 3) YYFILL(3);
	yych = *YYCURSOR;
	if (yych <= 'G') {
		if (yych <= 'D') {
			if (yych <= '/') goto yy64;
			if (yych <= '9') goto yy63;
		} else {
			if (yych <= 'E') goto yy68;
			if (yych <= 'F') goto yy86;
		}
	} else {
		if (yych <= 'e') {
			if (yych <= 'H') goto yy86;
			if (yych >= 'e') goto yy68;
		} else {
			if (yych == 'g') goto yy64;
			if (yych <= 'h') goto yy86;
		}
	}
yy64:
	{ RET(TOKEN_FLOAT_LITERAL); }
yy65:
	++YYCURSOR;
	{ goto multilinecomment; }
yy66:
	++YYCURSOR;
	{ goto singlelinecomment; }
yy67:
	++YYCURSOR;
	{ RET(TOKEN_DIVASSIGN); }
yy68:
	yych = *++YYCURSOR;
	if (yych <= ',') {
		if (yych == '+') goto yy87;
		goto yy48;
	} else {
		if (yych <= '-') goto yy87;
		if (yych <= '/') goto yy48;
		if (yych <= '9') goto yy88;
		goto yy48;
	}
yy69:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'U') {
		if (yych == 'L') goto yy69;
		if (yych <= 'T') goto yy24;
		goto yy69;
	} else {
		if (yych <= 'l') {
			if (yych <= 'k') goto yy24;
			goto yy69;
		} else {
			if (yych == 'u') goto yy69;
			goto yy24;
		}
	}
yy70:
	yych = *++YYCURSOR;
	if (yych <= '@') {
		if (yych <= '/') goto yy48;
		if (yych <= '9') goto yy89;
		goto yy48;
	} else {
		if (yych <= 'F') goto yy89;
		if (yych <= '`') goto yy48;
		if (yych <= 'f') goto yy89;
		goto yy48;
	}
yy71:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy90;
	{ RET(TOKEN_LSHIFT); }
yy72:
	++YYCURSOR;
	{ RET(TOKEN_LEQ); }
yy73:
	++YYCURSOR;
	{ RET(TOKEN_EQL); }
yy74:
	++YYCURSOR;
	{ RET(TOKEN_GEQ); }
yy75:
	yych = *++YYCURSOR;
	if (yych == '=') goto yy91;
	{ RET(TOKEN_RSHIFT); }
yy76:
	++YYCURSOR;
	if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
	yych = *YYCURSOR;
yy77:
	if (yych <= '\f') {
		if (yych <= 0x08) goto yy48;
		if (yych != '\n') goto yy76;
	} else {
		if (yych <= '\r') goto yy80;
		if (yych == ' ') goto yy76;
		goto yy48;
	}
yy78:
	++YYCURSOR;
yy79:
	{ s->line++; goto scanner_loop; }
yy80:
	yych = *++YYCURSOR;
	if (yych == '\n') goto yy78;
	goto yy79;
yy81:
	++YYCURSOR;
	{ RET(TOKEN_XORASSIGN); }
yy82:
	++YYCURSOR;
	{ RET(TOKEN_ORASSIGN); }
yy83:
	++YYCURSOR;
	{ RET(TOKEN_OROR); }
yy84:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= '@') {
		if (yych <= '/') goto yy48;
		if (yych <= '9') goto yy46;
		goto yy48;
	} else {
		if (yych <= 'F') goto yy46;
		if (yych <= '`') goto yy48;
		if (yych <= 'f') goto yy46;
		goto yy48;
	}
yy85:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= '@') {
		if (yych <= '/') goto yy48;
		if (yych <= '9') goto yy55;
		goto yy48;
	} else {
		if (yych <= 'F') goto yy55;
		if (yych <= '`') goto yy48;
		if (yych <= 'f') goto yy55;
		goto yy48;
	}
yy86:
	++YYCURSOR;
	goto yy64;
yy87:
	yych = *++YYCURSOR;
	if (yych <= '/') goto yy48;
	if (yych >= ':') goto yy48;
yy88:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'G') {
		if (yych <= '9') {
			if (yych <= '/') goto yy64;
			goto yy88;
		} else {
			if (yych == 'F') goto yy86;
			goto yy64;
		}
	} else {
		if (yych <= 'f') {
			if (yych <= 'H') goto yy86;
			if (yych <= 'e') goto yy64;
			goto yy86;
		} else {
			if (yych == 'h') goto yy86;
			goto yy64;
		}
	}
yy89:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= 'T') {
		if (yych <= '@') {
			if (yych <= '/') goto yy24;
			if (yych <= '9') goto yy89;
			goto yy24;
		} else {
			if (yych <= 'F') goto yy89;
			if (yych == 'L') goto yy69;
			goto yy24;
		}
	} else {
		if (yych <= 'k') {
			if (yych <= 'U') goto yy69;
			if (yych <= '`') goto yy24;
			if (yych <= 'f') goto yy89;
			goto yy24;
		} else {
			if (yych <= 'l') goto yy69;
			if (yych == 'u') goto yy69;
			goto yy24;
		}
	}
yy90:
	++YYCURSOR;
	{ RET(TOKEN_LSHIFTASSIGN); }
yy91:
	++YYCURSOR;
	{ RET(TOKEN_RSHIFTASSIGN); }
}


multilinecomment:
    if (YYLIMIT == YYCURSOR) YYFILL(1);
    matchptr = cursor;
// The "*\/" is just to avoid screwing up text editor syntax highlighting.

{
	YYCTYPE yych;
	if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
	yych = *YYCURSOR;
	if (yych <= '\f') {
		if (yych <= 0x00) goto yy93;
		if (yych == '\n') goto yy96;
		goto yy94;
	} else {
		if (yych <= '\r') goto yy98;
		if (yych == '*') goto yy99;
		goto yy94;
	}
yy93:
	++YYCURSOR;
	{
                        if (eoi)
                            RET(TOKEN_INCOMPLETE_COMMENT);
                        goto multilinecomment;
                    }
yy94:
	++YYCURSOR;
yy95:
	{ goto multilinecomment; }
yy96:
	++YYCURSOR;
yy97:
	{
                        s->line++;
                        goto multilinecomment;
                    }
yy98:
	yych = *++YYCURSOR;
	if (yych == '\n') goto yy96;
	goto yy97;
yy99:
	yych = *++YYCURSOR;
	if (yych != '/') goto yy95;
	++YYCURSOR;
	{
                        if (s->report_comments)
                            RET(TOKEN_MULTI_COMMENT);
                        else if (s->report_whitespace)
                            RET(' ');

                        // Microsoft's preprocessor allows multiline comments
                        //  before a preprocessor directive, even though C/C++
                        //  doesn't. See if we've hit this case.
                        #if MATCH_MICROSOFT_PREPROCESSOR
                        if (s->tokenval == ((Token) '\n'))  // was start of line?
                        {
                            update_state(s, eoi, cursor, token, (Token) '\n');
                            goto ppdirective;  // may jump back to scanner_loop.
                        }
                        #endif

                        goto scanner_loop;
                    }
}


singlelinecomment:
    if (YYLIMIT == YYCURSOR) YYFILL(1);
    matchptr = cursor;

{
	YYCTYPE yych;
	if ((YYLIMIT - YYCURSOR) < 2) YYFILL(2);
	yych = *YYCURSOR;
	if (yych <= '\n') {
		if (yych <= 0x00) goto yy101;
		if (yych <= '\t') goto yy102;
		goto yy103;
	} else {
		if (yych == '\r') goto yy105;
		goto yy102;
	}
yy101:
	++YYCURSOR;
	{
                        if (eoi)
                        {
                            if (s->report_comments)
                                RET(TOKEN_SINGLE_COMMENT);
                            else
                                RET(TOKEN_EOI);
                        }
                        goto singlelinecomment;
                    }
yy102:
	++YYCURSOR;
	{ goto singlelinecomment; }
yy103:
	++YYCURSOR;
yy104:
	{
                        if (s->report_comments)
                        {
                            cursor = matchptr;  // so we RET('\n') next.
                            RET(TOKEN_SINGLE_COMMENT);
                        }
                        s->line++;
                        token = matchptr;
                        RET('\n');
                    }
yy105:
	yych = *++YYCURSOR;
	if (yych == '\n') goto yy103;
	goto yy104;
}


ppdirective:
    if (YYLIMIT == YYCURSOR) YYFILL(1);

{
	YYCTYPE yych;
	unsigned int yyaccept = 0;
	if ((YYLIMIT - YYCURSOR) < 8) YYFILL(8);
	yych = *YYCURSOR;
	if (yych <= '\f') {
		if (yych == '\t') goto yy108;
		if (yych >= '\v') goto yy108;
	} else {
		if (yych <= ' ') {
			if (yych >= ' ') goto yy108;
		} else {
			if (yych == '#') goto yy109;
		}
	}
	++YYCURSOR;
yy107:
	{
                            token = cursor = (const uchar *) s->source;
                            limit = cursor + s->bytes_left;
                            goto scanner_loop;
                        }
yy108:
	++YYCURSOR;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= '\n') {
		if (yych == '\t') goto yy108;
	} else {
		if (yych <= '\f') goto yy108;
		if (yych == ' ') goto yy108;
	}
	{ goto ppdirective; }
yy109:
	yyaccept = 0;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych <= 'h') {
		if (yych <= 0x1F) {
			if (yych == '\t') goto yy111;
			goto yy107;
		} else {
			if (yych <= ' ') goto yy111;
			if (yych <= 'c') goto yy107;
			if (yych <= 'e') goto yy111;
			goto yy107;
		}
	} else {
		if (yych <= 'o') {
			if (yych <= 'i') goto yy111;
			if (yych == 'l') goto yy111;
			goto yy107;
		} else {
			if (yych <= 'p') goto yy111;
			if (yych == 'u') goto yy111;
			goto yy107;
		}
	}
yy110:
	++YYCURSOR;
	if ((YYLIMIT - YYCURSOR) < 7) YYFILL(7);
	yych = *YYCURSOR;
yy111:
	if (yych <= 'h') {
		if (yych <= ' ') {
			if (yych == '\t') goto yy110;
			if (yych >= ' ') goto yy110;
		} else {
			if (yych <= 'c') goto yy112;
			if (yych <= 'd') goto yy113;
			if (yych <= 'e') goto yy114;
		}
	} else {
		if (yych <= 'o') {
			if (yych <= 'i') goto yy115;
			if (yych == 'l') goto yy116;
		} else {
			if (yych <= 'p') goto yy117;
			if (yych == 'u') goto yy118;
		}
	}
yy112:
	YYCURSOR = YYMARKER;
	if (yyaccept == 0) {
		goto yy107;
	} else {
		goto yy124;
	}
yy113:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy119;
	goto yy112;
yy114:
	yych = *++YYCURSOR;
	if (yych <= 'm') {
		if (yych == 'l') goto yy120;
		goto yy112;
	} else {
		if (yych <= 'n') goto yy121;
		if (yych == 'r') goto yy122;
		goto yy112;
	}
yy115:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy123;
	if (yych == 'n') goto yy125;
	goto yy112;
yy116:
	yych = *++YYCURSOR;
	if (yych == 'i') goto yy126;
	goto yy112;
yy117:
	yych = *++YYCURSOR;
	if (yych == 'r') goto yy127;
	goto yy112;
yy118:
	yych = *++YYCURSOR;
	if (yych == 'n') goto yy128;
	goto yy112;
yy119:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy129;
	goto yy112;
yy120:
	yych = *++YYCURSOR;
	if (yych == 'i') goto yy130;
	if (yych == 's') goto yy131;
	goto yy112;
yy121:
	yych = *++YYCURSOR;
	if (yych == 'd') goto yy132;
	goto yy112;
yy122:
	yych = *++YYCURSOR;
	if (yych == 'r') goto yy133;
	goto yy112;
yy123:
	yyaccept = 1;
	yych = *(YYMARKER = ++YYCURSOR);
	if (yych == 'd') goto yy134;
	if (yych == 'n') goto yy135;
yy124:
	{ RET(TOKEN_PP_IF); }
yy125:
	yych = *++YYCURSOR;
	if (yych == 'c') goto yy136;
	goto yy112;
yy126:
	yych = *++YYCURSOR;
	if (yych == 'n') goto yy137;
	goto yy112;
yy127:
	yych = *++YYCURSOR;
	if (yych == 'a') goto yy138;
	goto yy112;
yy128:
	yych = *++YYCURSOR;
	if (yych == 'd') goto yy139;
	goto yy112;
yy129:
	yych = *++YYCURSOR;
	if (yych == 'i') goto yy140;
	goto yy112;
yy130:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy141;
	goto yy112;
yy131:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy142;
	goto yy112;
yy132:
	yych = *++YYCURSOR;
	if (yych == 'i') goto yy143;
	goto yy112;
yy133:
	yych = *++YYCURSOR;
	if (yych == 'o') goto yy144;
	goto yy112;
yy134:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy145;
	goto yy112;
yy135:
	yych = *++YYCURSOR;
	if (yych == 'd') goto yy146;
	goto yy112;
yy136:
	yych = *++YYCURSOR;
	if (yych == 'l') goto yy147;
	goto yy112;
yy137:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy148;
	goto yy112;
yy138:
	yych = *++YYCURSOR;
	if (yych == 'g') goto yy149;
	goto yy112;
yy139:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy150;
	goto yy112;
yy140:
	yych = *++YYCURSOR;
	if (yych == 'n') goto yy151;
	goto yy112;
yy141:
	++YYCURSOR;
	{ RET(TOKEN_PP_ELIF); }
yy142:
	++YYCURSOR;
	{ RET(TOKEN_PP_ELSE); }
yy143:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy152;
	goto yy112;
yy144:
	yych = *++YYCURSOR;
	if (yych == 'r') goto yy153;
	goto yy112;
yy145:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy154;
	goto yy112;
yy146:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy155;
	goto yy112;
yy147:
	yych = *++YYCURSOR;
	if (yych == 'u') goto yy156;
	goto yy112;
yy148:
	++YYCURSOR;
	{ RET(TOKEN_PP_LINE); }
yy149:
	yych = *++YYCURSOR;
	if (yych == 'm') goto yy157;
	goto yy112;
yy150:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy158;
	goto yy112;
yy151:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy159;
	goto yy112;
yy152:
	++YYCURSOR;
	{ RET(TOKEN_PP_ENDIF); }
yy153:
	++YYCURSOR;
	{ RET(TOKEN_PP_ERROR); }
yy154:
	++YYCURSOR;
	{ RET(TOKEN_PP_IFDEF); }
yy155:
	yych = *++YYCURSOR;
	if (yych == 'f') goto yy160;
	goto yy112;
yy156:
	yych = *++YYCURSOR;
	if (yych == 'd') goto yy161;
	goto yy112;
yy157:
	yych = *++YYCURSOR;
	if (yych == 'a') goto yy162;
	goto yy112;
yy158:
	++YYCURSOR;
	{ RET(TOKEN_PP_UNDEF); }
yy159:
	++YYCURSOR;
	{ RET(TOKEN_PP_DEFINE); }
yy160:
	++YYCURSOR;
	{ RET(TOKEN_PP_IFNDEF); }
yy161:
	yych = *++YYCURSOR;
	if (yych == 'e') goto yy163;
	goto yy112;
yy162:
	++YYCURSOR;
	{ RET(TOKEN_PP_PRAGMA); }
yy163:
	++YYCURSOR;
	{ RET(TOKEN_PP_INCLUDE); }
}


bad_chars:
    if (YYLIMIT == YYCURSOR) YYFILL(1);

{
	YYCTYPE yych;
	if (YYLIMIT <= YYCURSOR) YYFILL(1);
	yych = *YYCURSOR;
	if (yych <= '#') {
		if (yych <= '\r') {
			if (yych <= 0x00) goto yy165;
			if (yych <= 0x08) goto yy166;
			goto yy167;
		} else {
			if (yych <= 0x1F) goto yy166;
			if (yych == '"') goto yy166;
			goto yy167;
		}
	} else {
		if (yych <= '@') {
			if (yych <= '$') goto yy166;
			if (yych <= '?') goto yy167;
			goto yy166;
		} else {
			if (yych == '`') goto yy166;
			if (yych <= '~') goto yy167;
			goto yy166;
		}
	}
yy165:
	++YYCURSOR;
	{
                        if (eoi)
                        {
                            assert( !((token >= sentinel) &&
                                     (token < sentinel+YYMAXFILL)) );
                            eoi = 0;
                            cursor = (uchar *) s->source_base + s->orig_length;
                            RET(TOKEN_BAD_CHARS);  // next call will be EOI.
                        }
                        goto bad_chars;
                    }
yy166:
	++YYCURSOR;
	{ goto bad_chars; }
yy167:
	++YYCURSOR;
	{ cursor--; RET(TOKEN_BAD_CHARS); }
}


    assert(0 && "Shouldn't hit this code");
    RET(TOKEN_UNKNOWN);
} // preprocessor_lexer

// end of mojoshader_lexer.re (or .c) ...

