<?xml version="1.0"?>
<package >
  <metadata>
    <id>SDL2-CS-Rolling</id>
    <version>2000.1.1</version>
    <authors>flibitijibibo</authors>
    <owners>beannaich</owners>
    <licenseUrl>https://github.com/flibitijibibo/SDL2-CS/blob/master/LICENSE</licenseUrl>
    <projectUrl>https://github.com/flibitijibibo/SDL2-CS</projectUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>SDL2# - C# Wrapper for SDL2</description>
    <copyright>Copyright 2013-2024</copyright>
    <tags>SDL2# SDL2 SDL</tags>
  </metadata>
  <files>
    <file src="..\bin\Debug\*.dll" target="lib/net461" />
    <file src="..\bin\Debug\*.dll.config" target="lib/net461" />
    <file src="..\bin\Debug\*.pdb" target="lib/net461" />
    <file src="..\bin\Debug\netstandard2.0\*.dll" target="lib/netstandard2.0" />
    <file src="..\bin\Debug\netstandard2.0\*.dll.config" target="lib/netstandard2.0" />
    <file src="..\bin\Debug\netstandard2.0\*.pdb" target="lib/netstandard2.0" />
  </files>
</package>
