/********************************************************************
 *                                                                  *
 * THIS FILE IS PART OF THE OggVorbis SOFTWARE CODEC SOURCE CODE.   *
 * US<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ON OF THIS LIBRARY SOURCE IS     *
 * GOVERNED BY A BSD-STYLE SOURCE LICENSE INCLUDED WITH THIS SOURCE *
 * IN 'COPYING'. PLEASE READ THESE TERMS BEFORE DISTRIBUTING.       *
 *                                                                  *
 * THE OggVorbis SOURCE CODE IS (C) COPYRIGHT 1994-2010             *
 * by the Xiph.Org Foundation http://www.xiph.org/                  *
 *                                                                  *
 ********************************************************************
 *
 * function: static codebooks for 5.1 surround
 *
 ********************************************************************/

static const long _vq_quantlist__44p0_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p0_l0_0[] = {
	 1, 3, 4, 7, 7, 8, 8, 9, 9, 9,10,10,10, 5, 6, 5,
	 8, 7, 9, 8, 9, 9,10, 9,11,10, 5, 5, 7, 7, 8, 8,
	 9, 9, 9, 9,10,10,11, 8, 9, 8,10, 9,10, 9,10, 9,
	11,10,11,10, 8, 8, 9, 9,10, 9,10, 9,11,10,11,10,
	11,10,11,11,11,11,11,11,11,11,11,11,11,11,10,11,
	11,11,12,11,11,11,11,11,11,10,12,12,12,12,12,12,
	12,11,12,12,12,11,11,11,12,12,12,12,12,12,12,11,
	12,11,12,11,11,13,12,12,12,13,12,12,12,12,11,12,
	11,11,13,13,13,12,12,12,12,12,12,11,11,11,10,13,
	13,13,12,13,12,13,11,13,10,12,11,11,13,13,12,13,
	12,12,12,12,11,12,11,11,11,
};

static const static_codebook _44p0_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p0_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p0_l0_0,
	0
};

static const long _vq_quantlist__44p0_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p0_l0_1[] = {
	 1, 4, 4, 6, 6, 5, 5, 5, 7, 5, 5, 5, 5, 6, 7, 7,
	 6, 7, 7, 7, 6, 7, 7, 7, 7,
};

static const static_codebook _44p0_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p0_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p0_l0_1,
	0
};

static const long _vq_quantlist__44p0_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p0_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p0_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p0_l1_0,
	0
};

static const char _huff_lengthlist__44p0_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p0_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p0_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p0_long[] = {
	 2, 3, 6, 7,10,14,16, 3, 2, 5, 7,11,14,17, 6, 5,
	 5, 7,10,12,14, 7, 7, 6, 6, 7, 9,13,10,11, 9, 6,
	 6, 9,11,15,15,13,10, 9,10,12,18,18,16,14,12,13,
	16,
};

static const static_codebook _huff_book__44p0_long = {
	2, 49,
	(char *)_huff_lengthlist__44p0_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p0_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p0_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p0_p1_0,
	0
};

static const long _vq_quantlist__44p0_p2_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p2_0[] = {
	 1, 5, 5, 0, 7, 7, 0, 8, 8, 0, 9, 9, 0,12,12, 0,
	 8, 8, 0, 9, 9, 0,12,12, 0, 8, 8, 0, 6, 6, 0,11,
	11, 0,12,12, 0,12,12, 0,15,15, 0,11,11, 0,12,12,
	 0,15,15, 0,12,12, 0, 5, 5, 0, 5, 5, 0, 6, 6, 0,
	 7, 7, 0,11,11, 0, 6, 6, 0, 7, 7, 0,10,11, 0, 6,
	 6, 0, 7, 7, 0,11,11, 0,12,12, 0,11,11, 0,15,15,
	 0,10,10, 0,12,12, 0,15,15, 0,12,12, 0, 6, 6, 0,
	12,12, 0,12,12, 0,12,12, 0,15,15, 0,11,11, 0,12,
	12, 0,15,15, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 8, 8, 0,12,12, 0,12,12, 0,12,12, 0,15,
	15, 0,12,12, 0,11,12, 0,15,16, 0,11,11, 0, 6, 6,
	 0,11,12, 0,12,12, 0,12,12, 0,16,15, 0,12,12, 0,
	13,12, 0,15,14, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p0_p2_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p2_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p0_p2_0,
	0
};

static const long _vq_quantlist__44p0_p2_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p2_1[] = {
	 1, 3, 3, 0, 9, 9, 0, 9, 9, 0,10,10, 0, 9, 9, 0,
	10,10, 0,10,10, 0, 9, 9, 0,10,10, 0, 7, 7, 0, 7,
	 7, 0, 6, 6, 0, 8, 8, 0, 7, 7, 0, 8, 8, 0, 8, 9,
	 0, 8, 8, 0, 8, 8, 0, 7, 7, 0, 9, 9, 0, 8, 8, 0,
	10,10, 0, 9, 9, 0,10,10, 0,10,10, 0, 9, 9, 0,10,
	10, 0, 9, 9, 0,11,11, 0,11,11, 0,12,12, 0,11,11,
	 0,12,12, 0,13,13, 0,12,12, 0,13,12, 0, 8, 8, 0,
	12,12, 0,12,12, 0,13,13, 0,12,12, 0,13,13, 0,13,
	13, 0,13,13, 0,13,13, 0, 7, 7, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0,11,11, 0,12,12, 0,13,13, 0,12,
	12, 0,13,13, 0,13,13, 0,12,12, 0,12,12, 0, 8, 8,
	 0,12,12, 0,12,12, 0,13,13, 0,13,13, 0,13,14, 0,
	14,13, 0,13,13, 0,13,13, 0, 7, 7, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p0_p2_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p2_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p0_p2_1,
	0
};

static const long _vq_quantlist__44p0_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p3_0[] = {
	 1, 6, 6, 7, 8, 8, 7, 8, 8, 7, 9, 9,10,12,11, 9,
	 8, 8, 7, 9, 9,11,12,12, 9, 9, 9, 6, 7, 7,10,11,
	11,10,11,11,10,11,11,13,13,14,12,12,12,11,11,11,
	14,14,14,12,12,12, 6, 5, 5, 9, 6, 5, 9, 6, 6, 9,
	 7, 7,12,10,10,11, 6, 6,10, 7, 7,13,10,10,12, 7,
	 7, 7, 8, 8,12,10,10,12,10,10,11,10,10,15,13,13,
	13, 9, 9,12,11,11,16,13,13,15,11,11, 8, 7, 7,12,
	12,12,12,11,11,12,11,11,14,14,14,14,12,12,12,12,
	12,16,15,15,14,12,12, 0,10,10, 0,12,12, 0,12,12,
	 0,11,11, 0,14,14, 0,11,11, 0,12,12, 0,15,15, 0,
	11,11, 8, 8, 8,13,11,11,13,10,10,13,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,14,10,10, 9, 7, 7,
	13,11,11,13,11,11,12,11,11,16,14,14,14,12,12,13,
	12,12,15,14,14,15,13,12, 0,11,11, 0,12,12, 0,12,
	12, 0,12,12, 0,15,15, 0,12,12, 0,13,12, 0,14,15,
	 0,12,12,
};

static const static_codebook _44p0_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p3_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p0_p3_0,
	0
};

static const long _vq_quantlist__44p0_p3_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p0_p3_1[] = {
	 2, 4, 4, 8, 8,10,12,12,11,11, 9,11,11,12,13,11,
	12,12,11,11,11,12,12,12,12,10,13,12,13,13,11,12,
	12,13,13,11,12,12,13,13,11,12,13,13,13,11,13,13,
	13,13,10,13,13,12,13,11,12,12,14,14,11,13,12,12,
	12,11,12,12,13,13,11,13,13,12,12,11,13,13,13,13,
	11,12,12,13,13,11,13,13,12,12,11,12,12,13,13,11,
	13,13,12,12,11,13,13,13,13,11,12,12,14,14,11,13,
	13,12,12,11,12,12,13,13,11,13,13,12,12,11,10,10,
	10,10,12,10,10,11,11,11, 8, 8,11,11,13,10,10,10,
	10,12,10,10,10,10,13,11,11,11,11,13,10,10,11,11,
	13,11,11,12,12,13,11,11,11,11,13,11,11,12,12,13,
	11,11,12,12,13,10,10,11,11,13,11,11,11,11,13,11,
	10,11,11,13,11,11,11,11,13,11,11,11,11,13,10,10,
	11,11,13,11,11,11,11,12,10,11,11,11,13,11,11,11,
	11,13,11,11,11,11,13,10,10,11,11,13,11,11,11,11,
	13,11,11,11,11,13,11,11,11,11,11,10,10,10,10,12,
	10,10, 9, 9,12,12,12,11,11,13,12,12, 9, 9,13,12,
	12,10,10,12,12,12,12,12,13,13,13,14,14,13,12,12,
	11,11,13,13,13,12,12,13,12,12,11,11,13,12,13,11,
	11,13,13,13,14,14,13,12,12,10,10,13,13,13,11,11,
	13,12,12,10,10,13,13,13,11,11,13,13,13,14,14,13,
	12,12,10,10,13,13,13,11,11,13,12,13,10,10,13,13,
	13,11,11,13,13,13,14,14,13,12,12,10,10,13,13,13,
	11,11,13,13,12,10,10,14,12,12, 8, 8,14,12,12, 9,
	 9,14,11,11, 9, 9,14,12,12, 8, 8,14,11,11, 7, 7,
	14,13,13,10,10,15,12,12,10,10,15,13,13,10,10,15,
	12,12, 9, 9,15,13,13,10,10,15,13,13,10,10,15,12,
	12,10,10,15,13,13,10,10,14,12,12, 9, 9,14,13,13,
	 9, 9,14,13,13, 9, 9,15,12,12, 9, 9,15,13,13, 9,
	 9,14,12,12, 9, 9,14,13,13, 9, 9,14,13,13, 9, 9,
	15,12,12, 9, 9,14,13,13, 9, 9,14,12,12, 9, 9,14,
	13,13, 9, 9,13,12,12, 8, 8,13,13,13, 8, 8,14,13,
	13, 9, 9,13,13,13, 7, 7,14,13,13, 8, 8,14,14,14,
	10,10,14,14,14,11,11,14,14,14, 9, 9,14,14,14,10,
	10,14,14,14, 9, 9,14,14,14,10, 9,15,14,14,11,11,
	14,14,14, 9, 9,14,14,14,10,10,14,14,14, 9, 9,14,
	14,14, 9, 9,15,14,14,11,11,14,14,14, 8, 8,14,14,
	14, 9, 9,14,14,14, 8, 8,14,14,14, 9, 9,15,14,14,
	11,11,14,14,14, 8, 8,14,14,14, 9, 9,14,14,14, 8,
	 8,12,12,12,13,13,16,15,15,11,11,16,15,16,12,12,
	17,16,16,11,11,17,15,15,12,11,16,16,16,12,13,16,
	15,15,13,13,16,16,16,12,12,16,16,15,13,13,16,16,
	16,12,12,16,16,16,13,13,17,16,16,14,14,17,17,16,
	12,12,17,16,16,13,13,17,17,16,12,13,16,16,17,13,
	12,17,16,16,14,13,17,16,16,12,12,17,16,16,12,12,
	17,16,17,12,12,17,17,17,13,13,16,16,16,13,14,17,
	17,16,12,12,16,16,16,13,13,17,17,17,12,12,13,14,
	14,10,10,16,14,14,12,12,16,15,15,14,14,16,14,14,
	12,12,15,14,14,13,13,17,15,15,14,13,16,16,15,15,
	15,16,15,15,14,14,16,15,15,14,14,17,15,15,14,14,
	16,15,15,14,14,16,16,15,15,15,17,15,15,13,13,16,
	15,15,14,14,17,15,15,13,13,17,15,15,14,14,16,15,
	15,15,15,16,14,14,13,13,16,15,15,14,14,16,14,14,
	13,13,17,15,15,14,14,16,16,15,15,15,17,14,14,13,
	13,16,15,15,14,14,17,14,14,13,13,13,11,11,10,10,
	16,14,14,13,13,15,14,14,13,13,16,14,14,12,12,16,
	14,14,12,12,15,15,15,14,14,16,14,14,14,14,16,15,
	14,14,14,16,14,14,14,14,16,15,15,14,13,16,15,15,
	14,14,16,14,14,14,14,17,15,15,14,14,16,14,14,14,
	14,16,15,15,13,14,16,15,15,14,14,16,14,14,14,14,
	16,15,15,13,13,16,14,14,13,13,16,15,15,13,13,16,
	15,15,14,14,16,14,14,14,14,17,15,15,13,13,16,15,
	14,13,13,17,15,15,13,13,14,14,14, 9, 9,14,14,14,
	17,17,14,15,15,18,18,14,14,14,18,19,14,14,14,18,
	18,15,15,15,19,18,15,16,15,18,20,15,15,15,18,19,
	15,15,15,19,19,15,15,15,18,20,15,15,15,18,19,15,
	15,16,20,18,15,15,15,18,18,15,15,15,19,19,15,15,
	15,18,19,15,15,15,18,19,15,15,15,19,19,14,15,14,
	19,19,15,15,15,20,19,15,14,14,19,18,14,15,15,18,
	19,15,15,16,20,20,14,14,14,18,19,15,15,15,19,18,
	14,14,14,18,18,14,12,12, 9, 9,13,14,14,18,18,14,
	13,13,18,19,14,14,14,18,18,14,14,14,18,18,15,15,
	15,19,19,15,14,14,19,18,14,15,15,19,18,15,14,14,
	18,18,15,15,15,19,18,14,15,15,19,19,15,14,14,19,
	18,14,15,15,19,18,15,14,14,19,18,14,15,15,19,18,
	15,15,15,21,18,15,14,14,19,18,14,15,15,18,19,14,
	15,14,20,19,14,15,15,18,19,14,15,15,19,19,15,14,
	14,19,20,14,15,15,18,18,14,14,14,19,19,14,15,15,
	19,18,12,12,12,13,13,16,15,15,11,11,16,15,15,12,
	12,16,16,16,11,11,16,15,15,11,11,16,16,16,13,13,
	17,16,16,13,13,17,17,17,12,12,16,16,16,13,13,17,
	16,17,13,12,15,16,16,12,12,16,15,15,13,13,17,16,
	16,12,12,16,16,15,12,12,16,16,16,12,12,17,17,16,
	13,12,16,16,16,13,13,17,16,16,12,12,17,16,16,12,
	12,17,17,16,12,12,16,17,16,12,12,17,15,15,13,13,
	17,16,16,12,12,16,16,16,12,12,16,16,16,12,12,13,
	13,13, 9, 9,15,14,14,13,13,16,15,14,14,14,16,14,
	14,13,13,15,14,14,13,13,17,15,15,14,14,16,15,15,
	15,15,16,15,15,14,14,16,15,15,15,15,17,15,15,14,
	14,16,15,15,14,14,16,15,15,15,15,17,14,15,14,14,
	16,15,15,14,14,17,15,15,13,14,17,15,15,14,14,16,
	15,15,15,15,17,14,14,13,13,16,15,15,14,14,17,14,
	14,13,13,17,15,15,14,14,16,15,16,15,15,17,14,14,
	13,13,16,15,15,14,14,18,14,14,13,13,13,11,11,11,
	11,15,14,14,12,12,15,14,14,13,13,16,14,14,12,12,
	16,13,14,12,12,16,15,15,13,13,16,14,14,14,14,16,
	15,15,13,13,16,14,14,13,13,16,14,15,13,13,15,15,
	15,13,13,16,14,14,14,13,16,14,14,13,13,16,14,14,
	13,13,16,15,15,13,13,16,15,15,13,13,16,14,14,14,
	14,16,15,15,12,12,16,14,14,13,13,16,15,15,12,12,
	16,15,15,13,13,16,14,14,14,14,17,15,14,12,12,16,
	14,14,13,13,16,15,15,12,12,14,14,14, 8, 8,14,14,
	14,17,18,14,15,15,17,18,14,14,14,17,18,14,14,14,
	18,18,14,15,15,18,18,14,16,15,19,19,15,15,15,18,
	19,15,16,15,20,19,15,15,15,18,18,14,15,15,18,19,
	15,16,16,20,19,15,15,15,19,17,14,15,15,20,18,14,
	15,15,18,18,14,15,15,18,19,14,15,15,19,20,14,14,
	14,18,18,14,15,15,18,19,14,14,14,18,19,14,15,15,
	19,18,15,16,16,20,21,14,14,15,19,19,14,15,15,19,
	19,14,14,14,19,18,13,12,12, 9, 9,13,14,14,18,19,
	14,14,14,18,19,14,14,14,18,18,14,14,14,18,18,14,
	15,15,19,19,15,14,14,19,18,15,15,15,19,19,15,14,
	14,19,20,14,15,15,18,19,14,15,15,20,18,15,14,14,
	18,18,14,15,15,18,18,14,14,14,19,19,14,15,15,18,
	18,14,15,15,19,18,15,14,14,19,19,14,15,15,19,18,
	15,14,14,19,18,14,14,15,18,19,14,15,15,19,18,15,
	14,14,18,19,14,15,14,19,20,14,14,14,19,19,14,15,
	15,19,19,12,12,12,13,13,16,16,16,11,11,16,16,16,
	12,12,17,16,16,11,11,17,15,15,11,11,16,16,16,13,
	13,17,15,16,13,13,16,16,16,12,12,17,16,16,13,13,
	17,17,16,12,12,17,17,16,13,13,17,16,16,13,13,17,
	17,17,12,12,17,16,16,13,13,17,17,17,12,12,16,16,
	16,12,12,17,15,15,13,13,17,16,16,11,11,17,16,16,
	12,12,16,16,16,11,11,16,17,16,12,12,17,16,16,13,
	13,17,17,16,12,12,17,17,16,12,12,17,16,16,11,11,
	13,14,14, 9, 9,16,14,14,13,13,16,14,15,14,14,16,
	14,14,12,12,16,14,14,13,13,17,15,15,14,14,16,15,
	15,15,15,17,15,15,14,14,16,15,15,14,14,17,15,15,
	14,14,16,15,15,14,14,16,15,15,15,16,17,14,15,14,
	14,16,15,15,14,14,17,15,15,14,14,16,15,15,14,14,
	16,15,15,15,15,17,14,14,13,13,16,15,15,14,14,16,
	14,14,13,13,17,15,15,14,14,16,16,15,15,15,17,14,
	14,13,13,16,15,15,14,14,17,14,14,13,13,13,11,11,
	10,10,16,14,14,12,12,15,13,13,13,12,16,14,14,11,
	11,16,14,14,11,11,16,14,15,13,14,16,14,14,13,13,
	16,15,15,13,13,16,14,14,13,13,16,15,15,13,13,16,
	15,15,13,13,17,14,14,14,14,17,15,15,13,13,16,14,
	15,13,13,16,15,15,13,13,16,15,15,13,13,16,14,14,
	13,13,17,15,15,12,12,16,14,14,12,12,16,15,15,12,
	12,16,15,15,13,13,16,14,14,13,13,17,15,15,12,12,
	17,14,14,12,12,16,15,15,12,12,13,14,14, 8, 8,13,
	14,14,18,18,13,15,15,17,18,14,14,14,18,19,14,14,
	14,19,18,14,15,15,19,18,15,15,16,21,18,15,15,15,
	19,19,14,16,16,19,19,14,15,15,18,19,14,15,15,19,
	20,14,16,16,19,18,15,15,15,18,19,14,15,15,19,18,
	15,15,15,18,18,15,15,15,20,18,15,16,16,20,19,14,
	15,14,18,19,14,15,16,19,20,14,15,15,19,18,15,15,
	15,19,18,15,16,16,20,19,15,14,14,18,18,14,15,15,
	19,19,14,15,15,18,18,13,12,12, 8, 8,13,14,14,19,
	18,14,13,13,20,18,14,14,14,19,18,14,13,13,18,19,
	14,15,15,20,19,15,14,14,19,19,14,15,15,19,18,15,
	14,14,20,20,15,15,15,19,18,14,15,15,19,18,15,14,
	14,19,18,14,15,15,20,19,14,14,14,20,19,14,15,15,
	19,18,15,15,15,18,18,15,14,14,18,18,14,15,15,19,
	19,14,14,14,19,19,14,15,15,19,19,15,15,15,19,18,
	15,14,14,20,19,15,15,15,19,19,14,14,14,20,19,14,
	15,15,20,20,12,12,12,13,13,17,16,16,11,11,16,16,
	15,12,12,17,16,16,11,11,17,15,15,11,11,17,17,17,
	13,13,17,16,16,13,13,17,17,17,12,12,17,16,16,13,
	13,17,17,16,12,13,16,17,16,13,13,17,16,15,13,13,
	17,16,16,12,12,17,16,16,12,13,17,16,17,12,12,17,
	17,17,12,12,17,16,15,13,13,17,16,16,12,12,17,16,
	16,12,12,17,16,16,11,11,16,16,16,12,12,17,15,15,
	13,13,17,16,15,11,11,16,16,16,12,12,17,16,16,11,
	11,13,14,14, 9, 9,16,14,14,13,13,16,14,15,14,14,
	16,14,14,12,12,16,14,14,13,13,17,15,15,14,15,16,
	15,15,15,15,17,15,15,14,14,16,15,15,15,14,16,15,
	15,14,14,16,15,15,14,14,16,15,16,15,15,17,15,14,
	14,14,16,15,15,14,14,17,15,15,13,13,16,15,15,14,
	14,16,16,16,15,15,17,14,14,13,13,16,15,15,14,14,
	18,14,15,13,13,16,15,15,14,14,16,16,15,15,15,16,
	14,14,13,13,16,15,15,14,14,17,14,15,13,13,13,11,
	11,10,10,15,14,14,12,12,15,14,14,13,13,16,14,14,
	12,12,16,13,14,12,12,16,14,15,14,13,16,14,14,14,
	14,16,15,15,13,13,16,14,14,13,13,16,15,15,13,13,
	15,15,15,13,13,16,14,14,14,14,17,15,15,13,13,16,
	14,14,13,13,16,15,15,13,13,16,15,15,13,13,16,14,
	14,13,13,17,15,15,12,12,16,14,14,12,12,16,14,15,
	12,12,16,15,15,13,13,16,14,14,13,13,17,15,15,12,
	12,16,14,14,12,12,16,15,15,12,12,14,14,14, 8, 8,
	14,14,14,17,17,14,15,15,18,18,14,14,14,18,17,14,
	14,14,18,18,14,15,15,18,20,15,16,15,19,18,15,15,
	15,19,18,15,15,16,19,18,15,15,15,18,18,14,15,15,
	18,18,15,16,16,18,19,15,15,15,18,18,15,15,15,19,
	20,15,15,15,18,18,15,15,15,18,18,15,16,16,19,19,
	15,14,15,19,19,15,15,15,19,20,14,14,15,18,18,15,
	15,15,19,19,15,16,16,19,19,15,15,14,18,19,15,15,
	15,20,20,15,15,14,18,18,13,12,12, 8, 8,13,14,14,
	18,18,14,14,14,18,18,14,14,14,18,20,14,14,14,18,
	18,14,15,15,19,18,15,14,14,18,19,15,15,15,18,19,
	15,14,14,18,19,15,15,15,18,18,14,15,14,18,19,15,
	14,14,21,19,15,15,15,19,18,14,14,14,19,18,14,15,
	15,19,18,15,15,15,20,19,15,14,14,20,18,14,15,15,
	18,19,14,14,14,19,18,14,15,15,18,19,15,15,15,18,
	19,15,14,14,19,19,15,15,15,19,19,14,14,14,19,20,
	14,15,15,18,19,
};

static const static_codebook _44p0_p3_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p0_p3_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p0_p3_1,
	0
};

static const long _vq_quantlist__44p0_p4_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p0_p4_0[] = {
	 2, 6, 6,14,14, 6, 8, 8,14,14, 7, 7, 7,14,14, 0,
	13,13,15,16, 0,13,13,15,15, 7, 8, 8,15,15, 9,10,
	10,16,16, 9, 8, 8,14,15, 0,13,13,17,17, 0,13,13,
	16,16, 8, 8, 8,15,15,12,11,11,16,16, 9, 8, 8,14,
	14, 0,13,13,17,17, 0,13,13,15,15, 0,14,14,16,16,
	 0, 0, 0,18,19, 0,12,12,16,15, 0,16,16, 0,20, 0,
	14,14,16,16, 0,14,14,17,17, 0, 0, 0,19,19, 0,12,
	12,15,15, 0,18,17,21,21, 0,14,14,16,16, 5, 7, 7,
	12,13, 9,10, 9,14,14,11,10,10,14,14, 0, 0, 0,18,
	17, 0,20,21,18,18, 9,10,10,14,14,12,12,12,17,16,
	12,10,10,14,14, 0,20,20,18,17, 0,21,21,17,17,11,
	10,10,14,14,15,13,13,18,18,13,11,11,14,14, 0,20,
	 0,18,18, 0,20,21,18,17, 0,21, 0,18,19, 0, 0, 0,
	 0,21, 0,21,20,16,17, 0, 0, 0,21,21, 0, 0, 0,20,
	18, 0,20, 0,17,18, 0, 0, 0, 0, 0, 0, 0,20,16,17,
	 0, 0, 0,20, 0, 0, 0, 0,18,18, 6, 6, 6,13,13, 8,
	 5, 5,11,11, 9, 6, 6,13,13, 0, 9, 9,12,12, 0,10,
	10,14,14, 9, 7, 7,13,13,12, 9, 9,13,13,10, 6, 6,
	13,13, 0,10,10,14,14, 0,10,10,13,13, 9, 7, 7,13,
	13,13,10,10,13,13,11, 6, 6,13,13, 0,10,10,15,15,
	 0,10,10,13,13, 0,12,11,15,15, 0,20,19,17,16, 0,
	 9, 9,13,13, 0,13,13,20,19, 0,11,11,13,13, 0,11,
	11,15,15, 0,20,19,17,17, 0,10,10,13,13, 0,14,15,
	 0,21, 0,12,12,13,13, 0,10,10,12,12, 0,11,11,15,
	15, 0,11,11,15,15, 0,15,15,20,20, 0,16,16, 0, 0,
	 0,11,11,15,15, 0,14,14,17,17, 0,11,11,15,15, 0,
	15,15,20,21, 0,16,16,21,21, 0,12,12,15,15, 0,15,
	15,18,20, 0,11,11,16,15, 0,15,15,21,21, 0,16,16,
	 0,21, 0,16,16, 0, 0, 0, 0, 0, 0, 0, 0,14,14,21,
	21, 0,17,18, 0, 0, 0,16,17,20, 0, 0,16,16, 0, 0,
	 0, 0, 0, 0, 0, 0,15,15,20,20, 0,19,18, 0,21, 0,
	18,17, 0, 0, 0,10,10,11,11, 0,10,10,10,10, 0,11,
	11,12,12, 0,11,11, 9, 9, 0,13,13,12,12, 0,11,11,
	12,12, 0,13,13,12,12, 0,10,10,12,12, 0,12,12,13,
	13, 0,12,12,12,12, 0,11,11,12,12, 0,13,13,12,12,
	 0,10,10,12,12, 0,13,13,13,13, 0,12,12,12,12, 0,
	14,13,13,13, 0,19,21,15,15, 0,12,11,12,12, 0,16,
	15,19,19, 0,13,13,11,11, 0,13,13,13,13, 0, 0,21,
	15,16, 0,12,12,12,12, 0,16,16,19,21, 0,13,13,12,
	12, 7, 7, 7,16,16,11, 9, 9,16,16,12, 9, 9,16,16,
	 0,13,13,16,16, 0,14,14,17,16,11, 9, 9,16,16,14,
	12,11,17,17,13, 8, 9,15,15, 0,13,13,19,19, 0,13,
	13,16,15,12,10,10,17,17,15,12,12,19,18,14, 9, 9,
	17,16, 0,14,14,18, 0, 0,14,13,16,16, 0,14,15,18,
	17, 0,21, 0,19,21, 0,12,12,16,16, 0,16,16, 0, 0,
	 0,14,14,16,16, 0,14,14,18,18, 0, 0,21,20, 0, 0,
	13,13,16,17, 0,18,18, 0, 0, 0,15,14,17,16, 8, 7,
	 7,14,14,11,10,10,15,15,13,10,10,15,15, 0,21,20,
	19,19, 0,21, 0,17,18,11,10,10,15,16,14,12,12,18,
	18,14,11,11,15,14, 0,21,20,18,19, 0, 0,21,18,18,
	12,11,11,16,16,16,14,14,18,20,14,11,11,16,15, 0,
	20,20,19,19, 0, 0,20,18,18, 0,21, 0,18,19, 0, 0,
	 0, 0, 0, 0,20,20,17,18, 0, 0, 0,20,20, 0, 0, 0,
	19,19, 0, 0, 0,20,18, 0, 0, 0, 0, 0, 0, 0,21,18,
	18, 0,21,21, 0,21, 0, 0, 0,19,20,11, 9, 9,14,14,
	13,10,10,14,14,13,11,11,15,15, 0,13,13,13,13, 0,
	14,14,16,16,13,11,11,15,15,16,12,12,15,15,14,10,
	10,14,14, 0,14,14,16,16, 0,14,14,15,15,13,10,10,
	15,15,17,13,14,15,16,15,10,10,15,15, 0,14,14,17,
	16, 0,14,14,15,15, 0,15,15,17,17, 0, 0,21,18,18,
	 0,13,13,15,15, 0,16,16,21,20, 0,14,14,15,14, 0,
	15,14,16,17, 0, 0,20,20,19, 0,13,13,15,15, 0,19,
	18, 0, 0, 0,15,15,15,15, 0,11,11,14,14, 0,12,12,
	16,16, 0,12,12,16,16, 0,15,16,21,21, 0,16,17,21,
	 0, 0,12,12,17,16, 0,14,14,18,19, 0,11,11,16,16,
	 0,15,15,20,21, 0,16,16,21, 0, 0,12,12,17,16, 0,
	15,15,19,19, 0,12,12,16,17, 0,16,15, 0, 0, 0,16,
	16, 0, 0, 0,17,17, 0,21, 0, 0, 0, 0, 0, 0,14,15,
	20, 0, 0,17,17, 0, 0, 0,17,17, 0, 0, 0,17,16, 0,
	 0, 0, 0, 0, 0, 0, 0,15,15, 0, 0, 0,18,18, 0, 0,
	 0,18,17, 0, 0, 0,11,11,14,14, 0,12,12,15,15, 0,
	12,12,15,15, 0,13,13,14,14, 0,14,14,17,17, 0,12,
	12,16,16, 0,14,14,16,16, 0,11,11,15,15, 0,13,13,
	16,17, 0,13,13,16,16, 0,12,12,15,15, 0,14,14,17,
	16, 0,11,11,15,15, 0,14,14,17,17, 0,13,13,16,16,
	 0,15,15,17,18, 0,21,20,20,21, 0,12,12,15,15, 0,
	16,16,20,21, 0,14,14,15,15, 0,14,14,17,17, 0, 0,
	 0,18,19, 0,12,13,15,15, 0,18,17,21, 0, 0,14,15,
	15,15, 8, 8, 8,16,16,12,10,10,16,16,13, 9, 9,16,
	16, 0,14,14,18,17, 0,14,14,16,17,12,10,10,18,17,
	14,12,11,18,18,14, 9, 9,16,16, 0,13,13,18,18, 0,
	13,13,17,16,12, 9, 9,16,17,17,13,13,17,17,14, 9,
	 9,15,15, 0,14,14,20,19, 0,13,13,16,16, 0,15,15,
	19,18, 0, 0, 0,20,19, 0,12,13,17,17, 0,16,16,20,
	 0, 0,14,14,16,17, 0,14,14,19,18, 0, 0, 0,20,20,
	 0,13,13,16,16, 0,18,17, 0, 0, 0,15,15,16,16, 9,
	 7, 7,14,14,12,10,10,15,15,13,10,10,15,15, 0,21,
	 0,18,19, 0,20,21,19,18,12,10,10,16,15,15,13,13,
	18,18,14,11,11,15,15, 0, 0, 0,19,18, 0, 0,21,18,
	18,13,11,11,15,15,16,14,14,17,19,15,11,11,15,15,
	 0,21,21,20,18, 0, 0,21,18,18, 0, 0,21,21,19, 0,
	 0, 0, 0, 0, 0,19,20,18,17, 0, 0, 0,21,21, 0,21,
	 0,20,18, 0, 0,21,19,19, 0, 0, 0, 0, 0, 0,20,21,
	17,17, 0, 0, 0, 0, 0, 0,21, 0,18,20, 0,10,10,14,
	14, 0,11,11,15,15, 0,11,11,15,15, 0,14,14,15,15,
	 0,15,15,16,16, 0,11,12,16,16, 0,13,13,16,16, 0,
	11,11,15,15, 0,14,14,17,17, 0,14,14,15,15, 0,11,
	11,16,15, 0,14,14,15,15, 0,11,11,15,15, 0,15,15,
	17,17, 0,14,14,15,15, 0,16,16,18,18, 0, 0, 0,20,
	19, 0,14,13,16,15, 0,17,17,21, 0, 0,15,15,15,15,
	 0,16,15,17,16, 0,20, 0,20,18, 0,13,14,15,15, 0,
	19,18, 0,21, 0,15,15,15,15, 0,11,11,14,14, 0,12,
	12,16,16, 0,12,12,16,16, 0,16,15,20,21, 0,17,16,
	 0, 0, 0,12,12,16,16, 0,14,14,18,18, 0,11,11,16,
	16, 0,15,15,21,20, 0,16,16, 0, 0, 0,12,12,16,17,
	 0,15,14,19,19, 0,11,12,16,16, 0,15,15,21, 0, 0,
	16,16, 0, 0, 0,16,17, 0, 0, 0, 0, 0, 0, 0, 0,15,
	15,21, 0, 0,17,17, 0, 0, 0,17,17, 0, 0, 0,17,16,
	 0, 0, 0, 0, 0, 0, 0, 0,15,15, 0,20, 0,19,20, 0,
	 0, 0,17,17, 0, 0, 0,12,12,15,15, 0,12,12,15,15,
	 0,12,12,16,16, 0,13,13,15,15, 0,15,15,17,17, 0,
	13,13,17,16, 0,14,14,17,17, 0,11,11,16,16, 0,14,
	14,17,17, 0,13,13,16,16, 0,12,12,16,16, 0,15,15,
	16,17, 0,11,11,15,16, 0,14,14,17,17, 0,13,14,16,
	16, 0,15,15,18,18, 0,21,20,20,19, 0,13,13,16,17,
	 0,16,16, 0, 0, 0,14,14,16,16, 0,15,15,18,18, 0,
	 0, 0,20,19, 0,13,13,16,16, 0,17,17, 0, 0, 0,14,
	14,16,16, 0,11,11,16,16, 0,13,13,18,17, 0,13,13,
	17,17, 0,16,16,17,17, 0,16,16,17,18, 0,12,12,17,
	17, 0,15,15,18,18, 0,12,12,16,16, 0,16,16,19,19,
	 0,15,15,16,17, 0,12,12,17,17, 0,17,17,18,18, 0,
	12,12,17,17, 0,16,16,19,19, 0,15,16,17,17, 0,16,
	16,18,17, 0, 0, 0,21,21, 0,13,13,16,16, 0,17,17,
	 0,20, 0,15,15,16,17, 0,16,16,19,18, 0, 0,21,20,
	21, 0,14,14,17,16, 0,20, 0, 0, 0, 0,15,16,16,17,
	 0, 9, 9,14,14, 0,13,13,16,16, 0,14,14,15,15, 0,
	 0,20,19,19, 0, 0, 0,19,19, 0,12,12,15,15, 0,15,
	16,19,18, 0,14,14,15,15, 0,21, 0,18,18, 0,20, 0,
	17,18, 0,13,13,16,16, 0,17,17,17,19, 0,14,14,16,
	15, 0,21,20,20,19, 0, 0, 0,19,19, 0, 0, 0,19,18,
	 0, 0, 0, 0, 0, 0,20,20,17,18, 0, 0, 0,21,21, 0,
	 0, 0,18,18, 0,21, 0,18,19, 0, 0, 0, 0, 0, 0,20,
	21,18,18, 0, 0, 0,20,21, 0, 0, 0,19,19, 0,18,18,
	15,15, 0,20,21,17,17, 0,19,21,17,17, 0, 0, 0,17,
	18, 0, 0, 0,20,19, 0,19,19,17,17, 0, 0, 0,18,18,
	 0,19,20,16,17, 0, 0,21,20,20, 0,19,20,19,18, 0,
	19,20,16,16, 0, 0, 0,18,19, 0,19,20,17,17, 0, 0,
	21, 0,20, 0,21,21,17,19, 0,20, 0,19,20, 0, 0, 0,
	20, 0, 0,19,18,17,16, 0, 0, 0, 0, 0, 0, 0,20,17,
	17, 0,20,21,18,20, 0, 0, 0, 0,21, 0,19,20,17,17,
	 0, 0, 0, 0, 0, 0,20,21,17,17, 0,11,11,14,14, 0,
	13,13,16,17, 0,13,13,16,16, 0,17,17, 0,21, 0,18,
	17,21, 0, 0,13,13,16,16, 0,15,15,18,18, 0,12,12,
	16,16, 0,17,16,21, 0, 0,17,17, 0, 0, 0,12,12,17,
	17, 0,17,17,19,21, 0,13,12,16,16, 0,17,17, 0, 0,
	 0,17,17, 0, 0, 0,18,17, 0,21, 0, 0, 0, 0, 0, 0,
	15,15,20, 0, 0,20,18, 0, 0, 0,17,18, 0, 0, 0,16,
	17, 0, 0, 0, 0, 0, 0, 0, 0,15,15, 0, 0, 0,19,19,
	 0, 0, 0,18,18, 0, 0, 0,14,14,18,18, 0,16,16, 0,
	21, 0,16,16,21,21, 0,17,17, 0,20, 0,17,17,20, 0,
	 0,16,15, 0, 0, 0,20,20, 0, 0, 0,15,15,20,20, 0,
	17,17,21, 0, 0,17,18,20,20, 0,15,15,20,20, 0,18,
	18, 0, 0, 0,15,15,19,20, 0,17,18, 0, 0, 0,17,17,
	20,20, 0,18,17,21, 0, 0, 0, 0, 0,21, 0,15,15,20,
	20, 0,19,19, 0, 0, 0,17,17,21, 0, 0,17,17, 0, 0,
	 0, 0, 0,21, 0, 0,15,15,19,19, 0,20,21, 0, 0, 0,
	18,17,21,21, 0,12,12,16,16, 0,14,14,17,17, 0,13,
	13,17,18, 0,16,16,18,17, 0,16,16,18,18, 0,13,13,
	18,18, 0,15,16,19,18, 0,13,13,16,16, 0,16,16,20,
	18, 0,16,16,17,17, 0,12,13,17,17, 0,17,16,18,18,
	 0,12,12,16,16, 0,17,16,20,19, 0,16,16,16,16, 0,
	16,17,18,20, 0, 0, 0,21,20, 0,14,14,17,16, 0,19,
	18, 0,20, 0,16,16,17,16, 0,16,16,17,18, 0, 0,21,
	21,21, 0,14,14,16,16, 0,20,20,21, 0, 0,16,16,16,
	16, 0,10,10,14,14, 0,14,14,15,16, 0,14,14,15,15,
	 0, 0,21,18,18, 0, 0,21,18,19, 0,13,13,16,16, 0,
	16,16,18,18, 0,14,14,15,15, 0,21, 0,18,18, 0,21,
	 0,18,18, 0,13,13,16,16, 0,17,17,19,20, 0,14,14,
	15,15, 0, 0, 0,18,20, 0, 0,21,18,18, 0, 0,21,19,
	18, 0, 0, 0, 0, 0, 0,20,21,18,17, 0, 0, 0,21,21,
	 0, 0, 0,19,19, 0,21, 0,18,19, 0, 0, 0, 0, 0, 0,
	21,20,17,17, 0, 0,21,20, 0, 0, 0, 0,19,19, 0,19,
	20,15,16, 0, 0,20,18,17, 0,20,21,17,18, 0,21, 0,
	18,18, 0, 0, 0,19,19, 0,20,20,17,18, 0, 0, 0,18,
	19, 0,20,20,18,17, 0, 0, 0, 0,20, 0, 0,21,17,18,
	 0,20,21,17,17, 0, 0, 0,18,18, 0,19,19,17,17, 0,
	 0, 0,21,21, 0,20,20,17,17, 0, 0, 0,21,19, 0, 0,
	 0,20,19, 0,21,20,17,18, 0, 0, 0, 0, 0, 0, 0,20,
	18,17, 0,21,20,18,18, 0, 0, 0,20,21, 0,20,20,17,
	17, 0, 0, 0, 0, 0, 0,20, 0,17,17, 0,11,11,13,14,
	 0,13,13,16,16, 0,13,13,16,16, 0,17,17, 0, 0, 0,
	17,18, 0, 0, 0,13,13,16,16, 0,15,16,18,18, 0,13,
	13,16,17, 0,16,17,20, 0, 0,17,18,20, 0, 0,13,13,
	17,17, 0,16,16,20,21, 0,13,13,16,16, 0,17,17,21,
	 0, 0,17,18, 0, 0, 0,17,18, 0,21, 0, 0, 0, 0, 0,
	 0,15,15,20, 0, 0,19,19, 0, 0, 0,17,17, 0, 0, 0,
	18,17,21,20, 0, 0, 0, 0, 0, 0,16,16,20,21, 0,21,
	20, 0,21, 0,19,21, 0, 0, 0,15,15, 0, 0, 0,16,17,
	 0,19, 0,16,16, 0, 0, 0,17,17, 0, 0, 0,19,18, 0,
	 0, 0,16,16,20,20, 0,20,18,21, 0, 0,15,15,21,21,
	 0,18,18, 0, 0, 0,18,19, 0, 0, 0,16,15, 0,21, 0,
	20,19, 0, 0, 0,16,16, 0, 0, 0,20,18, 0,21, 0,17,
	18,21, 0, 0,18,19, 0, 0, 0, 0, 0, 0, 0, 0,16,16,
	20,20, 0,19,20, 0, 0, 0,17,17, 0, 0, 0,18,17,20,
	21, 0, 0, 0, 0, 0, 0,16,16, 0,20, 0,20,22, 0, 0,
	 0,18,18, 0,22,
};

static const static_codebook _44p0_p4_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p0_p4_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p0_p4_0,
	0
};

static const long _vq_quantlist__44p0_p4_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p0_p4_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p0_p4_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p0_p4_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p0_p4_1,
	0
};

static const long _vq_quantlist__44p0_p5_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p5_0[] = {
	 1, 6, 6, 6, 8, 8, 7, 8, 8, 7, 9, 8,10,11,11, 9,
	 8, 8, 7, 8, 8,11,11,11, 9, 8, 8, 6, 7, 7,10,10,
	10,10,10,10,10,10,10,14,13,13,12,11,11,10,10,10,
	14,14,13,13,11,11, 6, 6, 6, 8, 5, 5, 8, 7, 7, 8,
	 7, 7,11, 9, 9, 9, 7, 7, 8, 7, 7,12,10,10,10, 7,
	 7, 7, 8, 8,12,11,11,12,10,10,11,10,10,14,13,13,
	13,10,10,11,10,11,16,14,14,13,10,10, 7, 8, 7,12,
	12,12,12,11,11,12,11,11,16,14,15,13,12,12,11,11,
	11,17,15,14,14,13,13,10, 9, 9,13,11,11,13,11,11,
	12,11,11,16,14,13,14,11,11,12,11,11,16,15,14,14,
	11,11, 7, 8, 8,12,11,11,12,10,10,12,10,10,16,14,
	13,13,11,11,12,10,10,16,14,14,13,10,10, 8, 8, 8,
	12,12,12,12,11,11,12,11,11,16,14,15,14,12,12,12,
	11,11,16,15,15,14,12,12,10,10,10,13,11,11,13,11,
	11,12,12,12,16,14,14,14,11,11,12,11,11,17,14,15,
	14,11,11,
};

static const static_codebook _44p0_p5_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p5_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p0_p5_0,
	0
};

static const long _vq_quantlist__44p0_p5_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p5_1[] = {
	 2, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8, 8, 8, 8, 9, 8,
	 7, 7, 8, 8, 8, 9, 9, 9, 9, 7, 7, 6, 6, 6, 9, 7,
	 7, 9, 7, 7, 9, 8, 8,10, 8, 8,10, 8, 8,10, 8, 8,
	10, 8, 8,10, 8, 8, 7, 6, 6, 9, 6, 6, 9, 6, 6, 9,
	 7, 7,10, 8, 8, 9, 6, 6, 9, 7, 7,10, 8, 8, 9, 7,
	 7, 7, 8, 8,11, 9, 9,11, 9, 9,11, 9, 9,12, 9, 9,
	12, 8, 8,12, 9, 9,12,10, 9,12, 8, 8, 8, 7, 7,10,
	 9, 9,11, 9, 9,11, 9, 9,11,11,10,11, 9, 9,11,10,
	 9,11,10,11,11, 9, 9,10, 8, 8,11, 9, 9,11, 9, 9,
	11, 9, 9,11,10,10,11, 9, 9,11, 9, 9,11,10,10,11,
	 9, 9, 9, 8, 8,12, 9, 9,12, 9, 9,11, 9, 9,12, 9,
	 9,12, 8, 8,12, 9, 9,12, 9, 9,12, 8, 8, 9, 7, 7,
	11, 9,10,11,10, 9,11, 9, 9,11,11,11,11, 9, 9,11,
	10,10,11,11,11,11, 9, 9,10, 9, 9,11, 9, 9,11,10,
	10,11,10, 9,11,10,10,11, 9, 9,11,10,10,11,10,11,
	11, 9, 9,
};

static const static_codebook _44p0_p5_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p5_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p0_p5_1,
	0
};

static const long _vq_quantlist__44p0_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p0_p6_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p0_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p0_p6_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p0_p6_0,
	0
};

static const long _vq_quantlist__44p0_p6_1[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p0_p6_1[] = {
	 1, 3, 2, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9,10,10,11,
	11,12,12,12,14,14,14,15,15,
};

static const static_codebook _44p0_p6_1 = {
	1, 25,
	(char *)_vq_lengthlist__44p0_p6_1,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p0_p6_1,
	0
};

static const long _vq_quantlist__44p0_p6_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p0_p6_2[] = {
	 3, 4, 4, 5, 4, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p0_p6_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p0_p6_2,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p0_p6_2,
	0
};

static const char _huff_lengthlist__44p0_short[] = {
	 3, 3, 7, 8,10,13,16, 3, 2, 5, 7, 9,13,16, 6, 4,
	 4, 6,10,14,15, 7, 5, 5, 7,10,13,14, 9, 8, 9, 9,
	 9,11,13,12,11,12, 9, 7, 8,11,14,12,10, 6, 5, 7,
	10,
};

static const static_codebook _huff_book__44p0_short = {
	2, 49,
	(char *)_huff_lengthlist__44p0_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p1_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p1_l0_0[] = {
	 1, 4, 4, 7, 7, 8, 8, 9, 9,10,10,11,11, 4, 6, 5,
	 8, 6, 9, 8,10, 9,10,10,11,10, 5, 5, 6, 6, 8, 8,
	 9, 9,10,10,10,10,11, 7, 8, 8, 9, 8,10, 9,10, 9,
	11,10,11,10, 7, 8, 8, 8,10, 9,10,10,10,10,11,10,
	11, 9,10,10,11,11,11,11,12,11,12,11,12,11, 9,10,
	10,11,11,11,11,11,11,11,12,11,12,11,11,11,12,12,
	12,12,12,12,12,12,12,11,11,12,11,12,12,12,12,12,
	12,12,12,11,12,12,12,12,12,13,12,13,12,12,12,12,
	12,12,12,12,12,13,13,13,13,12,13,12,12,12,12,12,
	13,13,12,13,12,13,12,13,12,12,12,12,13,13,13,13,
	13,13,12,12,12,12,12,11,12,
};

static const static_codebook _44p1_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p1_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p1_l0_0,
	0
};

static const long _vq_quantlist__44p1_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p1_l0_1[] = {
	 1, 4, 4, 6, 6, 5, 5, 5, 6, 6, 5, 6, 5, 6, 6, 6,
	 6, 7, 7, 7, 6, 7, 6, 7, 7,
};

static const static_codebook _44p1_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p1_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p1_l0_1,
	0
};

static const long _vq_quantlist__44p1_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p1_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p1_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p1_l1_0,
	0
};

static const char _huff_lengthlist__44p1_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p1_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p1_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p1_long[] = {
	 3, 3, 7, 7, 9,13,16, 3, 2, 4, 6,10,13,17, 7, 4,
	 4, 6, 9,12,14, 7, 6, 6, 5, 7, 9,12,10,10, 9, 6,
	 6, 9,12,14,14,13, 9, 8,10,11,18,18,15,13,11,10,
	11,
};

static const static_codebook _huff_book__44p1_long = {
	2, 49,
	(char *)_huff_lengthlist__44p1_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p1_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p1_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p1_p1_0,
	0
};

static const long _vq_quantlist__44p1_p2_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p2_0[] = {
	 1, 4, 4, 0, 7, 7, 0, 8, 8, 0, 9, 9, 0,12,12, 0,
	 8, 8, 0, 9, 9, 0,12,12, 0, 8, 8, 0, 6, 6, 0,11,
	11, 0,11,11, 0,12,12, 0,14,14, 0,11,11, 0,12,12,
	 0,14,14, 0,11,11, 0, 6, 6, 0, 6, 5, 0, 7, 6, 0,
	 7, 7, 0,10,10, 0, 6, 6, 0, 7, 7, 0,10,10, 0, 7,
	 7, 0, 7, 7, 0,10,10, 0,11,11, 0,11,11, 0,14,14,
	 0,10,10, 0,12,12, 0,14,14, 0,12,12, 0, 6, 6, 0,
	11,11, 0,11,11, 0,12,12, 0,14,14, 0,11,11, 0,12,
	12, 0,15,15, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 8, 8, 0,11,11, 0,11,11, 0,12,12, 0,15,
	15, 0,12,12, 0,11,11, 0,15,15, 0,11,11, 0, 6, 6,
	 0,11,11, 0,12,12, 0,12,12, 0,15,15, 0,11,11, 0,
	12,12, 0,14,14, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p1_p2_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p2_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p1_p2_0,
	0
};

static const long _vq_quantlist__44p1_p2_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p2_1[] = {
	 1, 3, 3, 0, 8, 8, 0, 8, 8, 0,10,10, 0, 9, 9, 0,
	10,10, 0,10,10, 0, 9, 9, 0,10,10, 0, 7, 7, 0, 7,
	 7, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 9, 9,
	 0, 8, 8, 0, 8, 8, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0,
	10,10, 0, 9, 9, 0, 9, 9, 0,10,10, 0, 9, 9, 0,10,
	10, 0, 8, 8, 0,11,11, 0,11,11, 0,12,12, 0,11,11,
	 0,12,12, 0,12,12, 0,12,12, 0,12,12, 0, 8, 8, 0,
	11,11, 0,11,11, 0,13,12, 0,12,12, 0,13,12, 0,13,
	13, 0,12,12, 0,13,13, 0, 7, 7, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 8, 8, 0,11,11, 0,11,11, 0,13,12, 0,12,
	12, 0,12,12, 0,12,12, 0,11,11, 0,12,12, 0, 8, 8,
	 0,12,12, 0,12,12, 0,13,13, 0,12,12, 0,13,13, 0,
	13,13, 0,12,13, 0,13,13, 0, 7, 7, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p1_p2_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p2_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p1_p2_1,
	0
};

static const long _vq_quantlist__44p1_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p3_0[] = {
	 1, 6, 6, 6, 7, 7, 7, 8, 8, 7, 8, 8,10,11,11, 9,
	 8, 8, 7, 9, 9,11,12,12, 9, 8, 8, 6, 7, 7, 9,11,
	11,10,11,11,10,11,11,13,13,13,11,12,12,10,11,11,
	13,14,14,12,12,12, 6, 6, 6, 8, 6, 6, 8, 6, 6, 9,
	 7, 7,12,10,10,10, 6, 6, 9, 7, 7,12,10,10,11, 7,
	 6, 7, 8, 8,12,10,10,12,10,10,11,10,10,15,13,13,
	13,10,10,12,11,11,15,13,13,14,11,11, 8, 7, 7,12,
	11,11,12,11,11,11,11,11,14,14,14,13,12,12,12,11,
	11,16,15,15,14,12,12, 0,10,10, 0,11,11, 0,12,12,
	 0,11,11, 0,14,14, 0,11,11, 0,11,11, 0,15,15, 0,
	11,11, 7, 8, 8,13,10,10,12,10,10,12,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,14,10,10, 8, 7, 7,
	12,11,11,13,11,11,12,11,11,15,14,14,14,12,12,13,
	12,12,15,14,14,15,12,12, 0,11,11, 0,12,12, 0,12,
	12, 0,12,12, 0,15,15, 0,12,12, 0,12,12, 0,15,14,
	 0,12,12,
};

static const static_codebook _44p1_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p3_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p1_p3_0,
	0
};

static const long _vq_quantlist__44p1_p3_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p1_p3_1[] = {
	 2, 3, 4, 7, 7,10,12,12,12,12,10,11,11,13,13,11,
	12,12,11,11,12,12,12,12,12,11,13,13,13,13,12,12,
	12,13,14,12,13,13,13,13,12,13,13,13,13,12,13,13,
	13,13,11,13,13,13,13,12,12,12,14,14,12,13,13,12,
	12,12,12,13,13,13,12,13,13,13,13,12,13,13,13,13,
	12,12,12,14,14,12,13,13,12,12,12,13,13,13,13,12,
	13,13,12,12,12,13,13,13,13,12,12,12,14,14,12,13,
	13,12,12,12,13,13,13,13,12,13,13,12,12,10,10,11,
	10,10,11,11,11,11,11,11, 9, 9,10,10,12,11,11,10,
	10,12,10,10,10,10,13,12,12,12,12,13,11,11,11,11,
	13,12,12,12,12,13,11,11,11,11,13,12,12,12,12,13,
	12,12,12,12,13,11,11,11,11,13,12,12,12,12,13,11,
	11,11,11,13,12,12,11,11,13,12,12,11,11,13,11,11,
	11,11,13,12,12,11,11,13,11,11,11,11,13,12,12,11,
	11,13,12,12,11,11,13,11,11,11,11,13,12,12,11,11,
	13,11,11,11,11,13,12,12,11,11,11,11,11,10,10,11,
	11,11, 9, 9,11,12,12,11,11,12,12,12, 9, 9,13,13,
	13,10,10,13,13,13,11,11,13,13,13,14,14,13,13,13,
	11,10,13,13,14,12,12,13,13,13,11,11,13,13,13,11,
	11,13,13,13,14,14,13,13,13,10,10,13,13,13,11,11,
	13,13,13,10,10,13,14,13,11,11,13,14,14,14,14,13,
	13,13,10,10,13,14,14,11,11,13,13,13,10,10,13,14,
	14,11,11,13,13,13,14,14,14,13,13,10,10,13,14,14,
	11,11,13,13,13,10,10,14,12,12, 9, 9,14,12,12, 9,
	 9,14,11,11, 9, 9,14,12,12, 8, 8,14,11,11, 7, 7,
	15,13,13,10,10,15,12,12,10,10,15,13,13,10,10,15,
	12,12,10,10,15,13,13,10,10,15,13,13,10,10,15,12,
	12,10,10,15,13,13,10,10,15,12,12,10,10,15,13,13,
	10,10,15,13,13,10,10,15,12,12,10,10,15,13,13, 9,
	 9,15,12,12, 9, 9,14,13,13, 9, 9,15,13,13,10,10,
	15,12,12,10,10,15,13,13, 9, 9,15,12,12, 9, 9,15,
	13,13, 9, 9,13,12,12, 9, 9,13,13,13, 8, 8,13,13,
	13, 9, 9,13,13,13, 7, 7,14,13,13, 8, 8,14,14,14,
	10,10,15,14,14,11,11,14,14,14, 9, 9,15,14,14,10,
	10,15,14,14, 9, 9,14,14,14,10,10,15,14,14,11,11,
	15,14,14, 9, 9,14,14,14,10,10,14,14,14, 9, 9,15,
	14,15,10,10,15,14,14,11,11,14,14,14, 9, 9,14,14,
	14, 9, 9,14,14,14, 8, 8,15,14,14,10,10,15,14,14,
	11,11,14,14,14, 9, 9,15,14,14, 9, 9,14,14,14, 8,
	 8,12,12,12,13,13,16,16,16,11,11,17,16,16,12,12,
	17,16,16,11,11,17,16,16,11,11,17,17,16,13,13,17,
	16,16,13,13,18,17,16,12,12,17,16,16,13,13,17,16,
	17,12,12,18,17,17,13,13,17,16,16,14,14,18,17,17,
	12,12,18,16,16,13,13,17,17,17,13,12,17,17,17,13,
	13,17,16,16,13,13,18,17,17,12,12,17,16,16,13,12,
	17,17,17,12,12,18,17,17,13,13,18,16,16,14,14,18,
	17,17,12,12,17,17,17,13,13,18,17,18,12,12,13,14,
	14,10,10,16,14,14,13,13,17,15,15,14,14,17,14,14,
	12,13,16,14,14,13,13,17,15,15,14,14,16,16,16,15,
	15,17,15,15,14,14,17,16,16,14,15,17,15,15,14,14,
	17,15,16,14,14,17,16,16,15,15,17,15,15,13,13,17,
	15,15,14,14,18,15,15,13,14,17,15,15,14,14,16,16,
	16,15,15,17,15,15,13,13,17,15,15,14,14,17,15,15,
	13,13,17,15,15,14,14,16,16,16,15,15,17,15,15,13,
	13,17,15,15,14,14,18,15,15,13,13,13,11,11,10,10,
	16,14,14,13,12,16,14,14,13,13,16,15,14,12,12,16,
	14,14,12,12,16,15,15,14,14,16,14,14,14,14,17,15,
	15,13,13,16,15,15,14,14,17,15,15,13,14,17,15,15,
	14,14,17,15,14,14,14,17,15,15,13,13,17,15,15,14,
	14,17,15,15,13,13,17,15,15,14,14,17,14,14,14,14,
	17,15,15,13,13,17,15,15,13,13,17,15,15,13,13,17,
	15,15,14,14,17,15,15,14,14,17,15,15,13,13,17,15,
	15,13,13,17,15,15,13,13,14,14,15, 8, 8,14,14,14,
	19,19,14,15,15,18,19,14,14,14,19,18,14,14,14,19,
	19,15,15,15,19,18,15,16,16,19,19,15,15,15,19,19,
	15,16,16,20,19,15,15,15,19,19,15,15,15,19,19,16,
	16,16,20,19,15,15,15,19,18,15,16,16,20,19,15,15,
	15,18,18,15,15,15,19,20,15,16,16,19,19,15,15,15,
	20,19,15,15,15,20,19,15,15,15,19,18,15,15,15,19,
	19,15,16,16,19,20,15,15,15,19,19,15,15,15,19,20,
	15,15,15,19,19,14,12,12, 9, 9,14,14,14,19,19,14,
	14,14,19,19,14,14,15,20,19,15,14,14,18,19,15,15,
	15,19,19,15,15,14,20,19,15,15,15,20,19,15,15,14,
	20,19,15,15,15,20,19,15,15,15,19,20,15,14,14,19,
	20,15,15,15,20,20,15,14,14,20,19,15,15,15,19,19,
	15,15,15,19,19,15,14,14,19,19,15,15,15,19,20,15,
	15,15,20,20,15,15,15,19,19,15,15,15,20,19,16,14,
	14,19,19,15,15,15,20,19,15,14,15,20,19,14,15,15,
	20,19,12,12,12,13,13,16,16,16,11,11,16,16,16,12,
	12,17,16,16,11,11,17,15,16,11,11,17,17,17,13,13,
	18,16,17,13,13,18,17,17,13,12,17,16,17,13,13,17,
	17,17,13,13,16,16,16,12,12,17,16,16,13,13,17,16,
	16,12,12,17,16,16,12,13,17,17,17,12,12,17,17,17,
	13,13,18,16,16,13,13,18,17,17,12,12,18,17,17,12,
	12,17,17,17,12,12,17,17,17,12,12,17,16,16,13,13,
	17,17,17,12,12,17,16,16,12,12,17,17,17,12,12,13,
	14,14, 9, 9,16,14,14,13,13,16,15,15,14,14,17,14,
	14,13,13,16,14,14,13,13,17,15,15,15,15,16,16,16,
	15,15,17,15,15,14,14,17,15,15,15,15,17,15,15,14,
	14,17,15,15,14,14,16,16,16,15,15,17,15,15,14,14,
	17,15,15,14,14,17,15,15,14,14,17,15,15,14,14,16,
	16,16,15,15,18,15,15,14,13,17,15,15,14,14,17,15,
	15,13,13,17,15,15,14,14,16,16,16,15,15,17,15,15,
	14,13,17,15,15,14,14,17,15,15,13,13,13,11,11,11,
	11,16,14,14,12,12,16,14,14,13,13,16,15,14,12,12,
	17,14,14,12,12,17,15,15,13,13,17,14,14,14,14,17,
	15,15,13,13,17,14,15,14,13,17,15,15,13,13,16,15,
	15,13,13,16,14,14,14,14,17,15,15,13,13,16,14,14,
	13,13,16,15,15,13,13,17,15,15,13,13,17,14,14,14,
	14,17,15,15,12,12,17,15,15,13,13,17,15,15,12,12,
	16,15,15,13,13,17,14,14,13,14,17,15,15,12,12,17,
	14,14,13,13,17,15,15,12,12,14,14,14, 8, 8,14,14,
	14,18,18,14,15,15,19,19,14,14,14,19,19,14,15,14,
	18,19,15,15,15,18,19,15,16,16,20,20,15,15,15,19,
	20,15,16,16,19,20,15,15,15,19,20,15,15,16,19,19,
	15,16,16,20,20,15,15,15,20,19,15,16,16,20,19,15,
	15,15,19,20,15,15,15,19,19,15,16,16,20,19,15,15,
	15,19,19,15,16,15,20,19,15,15,15,19,19,15,15,15,
	19,20,15,16,16,20,20,15,15,15,19,19,15,15,15,20,
	20,15,15,15,19,19,14,12,12, 9, 9,14,14,14,18,18,
	14,14,14,19,20,14,14,14,18,18,14,14,14,18,19,15,
	15,15,19,20,15,14,14,19,19,15,15,15,19,19,15,14,
	15,19,19,15,15,15,18,20,15,15,15,19,19,15,14,14,
	19,19,15,15,15,20,19,15,15,14,20,20,15,15,15,19,
	19,15,15,15,19,19,15,14,14,19,19,15,15,15,19,19,
	15,14,14,19,20,14,15,15,19,19,15,15,15,19,19,15,
	14,14,20,19,15,15,15,19,19,15,14,14,20,19,15,15,
	15,19,19,13,12,12,13,13,17,17,16,11,11,16,16,16,
	12,12,17,17,16,11,11,17,16,16,11,11,17,17,17,13,
	13,17,16,16,13,13,18,17,17,12,12,17,16,16,13,13,
	18,17,17,12,12,18,17,17,13,13,18,16,17,13,13,17,
	17,17,12,12,18,17,17,13,13,18,17,17,12,12,17,16,
	17,12,12,17,16,16,13,13,17,16,16,11,11,17,16,16,
	12,12,17,17,17,11,11,17,17,17,12,12,18,16,16,13,
	13,18,17,17,12,11,17,16,16,12,12,18,17,17,11,11,
	13,14,14, 9, 9,16,14,14,13,13,16,15,15,14,14,17,
	14,14,12,12,16,14,14,13,13,17,15,15,14,14,17,16,
	16,15,16,18,15,15,14,14,17,15,15,14,14,17,15,15,
	14,14,18,15,15,14,14,16,16,16,15,16,18,15,15,14,
	14,17,16,15,14,14,18,15,15,14,14,17,15,15,14,14,
	17,16,16,15,15,18,14,15,13,13,17,15,15,14,14,18,
	15,15,13,13,17,15,15,14,14,17,16,15,15,15,17,15,
	15,13,13,17,15,15,14,14,18,15,15,13,13,13,11,11,
	10,10,16,14,14,12,12,16,14,14,12,12,17,14,15,11,
	11,17,14,14,11,11,17,15,15,13,13,17,14,14,14,13,
	17,15,15,13,13,16,15,15,13,13,17,15,15,13,13,17,
	15,15,13,13,17,14,14,14,14,17,15,15,13,13,17,14,
	15,13,13,16,15,15,13,13,17,15,15,13,13,17,14,14,
	13,13,17,15,15,12,12,16,14,14,12,12,17,15,15,12,
	12,17,15,15,13,13,17,14,14,13,13,17,15,15,12,12,
	17,14,14,12,12,17,15,15,12,12,13,15,14, 8, 8,14,
	14,14,19,19,14,15,15,18,19,14,14,14,18,19,14,15,
	14,19,19,15,16,15,19,19,15,16,16,19,20,15,15,15,
	19,19,15,16,16,19,19,15,16,16,19,19,15,15,15,19,
	19,15,16,16,20,20,15,15,15,19,19,15,15,15,19,19,
	15,15,15,19,19,15,15,15,19,19,15,16,16,20,19,15,
	15,15,19,19,15,15,15,19,19,15,15,15,19,19,15,16,
	15,19,19,15,16,16,21,19,15,15,15,20,20,15,15,15,
	20,21,15,15,15,19,20,14,12,12, 8, 8,14,14,14,19,
	19,14,13,13,19,19,14,14,14,19,19,14,13,14,19,19,
	15,15,15,20,20,15,14,14,20,19,15,15,15,19,20,15,
	14,14,19,20,15,15,15,20,19,15,15,15,19,20,15,14,
	14,20,20,15,15,15,20,19,15,14,14,19,19,15,15,15,
	19,19,15,15,15,20,19,15,14,14,21,19,15,15,15,20,
	21,15,14,14,21,19,15,15,15,19,19,15,15,15,20,20,
	15,14,14,19,21,15,15,15,19,19,15,14,14,19,20,15,
	15,15,19,19,13,12,12,13,13,17,16,16,11,11,17,16,
	15,12,12,18,16,16,11,11,17,16,16,11,11,18,17,17,
	13,13,18,16,16,13,13,17,17,17,12,13,18,17,16,13,
	13,18,17,17,13,13,17,17,17,13,13,17,16,16,13,13,
	18,16,17,12,12,17,16,16,13,12,17,17,17,12,12,18,
	17,17,13,12,18,16,16,13,13,18,17,17,12,12,17,16,
	16,12,12,17,17,17,11,11,17,16,16,12,12,17,16,16,
	13,13,17,16,16,11,11,17,16,16,12,12,17,17,17,11,
	11,13,14,14, 9, 9,16,14,14,13,13,16,15,15,14,14,
	17,14,14,12,12,16,14,14,13,13,17,15,15,14,14,17,
	15,16,15,15,17,15,15,14,14,17,15,16,14,15,18,15,
	15,14,14,17,15,15,14,14,16,16,16,15,15,18,15,15,
	13,14,17,15,15,14,14,18,15,15,14,14,17,15,15,14,
	14,17,16,16,15,15,17,15,15,13,13,17,15,15,14,14,
	18,15,15,13,13,17,15,15,14,14,17,16,16,15,15,17,
	15,15,13,13,17,15,15,14,14,18,15,15,13,13,13,11,
	11,10,10,16,14,14,12,12,16,14,14,13,13,17,14,14,
	11,11,17,14,14,12,12,17,15,15,14,14,17,14,14,14,
	14,17,15,15,13,13,17,15,14,13,13,16,15,15,13,13,
	16,15,15,13,13,17,14,14,14,14,17,15,15,13,13,17,
	14,14,13,13,16,15,15,13,13,16,15,15,13,13,17,14,
	14,13,13,17,15,15,12,12,17,14,14,12,12,16,15,15,
	12,12,17,15,15,13,13,17,14,14,13,13,17,15,15,12,
	12,17,14,14,12,12,16,15,15,12,12,14,14,14, 8, 8,
	14,14,14,18,18,14,15,15,19,18,14,14,14,18,18,14,
	14,14,18,19,15,16,15,19,19,15,17,16,20,20,15,15,
	15,19,19,15,16,16,19,19,15,15,15,19,19,15,16,15,
	18,19,15,16,16,20,20,15,15,15,19,19,15,16,16,19,
	20,15,15,15,19,19,15,15,16,19,19,15,16,16,20,20,
	15,15,15,19,19,15,15,15,19,20,15,15,15,19,19,15,
	15,15,19,19,15,16,16,20,20,15,15,15,19,20,15,16,
	16,20,20,15,15,15,19,19,13,12,12, 8, 8,14,14,14,
	19,20,14,14,14,19,19,14,14,14,18,19,14,14,14,19,
	20,15,15,15,19,20,15,14,14,21,20,15,15,15,20,20,
	15,15,14,19,19,15,15,15,19,19,15,15,15,19,19,15,
	14,14,19,20,15,15,15,19,20,15,14,14,19,19,15,15,
	15,19,19,15,15,15,19,19,16,14,14,19,19,15,15,15,
	20,20,15,14,14,21,19,15,15,15,19,19,15,15,15,19,
	20,16,14,14,19,20,15,15,15,19,19,15,14,14,19,19,
	15,15,15,20,19,
};

static const static_codebook _44p1_p3_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p1_p3_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p1_p3_1,
	0
};

static const long _vq_quantlist__44p1_p4_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p1_p4_0[] = {
	 2, 6, 6,14,14, 6, 7, 7,14,14, 7, 7, 7,14,14, 0,
	13,13,16,16, 0,13,13,15,14, 7, 8, 8,15,15, 9,10,
	10,16,16, 9, 8, 8,15,15, 0,13,13,17,16, 0,13,13,
	15,16, 8, 8, 8,15,15,12,11,11,16,16, 9, 8, 8,14,
	14, 0,13,13,17,18, 0,13,13,15,15, 0,14,14,16,16,
	 0, 0, 0,19,18, 0,12,12,16,15, 0,15,16, 0,20, 0,
	14,14,16,16, 0,14,14,17,17, 0, 0, 0,19,18, 0,12,
	12,15,15, 0,17,17, 0,20, 0,14,14,16,16, 5, 6, 7,
	12,12, 9, 9, 9,14,14,10,10,10,14,14, 0,21,21,18,
	17, 0,20,20,18,17, 9,10,10,14,14,12,12,12,16,16,
	12,10,10,14,14, 0,20,19,18,17, 0, 0,20,17,18,11,
	10,10,14,14,14,13,13,18,18,13,11,11,14,14, 0,20,
	20,17,18, 0,21,21,17,17, 0,21, 0,18,18, 0, 0, 0,
	 0, 0, 0,20,19,16,17, 0, 0, 0,19,19, 0, 0, 0,18,
	18, 0,21,21,18,18, 0, 0, 0, 0, 0, 0,20,20,16,17,
	 0, 0, 0,21,21, 0, 0, 0,18,19, 6, 6, 6,13,12, 8,
	 6, 6,11,11, 8, 6, 6,13,13, 0, 9, 9,11,11, 0,11,
	10,14,14, 9, 7, 7,13,13,11, 9, 9,13,13,10, 6, 6,
	13,13, 0,10,10,14,15, 0,10,10,13,13, 9, 7, 7,13,
	13,13,10, 9,13,13,10, 6, 6,13,13, 0,10,10,15,14,
	 0,10,10,13,13, 0,11,11,15,15, 0,19,20,17,17, 0,
	 9, 9,13,13, 0,13,13,20,20, 0,11,11,13,13, 0,11,
	11,15,15, 0,19,19,17,17, 0,10,10,13,13, 0,15,15,
	20,20, 0,12,12,13,13, 0,10,10,12,12, 0,11,11,15,
	15, 0,11,11,15,15, 0,15,15,20, 0, 0,16,16, 0,21,
	 0,11,11,15,15, 0,14,14,18,17, 0,11,11,15,15, 0,
	15,16,19,20, 0,16,16,21,21, 0,12,12,15,15, 0,15,
	14,18,18, 0,11,11,16,16, 0,15,15,21,21, 0,16,15,
	 0, 0, 0,16,16,21, 0, 0, 0, 0, 0, 0, 0,14,14,20,
	20, 0,18,18, 0, 0, 0,16,17,21, 0, 0,16,16,21,21,
	 0, 0, 0, 0, 0, 0,15,15,21,21, 0,20,19, 0,21, 0,
	17,17, 0, 0, 0,10,10,12,11, 0,10,10,10,11, 0,11,
	11,12,12, 0,11,11, 9, 9, 0,13,13,11,12, 0,11,11,
	12,12, 0,13,13,12,12, 0,10,10,12,12, 0,12,12,13,
	13, 0,12,12,12,12, 0,11,11,12,12, 0,13,13,12,12,
	 0,10,10,12,12, 0,13,13,14,14, 0,12,12,12,12, 0,
	14,14,14,13, 0,19,20,15,15, 0,12,11,12,12, 0,15,
	15,21,20, 0,13,13,11,11, 0,13,13,13,13, 0,19, 0,
	15,15, 0,12,12,12,12, 0,17,16,19, 0, 0,13,13,12,
	12, 7, 7, 7,16,16,11, 9, 9,15,15,12, 9, 9,16,16,
	 0,13,13,15,14, 0,14,14,17,16,10, 9, 9,16,16,14,
	11,11,17,16,12, 9, 8,15,15, 0,13,13,18,18, 0,13,
	13,15,15,12,10,10,18,17,15,12,12,17,17,14, 9, 9,
	16,16, 0,13,13,18,19, 0,14,13,17,16, 0,14,14,18,
	18, 0, 0, 0,20,21, 0,12,12,16,16, 0,16,16,20,21,
	 0,14,14,17,16, 0,14,14,18,19, 0, 0, 0,19,21, 0,
	13,13,17,17, 0,17,17, 0,21, 0,15,15,16,16, 8, 7,
	 7,14,14,11,10,10,15,15,12,10,10,15,15, 0,20,20,
	18,18, 0, 0, 0,17,17,11,10,10,16,16,14,12,12,18,
	17,14,11,11,15,15, 0,20,21,18,18, 0, 0,19,18,17,
	12,10,10,16,16,17,14,14,19,19,14,11,11,15,15, 0,
	21,21,19,19, 0,21,20,19,18, 0,21, 0,18,19, 0, 0,
	 0, 0, 0, 0,20,20,18,17, 0,21, 0, 0, 0, 0, 0, 0,
	19,18, 0, 0, 0,18,19, 0, 0, 0, 0, 0, 0, 0,21,17,
	18, 0, 0, 0, 0,21, 0, 0,21,18,19,11, 9, 9,14,14,
	13,10,10,13,13,13,11,11,15,15, 0,13,13,12,12, 0,
	15,15,16,16,13,10,10,15,15,16,12,12,15,15,15,10,
	10,15,15, 0,14,13,16,15, 0,14,13,15,15,13,10,10,
	15,15,18,14,14,15,15,15,10,10,14,15, 0,14,14,16,
	16, 0,14,14,16,15, 0,15,15,17,16, 0,21, 0,18,18,
	 0,12,13,15,15, 0,16,16, 0, 0, 0,14,14,15,15, 0,
	15,15,16,16, 0,21,20,18,18, 0,13,13,15,15, 0,19,
	18, 0, 0, 0,15,15,15,15, 0,11,11,13,13, 0,12,12,
	16,16, 0,12,12,16,16, 0,15,16,20, 0, 0,16,17, 0,
	 0, 0,12,12,16,16, 0,14,14,18,18, 0,11,11,16,17,
	 0,15,15,20, 0, 0,16,16, 0, 0, 0,12,12,16,16, 0,
	15,15,19,19, 0,11,11,17,17, 0,16,16,21, 0, 0,16,
	16, 0, 0, 0,17,17,20,20, 0, 0, 0, 0, 0, 0,15,15,
	20, 0, 0,17,18, 0, 0, 0,17,17, 0, 0, 0,16,16, 0,
	21, 0, 0, 0, 0, 0, 0,15,15,21, 0, 0,19,18, 0, 0,
	 0,18,17, 0, 0, 0,11,11,14,14, 0,11,11,15,15, 0,
	12,12,16,16, 0,13,13,14,14, 0,14,14,17,17, 0,12,
	12,16,16, 0,14,14,16,16, 0,11,11,16,15, 0,13,13,
	16,17, 0,13,13,16,16, 0,12,12,15,16, 0,15,14,16,
	16, 0,11,11,15,15, 0,14,14,17,17, 0,13,13,16,16,
	 0,15,14,18,18, 0,21, 0,19,19, 0,13,13,15,15, 0,
	16,16,20,20, 0,14,14,16,15, 0,14,14,17,17, 0,21,
	 0,20,18, 0,13,13,15,15, 0,17,17, 0, 0, 0,14,14,
	16,15, 8, 8, 8,16,16,12, 9, 9,16,16,13, 9, 9,16,
	16, 0,14,14,18,17, 0,14,14,16,17,12,10,10,18,17,
	14,11,11,18,18,14, 9, 9,16,16, 0,13,13,18,18, 0,
	13,13,17,16,12, 9, 9,16,17,17,13,13,16,16,14, 9,
	 9,15,15, 0,14,14,20,20, 0,13,13,15,15, 0,15,14,
	18,18, 0, 0, 0,20,21, 0,12,13,16,17, 0,16,16,20,
	21, 0,14,14,16,17, 0,14,14,18,17, 0, 0, 0,20,21,
	 0,13,13,16,16, 0,19,17, 0,21, 0,14,15,16,16, 8,
	 7, 7,14,13,12,10,10,15,15,13,10,10,15,15, 0,21,
	21,18,19, 0,20,21,18,18,12,10,10,16,15,15,12,12,
	17,17,14,11,11,15,15, 0,21,21,19,18, 0, 0,21,17,
	18,13,11,11,15,15,16,13,13,18,19,15,11,11,15,14,
	 0,21, 0,19,19, 0, 0,21,18,18, 0, 0,21,19,19, 0,
	 0, 0, 0, 0, 0,20,19,17,17, 0, 0, 0,21, 0, 0,21,
	 0,18,19, 0, 0,20,20,19, 0, 0, 0, 0, 0, 0,21,20,
	18,17, 0, 0, 0, 0,20, 0, 0, 0,18,19, 0,10,10,15,
	14, 0,11,11,14,14, 0,11,11,15,16, 0,14,14,15,15,
	 0,15,15,16,16, 0,11,11,16,16, 0,14,13,16,16, 0,
	11,11,15,15, 0,14,14,16,16, 0,14,14,15,15, 0,11,
	11,15,15, 0,13,13,15,15, 0,11,11,15,15, 0,15,15,
	18,17, 0,14,14,15,15, 0,15,16,18,18, 0, 0, 0,20,
	20, 0,14,13,16,15, 0,17,17,21, 0, 0,15,15,15,15,
	 0,16,15,17,17, 0, 0, 0,19,19, 0,13,13,15,15, 0,
	20,19, 0, 0, 0,15,15,15,15, 0,11,11,13,13, 0,12,
	12,16,16, 0,12,12,16,16, 0,15,15,21,21, 0,17,16,
	 0, 0, 0,12,12,16,16, 0,14,14,17,17, 0,11,11,16,
	16, 0,15,15, 0, 0, 0,16,16,21, 0, 0,12,12,17,16,
	 0,14,15,20,20, 0,11,11,16,16, 0,15,15, 0,20, 0,
	16,16, 0,21, 0,16,17,21, 0, 0, 0, 0, 0, 0, 0,15,
	15, 0,21, 0,18,18, 0, 0, 0,17,16, 0, 0, 0,17,17,
	21, 0, 0, 0, 0, 0, 0, 0,15,15, 0,20, 0,19,20,21,
	 0, 0,17,18, 0, 0, 0,12,12,15,15, 0,12,12,15,15,
	 0,12,12,16,16, 0,13,13,15,15, 0,15,15,17,17, 0,
	13,12,17,16, 0,14,14,17,16, 0,11,11,16,16, 0,14,
	14,17,17, 0,14,14,17,17, 0,12,12,16,16, 0,15,15,
	17,17, 0,11,11,16,16, 0,14,14,17,17, 0,14,14,16,
	16, 0,15,15,18,17, 0, 0, 0,19, 0, 0,13,13,16,16,
	 0,16,16, 0,21, 0,14,14,16,16, 0,15,15,18,17, 0,
	 0, 0,19,19, 0,13,13,16,16, 0,18,17, 0,21, 0,14,
	15,16,16, 0,11,11,16,16, 0,13,13,17,17, 0,13,13,
	17,17, 0,16,16,16,17, 0,16,16,18,18, 0,12,12,17,
	17, 0,16,15,18,17, 0,12,12,16,16, 0,16,15,19,19,
	 0,16,15,17,17, 0,12,12,17,18, 0,16,16,18,18, 0,
	12,12,16,16, 0,16,16,19,19, 0,15,16,17,17, 0,15,
	16,18,18, 0, 0, 0,20,20, 0,13,13,16,16, 0,18,18,
	21,20, 0,15,15,16,16, 0,16,16,19,18, 0, 0, 0,19,
	20, 0,14,14,17,17, 0,19,19, 0,21, 0,15,16,16,16,
	 0, 9, 9,14,14, 0,13,13,15,15, 0,14,14,15,15, 0,
	 0,21,19,19, 0, 0,21,18,18, 0,12,12,15,15, 0,15,
	15,18,18, 0,14,13,15,15, 0,21,21,18,19, 0,21,20,
	18,18, 0,13,13,16,16, 0,17,17,18,19, 0,14,14,15,
	15, 0, 0,21,19,19, 0,21,20,18,19, 0,20,20,19,19,
	 0, 0, 0, 0, 0, 0,19,20,17,17, 0, 0, 0,21,21, 0,
	21, 0,18,20, 0,21, 0,18,21, 0, 0, 0, 0, 0, 0,21,
	21,19,18, 0, 0, 0, 0, 0, 0, 0, 0,19,19, 0,18,18,
	15,15, 0,18,20,17,16, 0,20, 0,17,17, 0,21, 0,17,
	17, 0,21,20,19,20, 0,19,19,16,16, 0,21,21,17,18,
	 0,19,19,17,17, 0,20,21,21,21, 0,20,20,18,18, 0,
	19,19,16,16, 0, 0,21,18,19, 0,18,19,16,17, 0,21,
	21,19,20, 0,21,19,18,18, 0,21,20,19,21, 0, 0, 0,
	20,21, 0,19,19,17,16, 0, 0, 0, 0, 0, 0,21,20,17,
	17, 0,20,21,19,18, 0, 0, 0, 0,21, 0,19,18,16,17,
	 0, 0, 0, 0, 0, 0,20,20,17,17, 0,11,11,14,14, 0,
	13,13,16,16, 0,13,13,16,16, 0,17,17,21, 0, 0,17,
	18, 0, 0, 0,12,12,16,16, 0,15,15,17,18, 0,12,12,
	16,16, 0,16,16, 0,20, 0,17,17, 0,21, 0,12,12,17,
	17, 0,16,16,19,20, 0,12,12,17,17, 0,17,17, 0,20,
	 0,17,17, 0, 0, 0,17,17,21, 0, 0, 0, 0, 0, 0, 0,
	15,15, 0,20, 0,19,19, 0, 0, 0,18,18, 0, 0, 0,17,
	17, 0, 0, 0, 0, 0, 0, 0, 0,15,15, 0, 0, 0,20,19,
	 0, 0, 0,19,18, 0, 0, 0,14,14,21,19, 0,16,16,20,
	21, 0,16,16,20,20, 0,17,17,20, 0, 0,17,17,20,20,
	 0,15,15,20,20, 0,19,18,20, 0, 0,15,15,20,20, 0,
	17,18,21,20, 0,17,17,20,21, 0,15,15,19,19, 0,19,
	18,21,21, 0,15,15,19,20, 0,17,18, 0, 0, 0,17,17,
	20,20, 0,17,18,20,21, 0, 0, 0, 0, 0, 0,15,15,20,
	20, 0,19,19, 0, 0, 0,17,17,19,21, 0,17,17, 0,21,
	 0, 0, 0, 0,21, 0,15,15,20,19, 0, 0,20, 0, 0, 0,
	17,17,21,20, 0,12,12,16,16, 0,14,14,17,17, 0,13,
	13,17,17, 0,16,16,17,18, 0,17,16,18,18, 0,13,13,
	18,17, 0,15,16,19,18, 0,13,13,16,16, 0,16,16,19,
	19, 0,16,16,17,17, 0,13,12,17,17, 0,16,16,18,17,
	 0,12,12,16,16, 0,17,17,19,18, 0,16,15,16,16, 0,
	16,17,18,19, 0, 0, 0,20,20, 0,14,14,17,16, 0,18,
	18,21, 0, 0,16,16,16,16, 0,16,16,18,17, 0, 0,21,
	21,21, 0,14,14,16,16, 0,21,20,21, 0, 0,16,16,16,
	16, 0,10,10,14,14, 0,14,14,15,16, 0,14,14,15,15,
	 0, 0,21,18,18, 0, 0,21,18,19, 0,13,13,16,16, 0,
	16,16,18,17, 0,14,14,15,15, 0,20, 0,18,18, 0,21,
	 0,18,17, 0,13,13,16,15, 0,17,17,19,19, 0,14,14,
	15,15, 0,20,20,18,19, 0, 0, 0,18,17, 0, 0,21,18,
	18, 0, 0, 0, 0, 0, 0,20,21,18,17, 0, 0, 0, 0, 0,
	 0, 0, 0,19,19, 0, 0,21,18,18, 0, 0, 0, 0, 0, 0,
	21, 0,18,17, 0, 0, 0, 0,21, 0, 0, 0,19,20, 0,19,
	19,16,16, 0, 0,21,18,17, 0,21, 0,18,18, 0,20, 0,
	19,18, 0,21,20,19,19, 0,21,19,17,18, 0, 0,21,19,
	19, 0,21,19,18,18, 0,21, 0,20,18, 0, 0,21,18,18,
	 0,20,21,17,17, 0,21, 0,18,18, 0,21,19,17,17, 0,
	21, 0, 0,20, 0, 0,20,17,18, 0, 0, 0,19,20, 0, 0,
	 0,20,19, 0,19,21,17,18, 0,21, 0, 0, 0, 0,21,21,
	18,17, 0, 0,21,18,18, 0, 0, 0, 0,21, 0,20,19,16,
	17, 0, 0, 0, 0, 0, 0,21,20,17,17, 0,11,11,13,13,
	 0,13,13,16,16, 0,13,13,16,16, 0,17,17, 0,21, 0,
	18,19,21, 0, 0,12,12,16,16, 0,15,15,19,18, 0,13,
	13,16,16, 0,16,17,21,19, 0,17,17,21,21, 0,13,13,
	16,16, 0,16,16,20,18, 0,13,13,16,16, 0,17,17, 0,
	 0, 0,18,18, 0, 0, 0,18,17, 0,20, 0, 0, 0, 0, 0,
	 0,15,15,21,21, 0,19,18, 0, 0, 0,17,17,21,21, 0,
	17,17, 0, 0, 0, 0, 0, 0, 0, 0,15,15,20,21, 0,20,
	20, 0, 0, 0,19,19, 0, 0, 0,14,15,21,19, 0,16,16,
	 0,21, 0,17,16,21,21, 0,17,18,21,20, 0,18,18, 0,
	21, 0,16,16, 0,20, 0,19,19, 0, 0, 0,16,15, 0,20,
	 0,18,18, 0, 0, 0,17,17, 0,21, 0,16,16,20,20, 0,
	20,19, 0, 0, 0,15,16,21,22, 0,18,18, 0, 0, 0,18,
	17, 0, 0, 0,18,18, 0, 0, 0, 0, 0, 0, 0, 0,16,16,
	21,20, 0,19,20, 0, 0, 0,18,17,21, 0, 0,17,18, 0,
	 0, 0, 0, 0, 0, 0, 0,16,16, 0,20, 0, 0,20, 0, 0,
	 0,18,18,22, 0,
};

static const static_codebook _44p1_p4_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p1_p4_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p1_p4_0,
	0
};

static const long _vq_quantlist__44p1_p4_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p1_p4_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p1_p4_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p1_p4_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p1_p4_1,
	0
};

static const long _vq_quantlist__44p1_p5_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p5_0[] = {
	 1, 6, 6, 7, 8, 8, 7, 8, 8, 7, 9, 8,10,11,11, 9,
	 8, 8, 7, 8, 8,11,11,11, 9, 8, 8, 6, 7, 7,10,10,
	10,10,10,10,10,10,10,14,13,13,12,11,11,10,10,10,
	14,14,13,12,11,11, 6, 6, 6, 8, 5, 5, 8, 7, 7, 9,
	 7, 7,11,10,10, 9, 7, 7, 9, 7, 7,12,10,10,10, 7,
	 7, 7, 8, 8,12,11,10,12,10,10,11,10,10,15,13,13,
	13,10,10,11,10,10,17,14,13,13,10,10, 7, 7, 7,12,
	11,12,12,11,11,12,11,11,16,14,14,13,12,12,12,11,
	11,17,15,14,14,12,12,10, 9, 9,13,11,11,13,11,11,
	13,11,11,17,14,13,14,11,11,12,11,11,16,15,14,14,
	11,11, 7, 8, 8,12,11,11,12,10,10,12,10,10,15,13,
	13,14,11,10,12,10,10,16,14,14,14,10,10, 8, 7, 7,
	12,11,11,12,11,11,12,11,11,17,14,14,14,12,12,12,
	11,11,16,15,15,14,12,12,10,10,10,13,11,11,13,11,
	11,13,11,12,16,14,14,14,11,11,13,12,11,16,15,15,
	14,11,11,
};

static const static_codebook _44p1_p5_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p5_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p1_p5_0,
	0
};

static const long _vq_quantlist__44p1_p5_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p5_1[] = {
	 2, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 9, 8, 8, 8,
	 7, 7, 8, 8, 8, 9, 8, 8, 9, 7, 7, 6, 6, 6, 9, 8,
	 7, 9, 7, 7, 9, 8, 8,10, 8, 8,10, 8, 8,10, 8, 8,
	10, 8, 8,10, 8, 8, 7, 6, 6, 9, 6, 6, 9, 7, 7, 9,
	 7, 7,10, 8, 8, 9, 6, 6, 9, 7, 7,10, 8, 8, 9, 7,
	 7, 7, 8, 8,11, 9, 9,11, 9, 9,11, 8, 9,12, 9, 9,
	12, 8, 8,11, 9, 9,12, 9, 9,12, 8, 8, 8, 7, 7,10,
	 9, 9,10,10, 9,10, 9, 9,11,10,10,11, 9, 9,11, 9,
	 9,11,10,11,11, 9, 9,10, 8, 8,11, 9, 9,10, 9, 9,
	11, 9, 9,11,10,10,11, 9, 9,11, 9, 9,11,10,10,11,
	 9, 9, 9, 8, 8,11, 9, 9,12, 9, 9,11, 9, 9,12, 9,
	 9,12, 8, 8,12, 9, 9,12, 9, 9,12, 8, 8, 9, 7, 7,
	11, 9, 9,11,10,10,11, 9, 9,11,11,11,11, 9, 9,11,
	10,10,11,11,11,11, 9, 9,10, 9, 9,11, 9, 9,11,10,
	10,11, 9, 9,11,10,10,11, 9, 9,11, 9,10,11,10,10,
	11, 9, 9,
};

static const static_codebook _44p1_p5_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p5_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p1_p5_1,
	0
};

static const long _vq_quantlist__44p1_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p1_p6_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p1_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p1_p6_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p1_p6_0,
	0
};

static const long _vq_quantlist__44p1_p6_1[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p1_p6_1[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,13,14,16,16,16,16,
};

static const static_codebook _44p1_p6_1 = {
	1, 25,
	(char *)_vq_lengthlist__44p1_p6_1,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p1_p6_1,
	0
};

static const long _vq_quantlist__44p1_p6_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p1_p6_2[] = {
	 3, 4, 4, 5, 4, 5, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p1_p6_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p1_p6_2,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p1_p6_2,
	0
};

static const char _huff_lengthlist__44p1_short[] = {
	 4, 5, 7, 8,10,13,14, 4, 2, 4, 6, 8,11,12, 7, 4,
	 3, 5, 8,12,14, 8, 5, 4, 4, 8,12,12, 9, 7, 7, 7,
	 9,10,11,13,11,11, 9, 7, 8,10,13,11,10, 6, 5, 7,
	 9,
};

static const static_codebook _huff_book__44p1_short = {
	2, 49,
	(char *)_huff_lengthlist__44p1_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p2_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p2_l0_0[] = {
	 1, 4, 4, 7, 7, 8, 8, 9, 9,10,10,11,11, 4, 6, 5,
	 8, 7, 9, 8,10, 9,11,10,11,11, 4, 5, 6, 7, 8, 8,
	 9, 9,10,10,10,10,11, 8, 9, 8,10, 8,10, 9,11,10,
	11,11,11,11, 8, 8, 9, 8,10, 9,10,10,11,11,11,11,
	11, 9,10,10,11,11,11,11,11,11,12,11,12,11, 9,10,
	10,10,11,11,11,11,11,11,12,11,12,10,11,11,12,11,
	12,12,12,12,12,12,12,12,10,11,11,11,11,12,12,12,
	13,12,12,12,12,11,12,12,12,12,13,13,12,12,12,12,
	12,12,11,12,12,12,12,13,13,12,13,12,12,12,12,12,
	13,13,13,13,13,13,12,13,12,13,12,12,12,13,13,13,
	13,13,13,13,12,13,12,12,12,
};

static const static_codebook _44p2_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p2_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p2_l0_0,
	0
};

static const long _vq_quantlist__44p2_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p2_l0_1[] = {
	 2, 4, 4, 5, 5, 4, 5, 5, 6, 5, 4, 5, 5, 5, 6, 5,
	 5, 6, 6, 6, 5, 6, 5, 6, 6,
};

static const static_codebook _44p2_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p2_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p2_l0_1,
	0
};

static const long _vq_quantlist__44p2_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p2_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p2_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p2_l1_0,
	0
};

static const char _huff_lengthlist__44p2_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p2_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p2_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p2_long[] = {
	 3, 4, 9, 8, 8,10,13,16, 4, 2, 9, 5, 7,10,14,18,
	 9, 7, 6, 5, 7, 9,12,16, 7, 5, 5, 3, 5, 8,11,13,
	 8, 7, 7, 5, 5, 7, 9,11,10,10, 9, 8, 6, 6, 8,10,
	13,14,13,11, 9, 8, 9,10,17,18,16,14,11,10,10,10,
};

static const static_codebook _huff_book__44p2_long = {
	2, 64,
	(char *)_huff_lengthlist__44p2_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p2_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p2_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p2_p1_0,
	0
};

static const long _vq_quantlist__44p2_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p2_p2_0[] = {
	 1, 4, 4, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0,
	10,10, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0, 0, 0, 0,
	 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0,
	 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0,
	 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0,11,11, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0, 0, 0,
	 6, 6, 0, 0, 0, 7, 7, 0, 0, 0, 8, 8, 0, 0, 0, 0,
	 0, 0, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0,
	 0, 0, 0, 0, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0,
	 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,
	 8, 8, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 8, 8, 0,
	 0, 0,10,10, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0,
	 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	11,11, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,11,10, 0, 0, 0, 0, 0,
	 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 8, 8, 0, 0, 0,10,10, 0, 0, 0,11,11, 0, 0,
	 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0,
	 0, 0, 0, 0, 0,10,10, 0, 0, 0,13,13, 0, 0, 0, 0,
	 0, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,12,
	 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,13,13, 0,
	 0, 0, 0, 0, 0, 0, 0,12,12, 0, 0, 0,13,13, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6,
	 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11,
	 0, 0, 0,12,12, 0, 0, 0,12,12, 0, 0, 0, 0, 0, 0,
	 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,11, 0, 0,
	 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,13,13, 0, 0, 0,
	 0, 0, 0, 0, 0,12,12, 0, 0, 0,13,13, 0, 0, 0, 0,
	 0, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,12,
	 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 8, 8, 0, 0, 0,10,10, 0, 0, 0,11,11, 0,
	 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,13,13, 0, 0,
	 0, 0, 0, 0, 0, 0,12,12, 0, 0, 0,13,13, 0, 0, 0,
	 0, 0, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,10,
	10, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,14,13,
	 0, 0, 0, 0, 0, 0, 0, 0,13,12, 0, 0, 0,13,13, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 6, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,11,
	11, 0, 0, 0,12,12, 0, 0, 0,12,12, 0, 0, 0, 0, 0,
	 0, 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,12,12, 0,
	 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,13,13, 0, 0,
	 0, 0, 0, 0, 0, 0,12,12, 0, 0, 0,12,12, 0, 0, 0,
	 0, 0, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,
	12, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0, 0,12,12,
	 0, 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,12,12, 0,
	 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0,14,14, 0, 0,
	 0, 0, 0, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,
	12,12, 0, 0, 0,12,13, 0, 0, 0, 0, 0, 0, 0, 0,12,
	12, 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0,14,13,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	11,11, 0, 0, 0,12,12, 0, 0, 0,13,13, 0, 0, 0, 0,
	 0, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,12,
	 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0,12,12, 0,
	 0, 0, 0, 0, 0, 0, 0,12,12, 0, 0, 0,14,14, 0, 0,
	 0, 0, 0, 0, 0, 0,14,14, 0, 0, 0, 0, 0, 0, 0, 0,
	12,12, 0, 0, 0,13,13, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0,
};

static const static_codebook _44p2_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p2_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p2_p2_0,
	0
};

static const long _vq_quantlist__44p2_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p3_0[] = {
	 1, 5, 5, 6, 7, 7, 0, 8, 8, 6, 9, 9, 8,11,11, 0,
	 8, 8, 0, 9, 9, 0,12,12, 0, 8, 8, 5, 7, 7, 7,10,
	10, 0,12,12, 8,11,11, 9,12,12, 0,11,12, 0,12,12,
	 0,15,15, 0,12,12, 0, 6, 6, 0, 6, 6, 0, 7, 7, 0,
	 7, 7, 0,10,10, 0, 7, 7, 0, 8, 8, 0,11,11, 0, 7,
	 7, 6, 7, 7,10, 9, 9, 0,11,10,10, 9, 9,12,12,12,
	 0,10,10, 0,11,11, 0,13,13, 0,11,11, 7, 6, 6,10,
	10,10, 0,11,11,11,11,11,12,12,12, 0,11,11, 0,12,
	12, 0,15,15, 0,11,11, 0,11,11, 0,11,11, 0,12,12,
	 0,12,12, 0,14,14, 0,12,12, 0,12,12, 0,15,15, 0,
	11,11, 0, 8, 8, 0,10,10, 0,11,11, 0,11,11, 0,12,
	12, 0,12,12, 0,11,11, 0,15,15, 0,11,11, 0, 6, 6,
	 0,10,10, 0,12,12, 0,10,10, 0,13,13, 0,12,12, 0,
	13,13, 0,14,14, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p2_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p2_p3_0,
	0
};

static const long _vq_quantlist__44p2_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p3_1[] = {
	 2, 3, 3, 0, 8, 8, 0, 8, 8, 0, 9, 9, 0, 9, 9, 0,
	 9, 9, 0, 9, 9, 0, 9, 9, 0, 8, 8, 0, 6, 6, 0, 7,
	 7, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8,
	 0, 8, 8, 0, 8, 8, 0, 6, 6, 0, 6, 6, 0, 6, 6, 0,
	 8, 8, 0, 9, 9, 0, 7, 7, 0, 8, 8, 0, 9, 9, 0, 6,
	 6, 0, 8, 8, 0, 9, 9, 0, 9, 9, 0,10,10, 0,10,10,
	 0,10,10, 0,10,10, 0,11,11, 0, 9, 9, 0, 7, 7, 0,
	10,10, 0,10,10, 0,12,11, 0,12,12, 0,11,11, 0,11,
	11, 0,12,12, 0,10,10, 0, 7, 7, 0,10,10, 0,10,10,
	 0,12,12, 0,11,12, 0,11,11, 0,11,11, 0,11,11, 0,
	10,10, 0, 8, 8, 0, 9, 9, 0, 9, 9, 0,10,10, 0,10,
	10, 0,10, 9, 0,10,10, 0,10,10, 0, 9, 9, 0, 6, 6,
	 0,10,10, 0,10,10, 0,11,11, 0,12,12, 0,11,11, 0,
	11,11, 0,12,12, 0,11,11, 0, 7, 7, 0, 9, 9, 0, 9,
	 9, 0,11,11, 0,11,11, 0,10,10, 0,10,10, 0,11,11,
	 0, 9, 9,
};

static const static_codebook _44p2_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p2_p3_1,
	0
};

static const long _vq_quantlist__44p2_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p4_0[] = {
	 1, 6, 6, 6, 7, 7, 7, 8, 8, 7, 8, 8,10,11,11, 9,
	 8, 8, 7, 8, 8,11,11,11, 9, 8, 8, 6, 7, 7, 9,11,
	11, 9,11,11,10,11,11,12,13,13,11,12,12,10,11,11,
	13,14,14,12,12,12, 6, 6, 6, 8, 6, 6, 8, 7, 7, 9,
	 7, 7,11,10,10,10, 6, 6, 9, 7, 7,12,10,10,11, 6,
	 7, 7, 7, 7,11,10,10,12,10,10,11,10,10,14,13,13,
	13,10,10,12,11,11,15,13,13,14,10,10, 8, 7, 7,12,
	11,11,12,11,11,11,11,11,14,14,14,13,12,12,12,11,
	11,15,15,15,13,12,12, 0,10,10, 0,11,11, 0,11,11,
	 0,11,11, 0,14,14, 0,11,11, 0,11,11, 0,15,15, 0,
	11,11, 7, 8, 8,12,10,10,12,10,10,12,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,14,10,10, 8, 7, 7,
	12,11,11,12,11,11,12,11,11,16,14,14,14,12,12,13,
	12,12,15,14,14,15,12,12, 0,11,11, 0,12,12, 0,12,
	12, 0,12,12, 0,15,15, 0,12,12, 0,12,12, 0,14,14,
	 0,12,12,
};

static const static_codebook _44p2_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p2_p4_0,
	0
};

static const long _vq_quantlist__44p2_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p2_p4_1[] = {
	 3, 4, 4, 8, 8,11, 9, 9,12,12,11,10,10,12,12,12,
	10,10,11,11,12,12,12,12,12,12,11,11,13,13,12,12,
	12,13,13,12,10,10,12,12,12,11,11,13,13,12,13,13,
	13,13,12,11,11,13,13,12,12,12,13,13,12,10,10,12,
	12,12,11,11,13,13,12,13,13,12,12,12,11,11,13,13,
	12,13,13,13,13,12,11,11,12,12,12,11,11,12,12,12,
	13,13,12,12,12,13,13,13,13,12,13,13,13,13,13,13,
	13,12,12,12,13,13,13,13,12,13,13,12,12,11, 8, 8,
	10,10,12,11,11,11,11,12,10,10,10,10,13,11,11,10,
	10,13,11,11,10,10,13,12,12,12,12,13,11,11,11,11,
	13,12,12,11,11,13,12,12,11,11,13,12,12,12,11,13,
	12,12,12,12,13,11,11,11,11,13,12,12,11,11,13,11,
	12,11,11,13,12,12,11,11,14,12,12,11,11,13,11,11,
	11,11,14,12,12,11,11,13,11,12,10,10,14,12,12,11,
	11,14,12,12,11,11,14,11,11,11,11,14,12,12,11,11,
	13,12,12,11,11,14,12,12,11,11,11, 8, 8,10,10,12,
	 7, 7,10,10,12, 9, 9,11,11,13, 9, 9, 9, 9,13,13,
	13,10,10,13, 9, 9,12,12,13,13,13,12,12,13, 9, 8,
	11,11,13,10,10,12,12,14,13,13,11,11,13, 9, 9,11,
	11,13,13,13,12,12,13, 9, 9,10,10,13,10,10,11,11,
	13,13,13,10,10,14,10,10,11,11,14,14,14,12,12,13,
	 9, 9,10,10,13,10,10,11,11,14,13,14,10,10,14,14,
	14,11,12,14,14,14,14,14,14,13,13,10,10,13,14,14,
	11,11,14,14,14,10,10,14, 9, 9, 9, 9,14, 9, 9, 9,
	 9,14,10,10, 9, 9,14,10,10, 8, 8,14,11,11, 8, 8,
	15,11,11,10,10,15,12,12,10,10,15,10,10,10,10,15,
	11,11,10,10,15,13,13,10,10,15,11,11,10,10,15,12,
	12,10,10,15,10,10,10,10,15,11,11,10,10,15,13,13,
	10,10,15,11,11,10,10,15,12,12,10,10,15,11,11, 9,
	 9,15,11,11, 9, 9,15,13,13, 9, 9,15,13,13,10,10,
	15,12,12,10,10,15,13,13,10,10,15,13,12, 9, 9,15,
	13,13, 9, 9,14,12,12, 9, 9,14,13,13, 9, 9,14,13,
	13, 9, 9,14,13,13, 7, 7,14,13,13, 8, 8,15,14,14,
	10,10,15,14,14,10,10,15,14,14,10,10,15,14,14,10,
	10,15,14,14, 9, 9,15,14,14,10,10,15,14,14,10,10,
	14,14,14, 9, 9,15,14,14,10,10,14,14,14, 9, 9,15,
	14,14,10,10,15,14,14,10,10,14,14,14, 9, 9,14,14,
	14, 9, 9,14,14,14, 8, 8,15,14,14,10,10,15,14,14,
	11,11,15,14,14, 9, 9,15,14,14, 9, 9,14,14,14, 8,
	 8,13, 9, 9,12,12,17,11,11,12,12,17,12,12,12,12,
	17,12,12,11,11,18,15,15,12,12,17,12,12,12,12,17,
	14,15,13,13,17,12,12,12,12,17,13,13,12,13,17,15,
	15,12,12,18,13,13,13,13,18,15,15,13,13,18,12,12,
	12,12,18,13,13,13,13,18,15,15,12,12,18,13,13,12,
	12,18,15,15,13,13,18,13,13,12,12,17,13,13,12,12,
	17,15,15,12,12,18,15,15,13,13,18,15,15,13,14,18,
	15,16,12,12,18,15,15,12,12,18,16,16,12,12,13, 8,
	 8,10,10,14,15,14,11,11,14,15,15,12,12,15,14,14,
	12,11,15,15,15,12,12,15,15,15,12,12,15,15,15,13,
	13,15,15,15,12,12,15,15,15,13,13,15,15,15,13,13,
	15,15,15,13,13,15,15,16,13,13,15,15,15,12,12,15,
	15,15,13,13,15,15,15,13,13,15,15,15,13,13,15,15,
	15,13,13,15,15,14,12,12,15,15,15,12,12,16,15,14,
	12,12,16,15,15,13,13,16,16,16,13,13,16,15,15,12,
	12,15,15,15,13,13,15,15,15,12,12,13,12,12,10,10,
	14,14,14,11,11,15,14,14,12,12,15,14,14,11,11,15,
	14,14,11,11,15,15,15,13,13,15,14,14,13,13,15,15,
	15,12,12,15,14,15,13,13,16,15,15,12,12,15,15,15,
	13,13,16,14,14,13,13,15,15,15,12,12,15,15,15,13,
	13,16,15,15,12,12,16,15,15,12,12,16,14,14,13,13,
	15,15,15,11,11,15,15,15,12,12,16,15,15,11,11,16,
	15,15,13,13,16,14,15,14,14,16,15,15,12,12,16,15,
	14,12,12,16,15,15,12,12,14,10,10, 9, 9,14,11,11,
	12,12,14,12,12,13,13,14,12,12,12,12,15,14,14,13,
	13,15,13,13,14,14,15,14,14,15,15,15,12,12,13,13,
	15,13,13,14,14,15,14,14,13,13,15,13,13,13,14,15,
	14,14,15,15,15,12,12,13,13,15,13,13,14,14,15,14,
	14,13,13,15,13,13,14,14,15,14,14,15,15,15,13,13,
	12,12,15,13,13,13,13,15,14,14,13,12,15,15,15,14,
	15,15,15,14,20,20,15,14,14,13,13,15,14,14,13,13,
	15,14,14,13,13,14,12,12, 9, 9,14,14,14,12,12,14,
	13,13,12,13,14,14,14,12,12,15,14,14,12,12,15,14,
	14,14,13,15,14,14,14,14,15,14,14,13,13,15,14,14,
	13,13,15,15,15,14,14,15,14,14,13,13,15,14,14,14,
	14,15,14,14,13,13,15,14,14,13,13,15,15,15,15,14,
	15,15,15,13,13,15,14,14,14,14,15,14,14,13,13,15,
	14,14,13,13,14,15,15,14,14,15,15,15,14,14,15,14,
	14,14,14,15,15,15,14,14,15,14,14,13,14,15,15,15,
	14,14,13,10,10,12,12,17,11,11,12,12,17,12,12,12,
	12,17,12,12,11,11,17,15,15,12,11,18,13,13,13,13,
	18,15,15,13,13,17,12,12,12,12,18,13,13,13,13,17,
	15,15,12,12,17,12,12,12,12,17,15,15,13,13,17,12,
	12,12,12,17,13,13,12,12,17,15,15,12,12,18,14,13,
	12,12,18,15,15,13,13,18,13,13,12,12,18,13,13,12,
	12,18,16,16,12,12,18,16,16,12,12,18,15,15,13,13,
	18,16,16,12,12,17,15,15,12,12,17,16,16,12,12,13,
	 8, 8,10,10,14,14,15,12,12,14,15,15,12,12,15,14,
	14,12,12,15,15,14,12,12,15,15,15,13,13,15,15,15,
	13,13,15,15,15,12,12,16,15,15,13,13,16,15,15,13,
	13,15,15,15,12,12,15,15,15,14,14,15,15,15,12,12,
	15,15,15,13,13,16,15,15,13,13,15,15,15,13,13,16,
	15,15,13,13,15,15,14,12,12,15,15,15,12,12,16,14,
	15,13,13,16,15,15,13,13,15,16,15,13,13,16,15,14,
	13,13,16,15,15,13,13,16,15,15,13,13,13,12,12,11,
	11,14,14,14,11,11,14,14,14,12,12,15,14,14,11,11,
	16,14,14,11,11,15,15,15,12,13,16,14,14,13,13,15,
	15,15,12,12,15,14,14,13,13,16,15,15,12,12,15,15,
	15,12,12,15,14,14,13,13,15,15,15,12,12,15,14,14,
	12,12,16,15,15,12,12,16,15,15,12,12,16,14,14,13,
	13,15,15,15,11,11,15,15,14,12,12,16,15,15,11,11,
	16,15,15,12,12,16,14,14,13,13,16,15,15,11,11,16,
	14,14,12,12,16,15,15,11,11,14,10,10, 9, 9,14,11,
	11,12,12,14,12,12,13,14,14,12,12,12,12,14,14,14,
	13,13,15,13,13,14,14,15,14,14,15,15,15,12,12,13,
	13,15,13,13,14,14,15,15,15,14,14,15,13,13,14,14,
	15,15,15,15,15,15,12,12,13,13,15,13,13,14,14,15,
	14,14,13,13,15,13,13,14,14,15,14,14,15,15,15,12,
	12,13,13,15,13,13,13,13,14,14,14,13,13,15,15,15,
	14,15,15,15,15,21,19,15,14,14,13,13,15,14,14,14,
	14,14,14,14,13,13,14,12,12, 9, 9,14,14,14,12,12,
	14,14,13,13,13,14,14,14,12,12,14,14,14,12,12,15,
	14,14,13,13,15,14,14,14,14,15,14,14,13,13,15,14,
	14,13,13,15,15,15,15,15,15,14,14,13,13,15,14,14,
	14,14,15,14,14,13,13,15,14,14,13,13,14,15,15,15,
	15,15,14,15,13,13,15,14,14,14,14,15,14,14,13,13,
	15,14,14,13,13,14,15,15,14,14,15,15,15,14,14,15,
	14,14,14,14,15,15,15,15,15,15,14,14,14,13,14,15,
	15,14,14,13,10,10,12,12,18,12,12,12,12,17,12,12,
	12,12,18,13,13,11,11,18,15,14,11,11,17,13,13,13,
	13,18,15,15,12,12,18,12,12,12,12,17,13,13,12,12,
	18,15,15,12,12,18,13,13,13,12,18,15,15,13,13,18,
	13,13,12,12,18,13,13,12,12,18,15,15,12,12,17,13,
	13,12,12,17,15,15,12,12,17,12,12,11,11,17,13,13,
	11,11,17,15,15,11,11,18,16,16,12,12,18,15,15,13,
	13,18,15,15,11,11,17,15,15,12,12,18,15,15,11,11,
	13, 8, 8,10,10,14,14,14,11,11,15,15,15,12,12,15,
	14,14,11,11,16,14,14,12,12,15,15,15,12,12,15,15,
	15,13,13,15,15,15,12,12,15,15,15,12,12,16,15,15,
	13,13,15,15,15,12,12,15,15,15,13,13,16,15,15,12,
	12,15,15,15,12,12,16,15,15,13,13,16,15,15,12,12,
	15,15,15,13,13,15,14,14,12,12,15,15,15,12,12,16,
	15,14,12,12,16,15,15,13,13,16,16,16,13,13,16,14,
	15,13,13,15,15,15,13,13,16,15,15,12,12,13,12,12,
	10,10,14,14,14,11,11,15,14,14,12,12,15,14,14,11,
	11,16,14,14,11,11,15,14,15,12,12,15,14,14,13,13,
	15,15,15,12,12,15,14,14,12,12,15,14,15,12,12,15,
	15,15,12,12,16,14,14,13,13,15,15,15,11,12,16,14,
	14,12,12,16,15,15,12,12,15,15,15,12,12,16,14,14,
	12,12,15,15,15,11,11,15,14,14,11,12,15,15,14,11,
	11,16,15,15,12,12,16,14,14,13,13,16,15,15,11,11,
	16,14,14,12,12,16,15,15,11,11,13,10,10, 8, 8,14,
	12,12,12,12,14,12,12,13,13,14,12,12,12,12,14,14,
	14,13,13,15,13,13,14,14,15,15,14,15,15,15,13,13,
	13,13,15,13,13,14,14,15,14,15,14,14,15,13,13,13,
	13,15,15,15,15,15,15,12,12,13,12,15,13,13,14,14,
	15,14,14,13,13,15,13,13,14,13,15,15,15,16,16,15,
	13,13,12,12,15,13,13,13,13,14,14,14,12,12,15,15,
	15,14,14,15,15,15,20,20,15,14,14,13,13,15,15,14,
	14,14,15,14,14,13,13,13,12,12, 9, 9,14,13,13,12,
	12,14,13,13,12,12,14,14,14,12,12,14,14,14,13,13,
	15,14,14,13,13,15,14,14,14,14,15,15,14,12,12,15,
	14,14,13,13,15,14,15,14,15,15,14,14,13,13,15,14,
	14,14,14,15,14,14,12,12,15,14,14,13,13,14,15,14,
	15,14,15,14,14,13,13,15,14,14,14,14,15,14,14,12,
	12,15,14,14,13,13,15,15,15,14,14,15,15,15,14,14,
	16,14,14,14,14,15,15,15,14,14,15,14,14,14,14,14,
	15,15,14,14,13,13,13,12,13,17,15,15,12,12,17,15,
	15,12,12,18,15,15,11,11,17,16,16,11,11,18,16,16,
	13,13,18,17,16,13,13,18,16,16,12,12,18,16,16,12,
	12,18,17,17,12,12,17,16,16,12,13,17,16,16,12,13,
	17,16,16,12,12,17,16,16,12,12,18,17,16,12,12,18,
	16,16,12,12,17,16,17,12,12,18,15,15,11,11,18,15,
	15,12,12,17,17,17,11,11,17,17,17,12,12,17,16,16,
	13,13,18,16,16,11,11,18,16,16,12,12,18,17,16,11,
	11,14,14,14,10,10,16,15,14,11,11,16,15,15,12,12,
	16,14,14,12,12,17,14,14,13,13,17,15,15,13,13,17,
	15,15,14,14,16,15,15,12,12,16,15,15,13,13,18,15,
	15,14,14,16,15,15,12,12,16,15,15,14,14,16,15,15,
	12,12,16,15,15,13,13,17,15,15,13,13,17,15,15,13,
	13,17,15,15,14,14,16,14,14,12,12,17,15,15,12,12,
	18,15,15,13,13,17,15,15,14,14,17,16,16,15,15,17,
	15,14,13,13,17,15,15,14,14,17,15,15,13,13,14,12,
	12,11,11,15,14,14,12,12,16,14,14,12,12,16,14,14,
	11,11,17,14,14,12,12,16,15,14,13,13,16,14,14,13,
	13,16,15,15,12,12,16,14,14,13,13,17,15,15,13,13,
	16,15,15,13,13,17,14,14,13,13,16,15,15,12,12,16,
	14,14,12,12,16,15,15,12,12,17,15,15,12,12,17,14,
	14,13,13,16,15,15,12,12,16,14,14,12,12,16,15,15,
	12,12,17,15,15,13,13,17,14,14,13,13,17,15,15,12,
	12,17,14,14,12,12,17,15,15,12,12,14,14,14, 8, 8,
	14,14,14,13,13,14,15,15,14,14,14,14,14,14,14,15,
	15,15,19,19,15,15,15,14,14,15,15,16,20,19,15,15,
	15,14,14,15,16,16,15,15,15,15,15,19,19,15,15,15,
	14,14,15,16,16,19,20,15,15,15,14,14,15,15,15,15,
	15,15,15,15,19,19,15,15,15,15,15,15,15,16,19,20,
	15,14,15,14,14,15,15,15,15,15,15,15,15,20,19,15,
	15,15,21,19,15,16,16,20,20,15,15,14,19,19,15,15,
	16,20,21,15,15,15,20,19,13,12,12, 9, 9,14,14,14,
	12,12,14,13,13,13,13,14,14,14,13,13,15,14,14,20,
	19,15,14,14,14,13,15,14,14,19,19,15,15,14,13,13,
	15,14,14,14,14,15,15,15,19,20,15,14,14,13,13,15,
	14,14,20,19,14,15,14,13,13,15,14,14,14,13,15,15,
	15,19,20,15,15,14,14,14,15,14,14,21,19,15,15,15,
	13,13,15,14,14,14,14,14,15,15,20,20,15,15,15,21,
	20,15,14,14,19,20,15,15,15,20,20,15,14,14,19,20,
	15,15,15,21,19,
};

static const static_codebook _44p2_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p2_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p2_p4_1,
	0
};

static const long _vq_quantlist__44p2_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p2_p5_0[] = {
	 2, 6, 6,14,14, 6, 7, 7,14,14, 7, 7, 7,15,15, 0,
	13,13,16,16, 0,13,13,15,15, 7, 8, 8,15,15, 9,10,
	10,17,16, 9, 8, 8,15,15, 0,13,13,18,17, 0,13,13,
	16,16, 8, 8, 8,15,15,12,11,11,16,17, 9, 8, 8,14,
	14, 0,13,13,18,17, 0,13,13,16,15, 0,14,14,18,17,
	 0,20,22,18,20, 0,12,12,16,16, 0,16,16,22,20, 0,
	14,14,16,16, 0,14,14,17,17, 0,22,22,22,19, 0,12,
	13,16,16, 0,17,17, 0, 0, 0,15,15,16,16, 5, 7, 7,
	13,13, 9, 9, 9,15,14,10,10,10,14,14, 0,21,21,18,
	17, 0,21,22,18,17, 9,10,10,14,14,12,12,12,17,17,
	12,10,10,14,14, 0,19,21,18,17, 0,20,22,18,18,11,
	10,10,14,14,14,13,13,18,17,12,11,11,14,14, 0,22,
	19,17,18, 0,20, 0,18,17, 0,22,21,17,17, 0, 0, 0,
	 0, 0, 0,20,22,17,17, 0,22, 0,21,19, 0,22, 0,18,
	18, 0, 0,22,18,19, 0, 0, 0, 0, 0, 0,19,21,17,17,
	 0, 0, 0,20,20, 0, 0, 0,18,18, 6, 6, 6,13,12, 8,
	 6, 6,11,11, 8, 6, 6,13,13, 0, 9, 9,11,11, 0,11,
	11,14,14, 9, 7, 7,13,13,11, 9, 9,13,13,10, 6, 6,
	13,13, 0,10,10,14,14, 0,10,10,13,13, 9, 7, 7,13,
	14,13, 9, 9,13,13,10, 6, 6,13,12, 0,11,11,15,15,
	 0,10,10,13,13, 0,12,12,15,15, 0,19, 0,17,17, 0,
	 9, 9,13,13, 0,13,14,19,20, 0,11,11,13,13, 0,11,
	11,14,14, 0,19,20,17,18, 0,10,10,13,13, 0,15,15,
	21,19, 0,12,12,13,13, 0,10,10,12,13, 0,11,11,15,
	15, 0,11,11,15,15, 0,15,15,22, 0, 0,16,17,22, 0,
	 0,11,11,15,15, 0,14,14,18,17, 0,11,11,15,16, 0,
	15,15,22,21, 0,16,16, 0,20, 0,12,12,16,15, 0,15,
	14,19,19, 0,11,11,16,16, 0,15,15,21, 0, 0,16,15,
	 0, 0, 0,16,16,22,21, 0, 0, 0, 0, 0, 0,15,15,20,
	20, 0,18,18, 0, 0, 0,16,17, 0, 0, 0,17,17, 0,22,
	 0, 0, 0, 0, 0, 0,15,15,21,22, 0,20,18, 0, 0, 0,
	18,17,22, 0, 0,10,10,12,11, 0,10,10,10,10, 0,11,
	11,12,12, 0,11,11, 9, 9, 0,13,13,12,12, 0,11,11,
	12,12, 0,13,13,12,12, 0,10,10,12,12, 0,13,12,13,
	13, 0,12,12,12,12, 0,11,11,12,12, 0,13,13,12,12,
	 0,10,10,12,12, 0,13,13,13,14, 0,12,12,12,12, 0,
	13,14,14,14, 0,20,21,15,15, 0,12,11,12,12, 0,15,
	16,20,22, 0,13,12,11,11, 0,13,13,14,13, 0,20, 0,
	16,15, 0,12,12,12,12, 0,16,16,22,21, 0,13,13,12,
	12, 6, 7, 7,16,16,11, 9, 9,15,15,12, 9, 9,16,16,
	 0,13,13,14,14, 0,14,14,16,17,10, 9, 9,16,16,14,
	12,12,16,16,12, 9, 9,15,15, 0,13,13,18,18, 0,13,
	13,15,16,12,10,10,17,18,15,12,12,17,17,13, 9, 9,
	16,16, 0,13,13,17,18, 0,14,14,16,16, 0,15,15,18,
	18, 0,22, 0,20,20, 0,12,12,16,16, 0,16,16,20,22,
	 0,14,14,16,16, 0,15,14,18,18, 0, 0,22,19,21, 0,
	13,13,16,17, 0,17,17,22,22, 0,15,15,16,16, 7, 7,
	 7,14,14,11,10,10,15,15,12,10,10,15,14, 0,22, 0,
	18,18, 0, 0,21,17,18,11,10,10,15,15,14,12,12,17,
	17,14,11,11,15,15, 0,22,20,18,18, 0, 0,20,18,17,
	12,10,10,16,16,17,14,14,19,18,14,11,11,15,15, 0,
	21,22,19,19, 0,21,22,18,18, 0,22, 0,19,21, 0, 0,
	 0, 0, 0, 0,22,22,18,17, 0, 0, 0,21,20, 0,22,22,
	20,19, 0, 0,22,20,20, 0, 0, 0, 0, 0, 0,20,21,17,
	17, 0, 0,22,21,21, 0, 0, 0,18,18,10, 9, 9,14,14,
	13,10,10,13,13,13,10,11,14,14, 0,13,13,12,12, 0,
	15,15,16,16,13,10,10,15,15,15,12,12,14,14,15,10,
	10,14,15, 0,14,14,16,15, 0,14,14,15,15,13,10,10,
	15,15,18,13,13,15,15,15,10,10,14,15, 0,14,14,16,
	16, 0,14,14,15,15, 0,15,15,16,16, 0,22, 0,18,18,
	 0,12,13,14,14, 0,17,17,22, 0, 0,14,14,14,14, 0,
	15,15,16,16, 0,22, 0,18,17, 0,13,13,14,14, 0,19,
	18,21,22, 0,15,15,14,14, 0,11,11,13,13, 0,12,12,
	16,16, 0,12,12,16,16, 0,15,16,21, 0, 0,16,17, 0,
	22, 0,12,12,16,16, 0,14,14,17,18, 0,11,11,16,16,
	 0,15,15,21,22, 0,16,16, 0, 0, 0,12,12,16,16, 0,
	15,15, 0,19, 0,12,12,16,17, 0,16,16,22, 0, 0,16,
	16, 0,22, 0,17,17, 0,22, 0, 0, 0, 0, 0, 0,15,15,
	20,19, 0,18,18, 0, 0, 0,17,18, 0, 0, 0,17,17, 0,
	 0, 0, 0, 0, 0, 0, 0,15,15, 0,22, 0,20,18, 0, 0,
	 0,18,18,22,22, 0,11,11,14,14, 0,12,12,14,14, 0,
	12,12,15,15, 0,13,13,14,14, 0,14,14,17,16, 0,12,
	12,16,16, 0,14,14,16,16, 0,11,11,15,15, 0,13,13,
	16,16, 0,13,13,15,15, 0,12,12,15,15, 0,15,14,16,
	16, 0,11,11,15,15, 0,14,14,17,17, 0,13,13,15,15,
	 0,15,15,17,17, 0, 0, 0,19,18, 0,13,12,15,15, 0,
	16,16, 0, 0, 0,14,14,15,15, 0,14,14,16,17, 0,22,
	 0,18,18, 0,13,13,15,15, 0,17,17, 0, 0, 0,14,14,
	15,15, 8, 8, 8,16,16,12,10,10,16,16,13, 9, 9,16,
	16, 0,14,14,17,17, 0,14,14,17,16,12,10,10,18,17,
	14,11,11,18,18,14, 9,10,16,16, 0,13,13,18,19, 0,
	14,13,16,16,12, 9, 9,16,16,17,13,13,17,17,14, 9,
	 9,15,15, 0,14,14,19,20, 0,13,13,15,15, 0,15,15,
	18,19, 0, 0,22,22,22, 0,13,13,17,17, 0,16,16,19,
	21, 0,14,14,16,16, 0,14,14,18,18, 0, 0, 0, 0, 0,
	 0,13,13,16,16, 0,18,18, 0, 0, 0,15,15,16,16, 8,
	 7, 7,14,14,12,10,10,15,15,13,10,10,15,14, 0,22,
	 0,18,18, 0,22, 0,18,18,12,10,10,16,15,15,12,12,
	17,17,14,11,11,15,15, 0,20,21,19,18, 0, 0, 0,17,
	18,13,11,11,15,15,16,13,13,18,18,15,11,11,14,14,
	 0,22,21,19,19, 0,21,22,18,18, 0,22,22,20,18, 0,
	 0, 0, 0, 0, 0,22,19,17,17, 0, 0, 0,22,21, 0, 0,
	22,19,17, 0, 0,22,19,19, 0, 0, 0, 0, 0, 0,22,21,
	18,17, 0, 0, 0,22, 0, 0, 0, 0,19,19, 0,10,10,14,
	14, 0,11,11,15,14, 0,11,11,15,15, 0,14,14,15,14,
	 0,15,15,16,16, 0,11,11,16,16, 0,13,13,16,16, 0,
	11,11,15,15, 0,14,14,17,16, 0,14,14,15,15, 0,11,
	11,16,16, 0,14,13,15,15, 0,11,11,15,15, 0,15,15,
	17,17, 0,14,14,15,14, 0,16,16,17,17, 0, 0,22,18,
	18, 0,13,13,15,15, 0,17,17,22, 0, 0,15,15,15,14,
	 0,15,16,16,17, 0, 0,22,18,19, 0,13,13,15,15, 0,
	20,18,21, 0, 0,15,15,14,14, 0,11,11,13,13, 0,12,
	12,16,16, 0,12,12,16,15, 0,15,16,22,22, 0,17,17,
	 0, 0, 0,12,12,16,16, 0,14,14,18,18, 0,11,11,16,
	16, 0,15,16,22,20, 0,16,16, 0,22, 0,12,12,16,16,
	 0,15,15,18,20, 0,11,11,16,16, 0,15,15, 0, 0, 0,
	16,16, 0, 0, 0,17,17,22, 0, 0, 0, 0, 0, 0, 0,15,
	15, 0,21, 0,18,18, 0, 0, 0,17,16, 0, 0, 0,17,17,
	22,22, 0, 0, 0, 0, 0, 0,15,15,21, 0, 0,20,22, 0,
	 0, 0,18,18, 0, 0, 0,12,12,15,15, 0,12,12,15,15,
	 0,12,12,16,16, 0,13,13,15,15, 0,15,15,17,17, 0,
	13,12,16,16, 0,14,14,16,16, 0,12,11,16,16, 0,14,
	14,17,17, 0,14,14,16,16, 0,12,12,16,16, 0,15,15,
	17,16, 0,11,11,15,16, 0,14,14,17,17, 0,14,14,16,
	16, 0,15,15,18,18, 0, 0, 0,22,19, 0,13,13,15,16,
	 0,16,17, 0, 0, 0,14,14,16,16, 0,15,15,18,17, 0,
	 0, 0,20,20, 0,13,13,16,15, 0,17,17,22,22, 0,14,
	14,15,15, 0,11,11,16,16, 0,13,13,16,17, 0,13,13,
	17,18, 0,16,16,17,17, 0,17,17,18,18, 0,12,12,17,
	17, 0,16,15,18,18, 0,12,12,16,16, 0,16,16,18,18,
	 0,15,15,17,17, 0,12,12,17,17, 0,16,16,19,18, 0,
	12,12,16,17, 0,16,16,19,19, 0,15,16,16,17, 0,16,
	16,19,17, 0, 0, 0,20,22, 0,13,13,16,16, 0,19,18,
	21, 0, 0,15,15,16,16, 0,16,16,18,18, 0, 0, 0,22,
	21, 0,14,14,16,16, 0,21,19,21,22, 0,16,16,16,16,
	 0, 9, 9,14,14, 0,13,13,15,15, 0,14,14,15,15, 0,
	 0,20,18,19, 0, 0,22,18,18, 0,12,12,15,15, 0,15,
	15,17,18, 0,14,13,14,14, 0,20, 0,18,18, 0,21, 0,
	18,17, 0,13,13,15,16, 0,17,17,18,18, 0,14,14,15,
	15, 0,22,22,20,19, 0,20,21,18,18, 0,20,22,19,19,
	 0, 0, 0, 0, 0, 0,20,20,17,17, 0, 0,22,22,21, 0,
	22, 0,18,18, 0,20,22,19,19, 0, 0, 0, 0, 0, 0,21,
	21,17,18, 0, 0, 0,21,20, 0, 0,22,19,18, 0,18,18,
	15,15, 0,22,21,17,16, 0, 0,22,17,17, 0,20,22,18,
	18, 0, 0,22,20,20, 0,21,19,16,16, 0,21,21,18,18,
	 0,19,19,17,17, 0, 0,22,19,19, 0,22,20,17,17, 0,
	21,19,16,16, 0,22,22,19,18, 0,19,20,16,16, 0,22,
	21,19,21, 0,21,22,17,18, 0,21,20,18,18, 0, 0, 0,
	19,20, 0,20,19,16,16, 0,22,22, 0, 0, 0,21,21,17,
	16, 0,22,20,19,18, 0, 0, 0,20,20, 0,20,19,16,16,
	 0, 0, 0, 0, 0, 0,21,22,17,17, 0,11,11,13,13, 0,
	13,13,15,16, 0,13,13,16,16, 0,17,18,21, 0, 0,17,
	18, 0, 0, 0,12,12,15,16, 0,15,15,19,18, 0,12,12,
	16,16, 0,17,17,22, 0, 0,17,17, 0,22, 0,12,12,17,
	16, 0,16,16,19,20, 0,12,12,16,16, 0,17,17, 0, 0,
	 0,17,17, 0,21, 0,17,16,22, 0, 0, 0, 0, 0, 0, 0,
	15,15,20,22, 0,20,18, 0, 0, 0,18,18, 0, 0, 0,17,
	17,21, 0, 0, 0, 0, 0, 0, 0,15,15,21,22, 0,19,20,
	22, 0, 0,19,18, 0, 0, 0,14,14,18,18, 0,16,16,22,
	20, 0,16,16,22,19, 0,17,17,20,22, 0,19,19, 0, 0,
	 0,15,15,20, 0, 0,18,21, 0,20, 0,15,15,21,20, 0,
	18,17, 0, 0, 0,17,17, 0,22, 0,15,15,19,19, 0,19,
	18, 0, 0, 0,15,15,20, 0, 0,18,18,22,22, 0,17,17,
	 0,20, 0,18,18, 0, 0, 0, 0,22, 0, 0, 0,15,15,19,
	20, 0,20,19, 0, 0, 0,17,17,20,21, 0,17,18,20,22,
	 0, 0, 0, 0,22, 0,15,15,20,20, 0,22,20, 0, 0, 0,
	17,18,20, 0, 0,12,12,17,16, 0,14,14,17,17, 0,13,
	13,17,17, 0,16,16,18,18, 0,17,16,17,17, 0,13,13,
	17,17, 0,15,16,18,18, 0,13,13,16,16, 0,16,16,18,
	18, 0,16,16,17,16, 0,13,13,16,16, 0,17,17,18,17,
	 0,12,12,15,16, 0,17,17,19,19, 0,16,16,16,16, 0,
	16,17,19,18, 0, 0, 0,21,22, 0,14,14,16,16, 0,18,
	18, 0,22, 0,16,16,16,16, 0,16,16,18,17, 0, 0, 0,
	21,20, 0,14,14,16,16, 0,21,22,22, 0, 0,16,16,16,
	16, 0, 9, 9,14,13, 0,13,14,15,16, 0,14,13,15,14,
	 0,22, 0,18,18, 0,21, 0,17,18, 0,13,13,15,15, 0,
	15,16,18,17, 0,14,14,15,14, 0,20,22,18,18, 0,22,
	21,17,17, 0,13,13,15,15, 0,17,17,19,19, 0,14,14,
	14,14, 0, 0,22,18,18, 0, 0,22,17,17, 0, 0,22,19,
	20, 0, 0, 0, 0, 0, 0,21,20,17,16, 0, 0, 0,21,22,
	 0, 0, 0,18,19, 0, 0, 0,18,18, 0, 0, 0, 0, 0, 0,
	22, 0,17,17, 0, 0, 0,20,22, 0, 0, 0,18,19, 0,18,
	19,16,16, 0,22,20,17,17, 0,22,22,17,18, 0,22,22,
	18,17, 0, 0,22,18,19, 0,20,20,17,18, 0, 0,22,19,
	18, 0,22,22,17,17, 0,22, 0,19,19, 0, 0,22,18,18,
	 0,20,22,17,17, 0, 0,22,18,18, 0,19,20,17,17, 0,
	22, 0,20,19, 0,22,21,17,17, 0, 0, 0,18,18, 0, 0,
	 0,22,19, 0,20, 0,17,17, 0,22, 0, 0,22, 0, 0,20,
	17,18, 0,22, 0,19,19, 0, 0, 0, 0,19, 0,19,21,17,
	17, 0, 0, 0, 0, 0, 0,20,21,17,16, 0,11,11,13,13,
	 0,13,13,16,16, 0,13,13,15,16, 0,17,17,21,22, 0,
	17,18, 0, 0, 0,12,12,16,16, 0,15,15,18,18, 0,13,
	13,16,16, 0,17,16,21,21, 0,17,17, 0, 0, 0,13,13,
	16,16, 0,16,16,19,18, 0,13,13,16,16, 0,17,17, 0,
	22, 0,17,18,20,22, 0,17,18, 0, 0, 0, 0, 0, 0, 0,
	 0,15,15,20, 0, 0,18,19, 0, 0, 0,17,17, 0, 0, 0,
	18,17,22, 0, 0, 0, 0, 0, 0, 0,15,16,21,20, 0,20,
	20, 0, 0, 0,18,19, 0, 0, 0,15,15,22,22, 0,17,16,
	20,22, 0,17,17,20,22, 0,18,18, 0,21, 0,19,18, 0,
	 0, 0,16,16,20,20, 0,19,19,22, 0, 0,15,16,21,22,
	 0,18,19,22, 0, 0,17,18, 0, 0, 0,16,16,22, 0, 0,
	19,19, 0,21, 0,15,16,20, 0, 0,18,18, 0,22, 0,18,
	17, 0, 0, 0,18,18, 0, 0, 0, 0, 0, 0, 0, 0,16,16,
	22,21, 0,20,21, 0, 0, 0,17,18,22, 0, 0,18,18, 0,
	 0, 0, 0, 0, 0, 0, 0,16,16,20,19, 0,22,21, 0, 0,
	 0,18,18,22,22,
};

static const static_codebook _44p2_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p2_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p2_p5_0,
	0
};

static const long _vq_quantlist__44p2_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p2_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p2_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p2_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p2_p5_1,
	0
};

static const long _vq_quantlist__44p2_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p6_0[] = {
	 1, 7, 7, 7, 8, 8, 7, 8, 8, 7, 9, 9,10,11,11, 9,
	 8, 8, 7, 8, 9,11,11,11, 9, 8, 8, 6, 7, 7,10,10,
	10,10,10,10,10,10,10,14,14,14,12,11,11,10,11,11,
	15,14,14,13,11,11, 6, 6, 6, 8, 5, 5, 8, 7, 7, 8,
	 7, 7,11,10,10, 9, 7, 7, 9, 7, 7,12,10,10,10, 7,
	 7, 6, 8, 7,12,10,10,12,10,10,11,10,10,15,14,13,
	13,10,10,11,10,10,16,14,14,14,10,10, 7, 7, 7,12,
	11,11,12,11,11,11,11,11,16,14,14,13,12,12,11,11,
	11,17,15,15,14,12,12,10, 9, 9,13,11,11,13,11,11,
	12,11,11,16,14,13,14,11,11,12,11,11,17,15,14,14,
	11,11, 7, 8, 8,12,11,11,12,10,10,12,10,10,16,13,
	14,13,10,10,11,10,10,17,14,14,14,10,10, 7, 7, 7,
	12,11,11,12,11,11,12,11,11,15,14,15,14,12,12,12,
	11,11,17,15,15,14,12,12,10,10, 9,13,11,11,13,11,
	11,13,11,11,16,14,14,14,11,11,13,11,11,16,15,15,
	15,11,11,
};

static const static_codebook _44p2_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p2_p6_0,
	0
};

static const long _vq_quantlist__44p2_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p6_1[] = {
	 2, 6, 6, 7, 7, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9, 8,
	 7, 7, 8, 8, 8, 9, 9, 9, 9, 8, 8, 6, 7, 7, 9, 8,
	 8, 9, 7, 7, 9, 8, 8,10, 8, 8,10, 8, 8,10, 8, 8,
	10, 8, 9,10, 8, 8, 7, 6, 6, 8, 6, 6, 9, 6, 6, 9,
	 7, 7,10, 8, 8, 9, 6, 6, 9, 7, 7,10, 9, 8, 9, 7,
	 7, 7, 7, 7,11, 8, 8,11, 9, 9,10, 9, 9,12, 9, 9,
	12, 8, 8,11, 9, 9,12, 9, 9,12, 8, 8, 8, 7, 7,10,
	 9, 9,10, 9, 9,10, 9, 9,11,10,11,11, 9, 9,11, 9,
	 9,11,11,11,11, 9, 9,10, 8, 8,11, 9, 9,10, 9, 9,
	11, 9, 9,11,10,10,11, 9, 9,11, 9, 9,12,10,10,11,
	 9, 9, 8, 8, 8,11, 9, 9,12, 9, 9,11, 9, 9,12, 9,
	 9,12, 8, 8,12, 9, 9,12, 9,10,12, 8, 8, 9, 7, 7,
	11, 9, 9,11,10,10,11, 9, 9,11,11,11,11, 9, 9,11,
	10,10,12,11,11,11, 9,10,10, 9, 9,11, 9, 9,11,10,
	10,11,10,10,11,11,11,11, 9, 9,11, 9,10,11,11,11,
	11, 9, 9,
};

static const static_codebook _44p2_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p2_p6_1,
	0
};

static const long _vq_quantlist__44p2_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p2_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p2_p7_0,
	0
};

static const long _vq_quantlist__44p2_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p2_p7_1[] = {
	 1, 9, 9, 6, 9, 9, 5, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,
};

static const static_codebook _44p2_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p2_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p2_p7_1,
	0
};

static const long _vq_quantlist__44p2_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p2_p7_2[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p2_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p2_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p2_p7_2,
	0
};

static const long _vq_quantlist__44p2_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p2_p7_3[] = {
	 3, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p2_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p2_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p2_p7_3,
	0
};

static const char _huff_lengthlist__44p2_short[] = {
	 4, 4,12, 9, 8,12,15,17, 4, 2,11, 6, 5, 9,13,15,
	11, 7, 8, 7, 7,10,14,13, 8, 5, 7, 5, 5, 8,12,12,
	 8, 4, 7, 4, 3, 6,11,12,11, 8, 9, 7, 6, 8,11,12,
	15,13,14,12, 9, 7,10,13,16,12,17,12, 7, 5, 8,11,
};

static const static_codebook _huff_book__44p2_short = {
	2, 64,
	(char *)_huff_lengthlist__44p2_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p3_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p3_l0_0[] = {
	 1, 4, 4, 8, 8, 8, 8, 9, 9,10,10,10,10, 4, 6, 5,
	 8, 7, 9, 9, 9, 9,10, 9,11, 9, 4, 5, 6, 7, 8, 9,
	 9, 9, 9, 9,10, 9,10, 8, 9, 8, 9, 8,10, 9,11, 9,
	12,10,12,10, 8, 8, 9, 8, 9, 9,10, 9,11,10,12,10,
	12, 9,10,10,11,10,12,11,12,11,12,12,12,12, 9,10,
	10,11,11,11,11,11,12,12,12,12,12,10,11,11,12,12,
	12,12,12,12,12,12,12,12,10,11,11,12,12,12,12,12,
	12,12,12,12,12,11,12,12,12,12,12,13,12,13,12,13,
	12,12,11,12,12,12,12,12,12,13,12,12,12,12,12,12,
	12,12,13,13,12,13,12,13,12,13,12,12,12,13,12,13,
	12,13,12,13,12,13,12,12,12,
};

static const static_codebook _44p3_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p3_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p3_l0_0,
	0
};

static const long _vq_quantlist__44p3_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p3_l0_1[] = {
	 3, 4, 4, 5, 5, 4, 4, 5, 5, 5, 4, 5, 4, 5, 5, 5,
	 5, 6, 5, 6, 5, 6, 5, 6, 5,
};

static const static_codebook _44p3_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p3_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p3_l0_1,
	0
};

static const long _vq_quantlist__44p3_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p3_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p3_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p3_l1_0,
	0
};

static const char _huff_lengthlist__44p3_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p3_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p3_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p3_long[] = {
	 3, 4,13, 9, 9,12,15,17, 4, 2,18, 5, 7,10,14,18,
	11, 8, 6, 5, 6, 8,11,14, 8, 5, 5, 3, 5, 8,11,13,
	 9, 6, 7, 5, 5, 7, 9,10,11,10, 9, 8, 6, 6, 8,10,
	14,14,11,11, 9, 8, 9,10,17,17,14,13,10, 9,10,10,
};

static const static_codebook _huff_book__44p3_long = {
	2, 64,
	(char *)_huff_lengthlist__44p3_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p3_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p3_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p3_p1_0,
	0
};

static const long _vq_quantlist__44p3_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p3_p2_0[] = {
	 3, 7, 7, 0, 0, 0, 8, 8, 0, 0, 0, 8, 8, 0, 0, 0,
	11,11, 0, 0, 0, 0, 0, 0, 0, 0,10, 9, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,10,11, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0,
	 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,12,12, 0, 0,
	 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0,12,12, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0, 0, 0,
	 5, 5, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5,
	 0, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0,
	 0, 0, 0, 0, 0, 0, 0, 5, 6, 0, 0, 0, 7, 7, 0, 0,
	 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,
	 8, 8, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0, 9, 9, 0,
	 0, 0,10,10, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0,
	 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	10,10, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0,
	 0, 0, 0,11,12, 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0,
	 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 9, 9, 0, 0, 0, 7, 7, 0, 0, 0, 8, 8, 0, 0,
	 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0,
	 0, 0, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5,
	 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 7, 7,
	 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0,
	 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0,
	 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9,
	 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0, 0, 0, 7, 7, 0, 0, 0, 8, 8, 0,
	 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0,
	 0, 0, 0, 0, 0, 0, 8, 7, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 7,
	 7, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,11,11,
	 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 7,
	 7, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0,
	 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0,
	 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9,
	 9, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0,10,10, 0, 0, 0, 9, 9, 0, 0, 0,10,10,
	 0, 0, 0,11,12, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0,11,11, 0, 0,
	 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,
	 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,11,
	11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,12,12,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0,
	 9, 9, 0, 0, 0,10,10, 0, 0, 0,12,12, 0, 0, 0, 0,
	 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9,
	 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0,
	 0, 0, 0, 0, 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	10,10, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0,
};

static const static_codebook _44p3_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p3_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p3_p2_0,
	0
};

static const long _vq_quantlist__44p3_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p3_0[] = {
	 1, 5, 5, 5, 8, 8, 0, 8, 8, 6, 9, 9, 8,10,10, 0,
	 8, 8, 0, 9, 9, 0,12,12, 0, 8, 8, 4, 7, 7, 6,10,
	10, 0,12,12, 7,11,11, 9,12,12, 0,12,12, 0,13,13,
	 0,15,15, 0,12,12, 0, 7, 7, 0, 7, 7, 0, 8, 8, 0,
	 8, 8, 0,10,10, 0, 7, 7, 0, 8, 8, 0,11,11, 0, 7,
	 7, 5, 7, 7, 9, 9, 9, 0,11,10, 9, 9, 9,11,12,12,
	 0,10,10, 0,11,11, 0,13,13, 0,11,11, 6, 7, 7, 9,
	10,10, 0,12,12,10,11,11,11,12,12, 0,12,12, 0,13,
	13, 0,15,15, 0,12,12, 0,10,10, 0,11,11, 0,11,11,
	 0,12,12, 0,13,13, 0,11,11, 0,12,12, 0,15,15, 0,
	11,11, 0, 8, 8, 0,10,10, 0,12,12, 0,11,11, 0,12,
	12, 0,12,12, 0,12,12, 0,15,15, 0,11,11, 0, 7, 7,
	 0,10,10, 0,12,12, 0,10,10, 0,12,13, 0,12,12, 0,
	13,13, 0,14,14, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p3_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p3_p3_0,
	0
};

static const long _vq_quantlist__44p3_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p3_1[] = {
	 3, 4, 4, 0, 8, 8, 0, 8, 8, 0, 9, 9, 0,10,10, 0,
	 8, 8, 0, 9, 9, 0,10,10, 0, 8, 8, 0, 7, 7, 0, 8,
	 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8,
	 0, 8, 8, 0, 8, 8, 0, 7, 7, 0, 6, 6, 0, 7, 7, 0,
	 7, 7, 0,10,10, 0, 6, 6, 0, 7, 7, 0,10,10, 0, 6,
	 5, 0, 8, 8, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0, 9, 9,
	 0, 7, 7, 0, 8, 8, 0, 9, 9, 0, 7, 7, 0, 6, 6, 0,
	 9,10, 0,10,10, 0,10,10, 0,11,11, 0, 9, 9, 0,10,
	10, 0,11,11, 0, 9, 9, 0, 8, 8, 0, 8, 8, 0, 8, 8,
	 0, 9, 9, 0, 9, 9, 0, 8, 8, 0, 8, 8, 0, 9, 9, 0,
	 7, 7, 0, 8, 8, 0, 7, 7, 0, 7, 7, 0, 8, 8, 0, 9,
	 9, 0, 7, 7, 0, 7, 7, 0, 9, 9, 0, 6, 6, 0, 6, 6,
	 0,10,10, 0,10,10, 0,10,10, 0,12,12, 0, 9, 9, 0,
	10,10, 0,12,12, 0, 9, 9, 0, 8, 8, 0, 7, 7, 0, 8,
	 8, 0, 8, 8, 0, 9, 9, 0, 7, 7, 0, 8, 8, 0, 9, 9,
	 0, 7, 7,
};

static const static_codebook _44p3_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p3_p3_1,
	0
};

static const long _vq_quantlist__44p3_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p4_0[] = {
	 1, 6, 6, 7, 7, 7, 7, 7, 7, 7, 8, 8,10,11,11, 9,
	 8, 8, 8, 8, 8,11,11,11,10, 8, 8, 5, 7, 7, 9,11,
	11,10,11,11,10,11,11,12,13,14,11,12,12,10,11,11,
	13,14,14,12,12,12, 5, 6, 6, 8, 6, 6, 8, 7, 7, 8,
	 7, 7,11,10,10,10, 7, 7, 9, 7, 7,12,11,11,11, 7,
	 7, 7, 7, 7,11,10,10,12,10,10,11,10,10,15,13,13,
	13,10,10,12,11,11,15,13,13,14,11,11, 7, 7, 7,11,
	11,11,12,11,11,12,11,11,14,14,14,14,12,12,12,12,
	12,16,15,15,14,12,12, 0,10,10, 0,11,11, 0,11,12,
	 0,11,11, 0,14,14, 0,11,11, 0,12,12, 0,15,15, 0,
	11,11, 8, 8, 8,12,10,10,12,10,10,13,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,14,10,10, 8, 7, 7,
	12,11,11,13,11,11,12,11,11,15,14,14,14,12,12,13,
	12,12,15,14,14,15,12,12, 0,11,11, 0,12,12, 0,12,
	12, 0,12,12, 0,15,15, 0,12,12, 0,13,13, 0,14,15,
	 0,12,12,
};

static const static_codebook _44p3_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p3_p4_0,
	0
};

static const long _vq_quantlist__44p3_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p3_p4_1[] = {
	 3, 4, 5, 8, 8,12,10,10,12,12,12,10,10,12,12,13,
	11,11,12,12,13,12,12,12,12,13,10,10,13,13,13,13,
	13,13,13,13,10,10,13,13,13,11,11,13,13,14,13,13,
	12,12,13,10,10,13,13,13,13,13,13,13,13,10,10,12,
	12,13,11,11,13,13,13,13,13,12,12,13,12,12,13,13,
	13,13,13,13,13,14,11,11,12,12,14,12,12,13,12,14,
	14,14,12,12,13,14,14,13,13,14,13,13,13,13,14,14,
	14,12,12,14,13,13,13,13,14,14,14,12,12,12, 8, 8,
	11,11,12,12,12,11,11,12,11,11,10,10,13,12,12,10,
	10,13,12,12,10,10,13,12,12,12,12,14,12,12,12,12,
	13,13,13,11,11,14,12,12,11,11,14,12,12,12,12,14,
	12,12,12,12,13,12,12,12,12,13,13,13,11,11,14,12,
	12,11,11,14,12,12,12,12,14,13,13,12,12,14,12,12,
	12,11,14,13,13,11,11,14,13,12,11,11,14,13,13,11,
	11,14,13,13,12,12,14,12,12,12,12,15,13,13,12,12,
	14,12,12,11,11,14,13,13,11,11,12, 9, 9,10,10,12,
	 7, 7,11,11,12, 9, 9,12,12,13,10,10,10,10,14,14,
	14,11,11,13, 9, 9,12,12,14,14,14,12,12,13, 8, 8,
	11,11,14, 9, 9,12,12,14,14,14,11,11,13, 9, 9,12,
	12,14,14,14,12,12,14, 8, 8,11,11,14, 9, 9,12,12,
	14,14,14,11,11,14,10,10,12,12,14,14,14,13,13,14,
	 9, 9,11,11,14,10,10,12,12,14,14,14,11,11,14,14,
	15,12,12,15,14,14,14,14,15,14,14,11,11,14,14,14,
	12,12,14,14,14,11,11,14,11,11,10,10,14,10,10,10,
	10,14,10,10,10,10,15,11,11, 9, 9,14,12,12, 9, 9,
	15,11,11,11,11,15,13,13,11,11,15,10,10,10,10,15,
	11,11,10,10,15,13,13,11,11,15,11,11,11,11,15,13,
	13,11,11,15,10,10,10,10,15,11,11,10,10,15,13,13,
	11,11,15,12,12,11,11,15,13,13,11,11,15,11,11,10,
	10,15,12,12,10,10,15,13,13,10,10,15,14,14,11,11,
	15,13,13,11,11,15,14,14,10,11,15,13,13,10,10,15,
	13,14,10,10,14,13,13,10,10,14,13,13,10,10,14,13,
	13,10,10,14,13,13, 9, 9,14,14,14, 9, 9,15,14,14,
	11,11,15,14,14,10,10,15,14,14,10,10,15,14,14,11,
	11,15,14,14,10,10,15,14,14,11,11,15,14,14,10,10,
	14,14,14,10,10,15,14,14,10,10,14,14,14,10,10,15,
	14,14,11,11,15,14,14,11,11,14,14,14,10,10,15,14,
	14,10,10,14,14,14, 9, 9,15,15,15,11,11,15,14,14,
	12,12,15,15,14,10,10,15,14,14,10,10,14,15,15, 9,
	 9,14,10,10,12,12,17, 9, 9,12,12,17,10,10,13,13,
	17,11,11,12,12,18,14,14,12,12,17,10,10,13,13,17,
	14,14,12,12,17, 9, 9,12,12,17,11,11,12,12,17,14,
	14,12,12,18,10,10,13,13,18,14,14,13,13,18, 9, 9,
	12,12,18,10,10,13,13,18,14,14,12,12,18,11,11,13,
	13,18,14,14,13,13,18,10,10,12,12,17,11,11,12,12,
	17,14,14,12,12,18,15,15,13,13,18,14,14,14,14,18,
	15,15,12,12,18,14,14,12,12,18,15,15,12,12,13, 7,
	 7,11,11,14,15,15,11,11,14,15,15,12,12,14,15,15,
	11,11,15,15,15,11,11,14,15,15,12,12,14,15,15,12,
	12,14,15,15,11,11,14,15,15,11,11,15,15,15,12,12,
	14,15,15,12,12,14,15,15,12,12,14,15,15,11,11,14,
	15,15,11,11,15,15,15,12,12,15,15,15,12,12,14,15,
	15,12,12,14,15,14,12,12,14,15,15,11,11,15,14,14,
	12,12,15,15,15,12,12,15,16,16,12,12,15,15,15,12,
	12,15,15,15,12,12,15,15,15,12,12,13,13,13,11,10,
	14,14,15,11,11,14,14,14,12,12,15,14,14,10,10,15,
	15,15,11,11,14,15,15,12,12,14,14,14,11,11,14,15,
	15,11,11,14,15,15,12,12,15,15,15,11,11,14,15,15,
	12,12,14,14,14,12,12,14,15,15,11,11,14,15,15,12,
	12,15,15,15,11,11,15,15,15,12,12,15,14,14,12,12,
	14,15,15,11,11,14,15,15,11,11,15,15,15,10,10,15,
	15,16,12,12,15,15,15,14,14,15,15,15,11,11,15,15,
	15,12,12,15,15,15,11,11,14,11,11,10,10,15, 9, 9,
	12,12,15,10,10,12,12,15,11,11,11,11,15,14,14,12,
	12,15,10,10,13,13,15,14,14,12,12,15, 9, 9,12,12,
	15,10,10,13,13,15,13,13,12,11,15,10,10,12,12,15,
	14,14,12,12,15, 9, 9,11,11,15,11,11,12,12,15,13,
	13,11,11,15,11,11,13,13,15,13,14,13,14,15,11,11,
	11,11,15,11,11,12,12,15,14,14,11,11,15,14,14,13,
	13,15,14,14,20,20,15,14,14,12,12,15,14,14,12,12,
	15,14,14,11,11,14,13,13,10,10,14,13,13,12,12,14,
	14,13,12,12,15,14,14,12,12,15,14,14,11,11,15,14,
	14,12,12,15,14,14,13,13,15,14,14,12,11,15,14,14,
	11,11,15,14,14,13,13,15,14,14,12,12,15,14,14,13,
	13,15,14,14,12,11,15,14,14,12,12,15,14,14,13,13,
	15,14,14,13,13,15,14,14,12,12,15,14,14,12,12,15,
	14,14,12,12,15,15,15,13,13,15,15,15,13,13,15,14,
	14,13,13,15,15,15,13,13,15,14,15,12,12,15,15,15,
	13,13,14,10,10,12,13,17, 9, 9,12,12,17,10,10,13,
	13,17,11,11,12,12,18,14,14,12,12,18,10,10,13,13,
	18,14,14,12,12,17, 9, 9,12,12,18,10,11,13,13,18,
	14,14,12,12,17,10,10,12,12,17,14,14,12,12,17, 9,
	 9,12,12,17,11,11,12,12,17,14,14,12,12,18,11,11,
	12,12,18,14,14,13,13,18,11,11,12,12,18,11,11,12,
	12,18,14,14,12,12,18,15,15,12,12,18,14,14,13,13,
	18,15,15,12,12,17,14,14,12,12,17,15,15,12,12,13,
	 7, 7,11,11,14,15,15,11,11,14,15,15,11,11,14,15,
	14,12,12,15,15,15,12,11,14,15,15,12,12,14,15,15,
	12,12,14,15,15,11,11,14,15,15,11,11,15,15,15,13,
	13,14,15,15,11,11,14,15,15,13,12,14,15,15,11,11,
	14,15,15,11,11,15,15,15,13,13,14,15,15,12,12,15,
	15,15,12,12,15,15,15,11,11,15,15,15,11,11,15,15,
	15,12,12,15,15,15,13,13,15,16,16,12,12,15,15,15,
	12,13,15,15,15,12,12,15,15,15,12,12,13,13,13,11,
	11,14,14,14,11,11,14,14,14,12,12,14,14,14,10,10,
	15,14,14,11,11,14,15,15,12,12,14,14,14,12,12,14,
	15,15,11,11,14,15,14,12,12,15,14,14,11,11,14,15,
	15,12,12,14,14,14,11,11,14,15,15,11,11,14,14,14,
	12,12,15,15,14,11,11,15,15,15,12,12,15,14,14,12,
	12,14,15,15,11,11,14,15,14,11,11,15,15,15,10,10,
	15,15,15,12,12,15,14,14,14,13,15,15,15,11,11,15,
	15,15,11,11,15,15,15,10,10,14,11,11,10,10,15, 9,
	 9,12,12,15,10,10,12,12,15,11,11,11,11,15,14,14,
	12,12,15,10,10,13,13,15,13,13,12,12,15, 9, 9,12,
	12,15,11,11,13,13,15,14,14,12,12,15,10,10,13,13,
	15,13,14,12,12,15, 9, 9,12,12,15,10,10,13,13,15,
	13,13,11,11,15,11,11,13,13,15,14,14,13,13,15,10,
	10,11,11,15,11,11,12,12,15,14,14,11,11,15,14,14,
	13,13,15,14,14,21,20,15,14,14,11,11,15,14,14,12,
	12,15,14,14,11,11,14,13,13,10,10,14,13,13,11,11,
	15,14,14,12,12,15,14,14,12,12,14,14,14,12,12,15,
	14,14,12,12,15,14,14,13,13,14,14,14,11,11,15,14,
	14,11,11,15,14,14,13,13,15,14,14,12,12,15,14,14,
	13,13,14,14,14,11,11,15,14,14,11,11,14,14,14,13,
	13,15,14,14,12,12,15,14,14,12,12,15,14,14,12,12,
	15,14,14,12,12,14,14,14,13,13,15,15,15,13,13,16,
	14,14,12,13,15,15,15,13,13,15,14,14,12,12,15,15,
	15,13,13,15,11,11,13,12,18,10,10,12,12,17,11,11,
	12,12,18,12,12,11,11,18,14,14,12,12,18,11,11,13,
	13,17,14,14,12,12,18,10,10,12,12,18,12,12,12,12,
	18,14,15,12,12,18,11,11,13,13,18,14,14,12,12,17,
	10,10,12,12,18,11,11,12,12,18,15,14,12,12,17,12,
	12,12,12,17,14,14,12,12,17,11,11,11,11,17,12,12,
	12,11,17,15,15,11,11,18,15,15,12,12,18,14,15,13,
	13,18,15,15,11,11,17,15,15,12,12,18,15,15,11,11,
	14, 9, 9,11,11,14,15,15,11,11,15,15,15,11,11,15,
	15,15,12,11,15,15,15,12,12,15,15,15,11,11,15,15,
	15,13,13,14,15,15,11,11,15,15,15,11,11,15,15,15,
	13,13,15,15,15,11,11,15,15,15,13,13,15,15,15,11,
	11,15,15,15,11,11,15,15,15,13,13,15,15,15,12,12,
	15,15,15,13,13,15,15,14,11,11,15,15,15,12,12,15,
	15,15,12,12,16,15,15,13,13,15,16,16,13,13,16,15,
	15,12,12,15,15,15,13,12,15,15,15,12,12,13,12,12,
	11,11,14,14,14,11,11,14,14,14,12,12,15,14,14,11,
	11,15,14,14,12,12,15,14,14,12,12,15,14,14,12,12,
	14,15,15,11,11,15,14,14,12,12,15,14,14,11,11,15,
	14,14,12,12,15,14,14,12,12,14,15,15,11,11,15,14,
	14,12,12,15,14,14,11,11,15,15,15,12,12,15,14,14,
	12,12,15,15,15,11,11,15,14,14,11,11,15,14,15,11,
	11,15,15,15,12,12,15,14,14,13,13,16,15,15,11,11,
	15,14,14,12,12,15,15,15,11,11,14,11,11, 9, 9,15,
	10,10,12,12,14,11,11,12,12,15,12,12,12,12,15,14,
	14,13,13,15,11,11,13,13,15,14,14,13,13,15,10,10,
	12,12,15,12,12,13,13,15,14,14,13,13,15,11,11,12,
	12,15,14,14,13,13,14,10,10,12,12,15,12,12,13,13,
	15,14,14,12,12,15,12,12,13,13,15,14,14,15,15,15,
	11,11,12,12,15,12,12,12,13,15,14,14,12,12,15,15,
	15,14,14,15,14,14,20,20,15,14,14,12,12,15,14,14,
	13,13,15,14,14,12,12,14,13,13,10,10,14,13,13,11,
	11,14,13,13,12,12,14,14,14,12,12,15,14,14,13,13,
	15,14,14,12,12,14,14,14,14,14,14,14,14,11,11,15,
	14,14,12,12,15,14,14,14,14,15,14,14,12,12,14,14,
	14,14,14,14,14,14,11,11,15,14,14,12,12,14,14,14,
	14,14,15,14,14,12,12,15,14,14,13,13,15,14,14,12,
	12,15,14,14,12,12,14,14,14,14,13,15,15,15,14,14,
	15,14,14,13,13,15,15,15,14,14,15,14,14,13,13,15,
	15,15,13,13,14,13,13,13,13,18,15,15,12,12,18,15,
	15,13,12,18,15,16,11,11,18,16,17,12,12,18,15,15,
	13,13,18,17,17,12,12,18,15,15,12,12,17,15,15,12,
	12,18,17,17,12,12,18,15,15,13,13,18,16,17,12,12,
	17,15,15,12,12,18,15,15,12,12,18,16,17,11,12,18,
	16,16,12,12,17,16,17,12,12,18,15,15,11,11,18,15,
	15,12,12,18,17,17,11,11,17,17,17,12,12,18,16,16,
	13,13,18,17,17,11,11,18,16,16,12,12,18,17,17,11,
	11,15,14,14,11,11,16,15,15,11,11,16,15,15,12,12,
	16,15,15,12,12,17,15,15,14,13,16,15,15,12,12,17,
	15,15,14,14,16,15,15,11,11,16,15,15,12,12,18,15,
	15,13,13,16,15,15,11,11,17,15,15,14,14,16,15,15,
	11,11,16,15,15,12,12,17,15,15,13,13,16,15,15,12,
	12,17,16,15,14,14,16,14,15,12,12,16,15,15,12,12,
	18,15,15,13,13,17,15,15,14,14,17,16,16,15,15,17,
	15,15,13,13,17,15,15,14,14,18,15,15,13,13,15,12,
	13,11,11,15,14,14,12,12,16,14,14,12,12,16,14,14,
	12,12,16,14,14,12,12,16,14,14,13,12,17,14,14,13,
	13,16,15,15,12,12,16,14,14,12,12,17,14,14,12,12,
	16,14,14,12,12,17,14,14,13,13,15,15,15,12,12,16,
	14,14,12,12,17,14,14,12,12,17,15,15,12,12,17,14,
	14,13,13,16,15,15,12,12,16,14,14,12,12,17,15,15,
	12,12,18,15,15,13,13,17,14,14,13,13,17,15,15,12,
	12,17,14,14,12,12,17,15,15,12,12,14,15,15, 9, 9,
	15,15,15,12,12,15,15,15,13,13,15,15,15,14,14,15,
	15,15,19,19,15,15,16,13,13,15,15,16,19,20,15,15,
	15,13,12,15,16,16,14,14,15,15,15,19,19,15,15,15,
	13,13,15,16,15,20,19,14,15,15,13,13,15,15,15,14,
	14,15,15,15,19,19,15,15,15,14,14,15,16,16,19,20,
	15,15,15,14,14,15,15,15,14,14,15,15,15,19,19,15,
	15,15,20,19,15,16,16,20,19,15,15,15,19,19,15,16,
	16,20,20,15,15,15,19,20,14,13,13,10,10,14,14,14,
	11,11,14,14,14,12,12,15,14,14,13,13,15,14,14,19,
	20,15,14,14,12,12,14,14,14,20,19,14,14,14,11,11,
	15,14,14,12,12,15,14,14,20,20,15,14,14,12,12,14,
	14,14,20,19,14,14,14,11,11,15,14,14,12,12,15,14,
	14,19,20,15,14,14,13,13,15,14,14,22,19,15,15,14,
	12,12,15,14,14,13,13,14,15,15,22,20,15,15,15,20,
	20,15,14,14,21,20,15,15,15,20,21,15,14,14,20,20,
	14,15,15,20,20,
};

static const static_codebook _44p3_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p3_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p3_p4_1,
	0
};

static const long _vq_quantlist__44p3_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p3_p5_0[] = {
	 2, 6, 6,14,14, 6, 7, 7,14,14, 7, 7, 7,15,15, 0,
	12,12,15,15, 0,13,13,15,15, 7, 8, 8,15,15,10,10,
	10,16,16, 9, 8, 8,15,15, 0,13,13,18,17, 0,13,13,
	16,16, 8, 8, 8,15,15,12,11,11,16,16, 9, 8, 8,15,
	15, 0,13,13,18,18, 0,13,13,16,16, 0,14,14,17,17,
	 0,20, 0,19,20, 0,12,12,16,16, 0,16,16,20,22, 0,
	14,14,16,16, 0,14,14,17,17, 0,20,22,20,19, 0,13,
	13,15,16, 0,17,18, 0,21, 0,15,15,16,16, 5, 7, 7,
	13,13, 8, 9, 9,14,14,10,10,10,14,14, 0,20,22,18,
	18, 0,22,21,18,17, 9,10,10,14,14,12,12,12,17,17,
	12,10,10,14,14, 0, 0,20,17,17, 0,22,21,17,18,11,
	10,10,14,14,14,13,13,18,18,12,11,11,14,14, 0,22,
	21,18,19, 0,20, 0,17,17, 0,22, 0,18,18, 0, 0, 0,
	 0, 0, 0,20,20,17,17, 0,22, 0,22,21, 0,21, 0,19,
	18, 0,22,22,18,18, 0, 0, 0, 0, 0, 0,21, 0,17,17,
	 0,22, 0,20,20, 0, 0, 0,19,18, 6, 6, 6,12,12, 8,
	 6, 6,10,10, 8, 6, 6,13,12, 0,10,10,11,11, 0,11,
	11,13,13, 8, 7, 7,13,13,11, 9, 9,13,13,10, 6, 6,
	12,12, 0,10,10,14,14, 0,10,10,13,13, 9, 7, 7,13,
	13,12,10,10,13,13,10, 6, 6,12,12, 0,11,11,15,15,
	 0,10,10,13,13, 0,12,12,15,14, 0,19,20,16,17, 0,
	 9, 9,13,13, 0,14,14,20,21, 0,12,11,13,12, 0,12,
	12,15,14, 0,20,19,17,17, 0,10,10,12,13, 0,15,15,
	22,21, 0,12,12,12,13, 0,10,10,12,12, 0,11,11,15,
	15, 0,11,11,15,15, 0,15,15,22,22, 0,16,17, 0, 0,
	 0,11,11,15,15, 0,14,14,18,18, 0,11,11,16,16, 0,
	16,15, 0,21, 0,16,16, 0, 0, 0,12,12,15,15, 0,14,
	14,19,19, 0,11,11,15,15, 0,15,15,22, 0, 0,16,16,
	22, 0, 0,16,16, 0,21, 0, 0, 0, 0, 0, 0,15,15,19,
	20, 0,18,18, 0, 0, 0,17,17, 0, 0, 0,17,17, 0, 0,
	 0, 0, 0, 0, 0, 0,16,15,22,21, 0,20,20, 0, 0, 0,
	18,18, 0, 0, 0,10,10,12,12, 0,10,10,11,11, 0,11,
	11,12,12, 0,11,11, 9, 9, 0,13,12,12,12, 0,11,11,
	13,13, 0,13,13,12,12, 0,10,10,12,12, 0,13,12,13,
	13, 0,12,12,12,12, 0,11,11,13,13, 0,13,13,12,12,
	 0,10,10,12,12, 0,13,13,14,13, 0,12,12,12,12, 0,
	14,13,13,14, 0,20,21,15,15, 0,11,11,12,12, 0,15,
	16,20,20, 0,12,13,10,10, 0,13,13,14,13, 0,20,20,
	15,15, 0,11,11,12,12, 0,16,17,21,21, 0,13,13,11,
	11, 6, 7, 7,16,15,11, 9, 9,14,15,12, 9, 9,16,16,
	 0,13,13,15,15, 0,14,14,17,17,10, 9, 9,16,16,14,
	12,12,16,16,12, 9, 9,15,15, 0,13,13,17,18, 0,13,
	13,15,15,12,10,10,17,17,15,12,12,17,17,13, 9, 9,
	16,16, 0,13,13,18,19, 0,14,14,16,16, 0,15,15,18,
	18, 0, 0, 0,20,19, 0,12,12,17,16, 0,16,17, 0,21,
	 0,14,15,16,16, 0,15,15,18,18, 0, 0,22,19,21, 0,
	13,13,16,16, 0,18,17,22,22, 0,15,15,16,16, 7, 7,
	 7,13,13,11,10,10,15,15,12,10,10,14,14, 0,21, 0,
	18,17, 0,21,22,18,18,11,10,10,15,15,14,12,12,17,
	17,14,11,11,14,14, 0,21,20,18,18, 0,22,21,18,17,
	12,11,10,16,16,16,14,14,17,19,14,11,11,15,15, 0,
	 0,22,19,19, 0,21,22,18,18, 0,21, 0,18,19, 0, 0,
	 0,22, 0, 0,22,21,17,17, 0, 0, 0,20,22, 0, 0,21,
	18,18, 0, 0, 0,19,20, 0, 0, 0, 0, 0, 0, 0,21,17,
	17, 0, 0, 0,22,21, 0, 0, 0,19,19,10, 9, 9,14,13,
	13,10,10,12,12,13,10,10,14,14, 0,13,13,12,12, 0,
	15,14,16,15,13,10,10,14,14,15,12,12,14,14,15,10,
	10,14,14, 0,14,14,15,15, 0,14,13,14,14,13,10,10,
	15,15,17,13,13,15,15,14,10,10,14,14, 0,14,14,15,
	16, 0,14,14,15,15, 0,15,15,16,16, 0,21,22,17,18,
	 0,12,12,14,14, 0,17,17,20,21, 0,14,14,14,14, 0,
	15,15,16,16, 0,21,22,18,18, 0,13,13,14,14, 0,18,
	18,22, 0, 0,15,15,14,14, 0,11,11,13,13, 0,12,12,
	16,15, 0,12,12,16,16, 0,16,16, 0, 0, 0,16,17, 0,
	22, 0,12,12,16,16, 0,14,14,17,18, 0,11,11,16,16,
	 0,15,15, 0,21, 0,16,16,21,22, 0,12,12,16,16, 0,
	15,15,19,19, 0,12,12,17,16, 0,16,16,21,22, 0,16,
	16, 0, 0, 0,17,17, 0,22, 0, 0, 0, 0, 0, 0,15,15,
	19,20, 0,17,19, 0, 0, 0,17,17,22, 0, 0,17,17, 0,
	22, 0, 0, 0, 0, 0, 0,15,15,21, 0, 0,19,20, 0, 0,
	 0,19,18,22, 0, 0,11,12,14,14, 0,11,11,14,14, 0,
	12,12,15,15, 0,13,13,13,13, 0,14,14,16,16, 0,12,
	12,15,15, 0,14,14,16,15, 0,11,11,15,15, 0,13,13,
	16,16, 0,13,13,15,15, 0,12,12,15,15, 0,15,14,16,
	16, 0,11,11,15,15, 0,14,14,17,17, 0,13,13,15,15,
	 0,15,15,16,16, 0, 0, 0,18,18, 0,12,12,14,14, 0,
	16,16,22, 0, 0,14,14,15,15, 0,15,15,16,17, 0,21,
	22,18,18, 0,13,13,15,14, 0,18,17,22, 0, 0,14,14,
	15,15, 8, 8, 8,16,15,12,10,10,16,15,12,10,10,16,
	16, 0,14,14,16,17, 0,14,14,17,16,12,10,10,17,18,
	14,12,12,18,18,14,10,10,16,16, 0,14,14,18,18, 0,
	14,14,16,16,12, 9, 9,16,16,17,13,13,16,17,14, 9,
	 9,15,15, 0,14,14,18,19, 0,13,13,15,15, 0,15,15,
	18,19, 0, 0, 0,22,21, 0,13,13,16,16, 0,16,16,22,
	 0, 0,15,15,16,16, 0,14,14,18,17, 0, 0, 0,20, 0,
	 0,13,13,16,16, 0,18,18, 0, 0, 0,15,15,16,16, 8,
	 7, 7,13,13,12,10,10,15,15,12,10,10,14,14, 0,22,
	22,19,18, 0, 0, 0,18,18,12,10,10,15,15,14,13,13,
	17,17,14,11,11,15,15, 0,19,20,18,18, 0,22,21,17,
	18,13,11,11,15,15,16,13,13,18,18,14,11,11,14,15,
	 0,22,21,20,19, 0,22,21,17,17, 0, 0,22,19,18, 0,
	 0, 0, 0, 0, 0,22,20,17,17, 0, 0, 0,21,20, 0, 0,
	 0,19,17, 0, 0,22,19,19, 0, 0, 0, 0, 0, 0,22,20,
	18,17, 0, 0, 0, 0, 0, 0, 0, 0,18,18, 0,10,10,14,
	14, 0,11,11,14,14, 0,11,11,15,15, 0,14,14,14,14,
	 0,15,15,16,16, 0,11,11,16,16, 0,13,13,16,16, 0,
	11,11,15,15, 0,14,14,16,16, 0,14,14,15,15, 0,11,
	11,15,15, 0,13,13,15,15, 0,10,10,15,15, 0,15,15,
	17,17, 0,14,14,14,14, 0,16,16,16,16, 0, 0,22,19,
	19, 0,13,13,14,14, 0,17,17, 0, 0, 0,15,15,14,14,
	 0,16,16,17,17, 0, 0,22,18,18, 0,13,13,14,14, 0,
	21,18, 0, 0, 0,15,15,14,14, 0,11,11,13,13, 0,12,
	12,15,15, 0,12,12,16,15, 0,16,16, 0, 0, 0,17,17,
	22,22, 0,12,12,16,16, 0,14,14,18,18, 0,11,12,16,
	16, 0,15,16, 0,21, 0,16,16,22,21, 0,12,12,16,16,
	 0,15,15,19,20, 0,11,12,16,16, 0,15,15,20,22, 0,
	16,16, 0,22, 0,17,17,22, 0, 0, 0, 0, 0, 0, 0,15,
	15,21,22, 0,19,18, 0, 0, 0,17,17, 0, 0, 0,17,17,
	 0,22, 0, 0, 0, 0, 0, 0,16,15,22, 0, 0,19,19, 0,
	 0, 0,17,18, 0, 0, 0,12,12,15,15, 0,12,12,15,15,
	 0,12,12,15,15, 0,13,13,14,14, 0,15,15,16,17, 0,
	12,12,16,16, 0,14,14,16,16, 0,12,11,15,16, 0,14,
	14,16,17, 0,14,14,16,16, 0,13,12,16,16, 0,15,15,
	16,16, 0,11,11,15,15, 0,14,14,16,16, 0,14,14,15,
	15, 0,15,15,18,17, 0, 0,22, 0,20, 0,13,13,15,15,
	 0,16,17,22,22, 0,14,14,15,15, 0,15,15,17,18, 0,
	20, 0,19,19, 0,13,13,15,15, 0,18,18,22, 0, 0,14,
	14,15,15, 0,11,11,16,16, 0,14,14,17,16, 0,13,13,
	17,17, 0,16,16,17,17, 0,17,17,18,19, 0,12,12,16,
	17, 0,15,15,18,18, 0,12,12,16,16, 0,16,16,19,18,
	 0,16,16,17,16, 0,12,13,17,17, 0,17,16,18,17, 0,
	13,12,16,16, 0,16,16,18,19, 0,16,16,16,17, 0,16,
	16,18,18, 0,22, 0,22,22, 0,13,13,16,16, 0,19,18,
	22,20, 0,16,15,16,16, 0,16,17,18,18, 0, 0, 0,22,
	20, 0,14,14,16,16, 0,19,19, 0, 0, 0,16,16,16,16,
	 0, 9, 9,13,13, 0,13,13,15,15, 0,14,14,15,15, 0,
	 0,22,17,18, 0,22, 0,18,19, 0,12,12,15,15, 0,15,
	16,17,17, 0,14,14,14,14, 0,22, 0,18,18, 0,21,22,
	17,17, 0,13,13,15,15, 0,17,17,17,18, 0,14,14,15,
	15, 0,22,21,21,19, 0,20,21,17,17, 0,21,21,19,18,
	 0, 0, 0, 0, 0, 0,21,21,17,17, 0, 0, 0,22,22, 0,
	 0,22,19,18, 0, 0,21,19,18, 0, 0, 0, 0,22, 0,19,
	20,17,17, 0, 0, 0, 0,22, 0, 0, 0,19,18, 0,19,19,
	15,16, 0,21,19,16,17, 0, 0,21,17,17, 0, 0,22,17,
	17, 0,22,22,18,19, 0,20,20,16,16, 0, 0,22,18,18,
	 0,20,19,16,17, 0,22,21,20,19, 0, 0,21,17,17, 0,
	21,20,17,17, 0, 0, 0,18,18, 0,19,19,17,16, 0,22,
	 0,19,19, 0,21,22,17,18, 0, 0,22,19,18, 0, 0, 0,
	19,20, 0,19,19,16,16, 0,22,22,22, 0, 0,20,22,16,
	16, 0,22,20,18,19, 0, 0, 0,20,19, 0,20,20,16,16,
	 0, 0, 0, 0, 0, 0,22,20,17,16, 0,11,11,13,13, 0,
	14,13,15,15, 0,13,13,16,15, 0,18,17,21, 0, 0,18,
	18,21, 0, 0,12,12,15,15, 0,15,16,17,18, 0,12,12,
	15,15, 0,17,17,22,20, 0,17,18,22, 0, 0,12,12,17,
	16, 0,16,17,19,19, 0,13,13,16,16, 0,17,17, 0,22,
	 0,17,17, 0,21, 0,18,18,20,22, 0, 0, 0, 0, 0, 0,
	15,15,21,20, 0,20,19, 0, 0, 0,18,18,22, 0, 0,17,
	17,22, 0, 0, 0, 0, 0, 0, 0,15,16,20,22, 0,20,21,
	 0, 0, 0,19,18, 0, 0, 0,15,15,19,19, 0,17,16,20,
	20, 0,16,17,20,21, 0,18,17, 0, 0, 0,19,19, 0, 0,
	 0,15,15,21,19, 0,19,19, 0, 0, 0,15,15,22,22, 0,
	18,18, 0,22, 0,17,18,22,21, 0,15,15,20,19, 0,19,
	19, 0, 0, 0,15,15,20,22, 0,18,19,20, 0, 0,18,17,
	21,21, 0,18,18,19,22, 0, 0, 0, 0, 0, 0,15,15,20,
	19, 0,19,19, 0, 0, 0,18,18,21,22, 0,18,18,22, 0,
	 0, 0, 0, 0, 0, 0,15,15,19,20, 0,21,21, 0, 0, 0,
	17,17,20,20, 0,12,12,17,17, 0,14,14,16,17, 0,13,
	14,17,17, 0,16,16,17,17, 0,17,17,17,19, 0,13,13,
	17,17, 0,16,16,18,18, 0,13,13,16,16, 0,16,16,18,
	18, 0,16,16,17,17, 0,13,13,17,17, 0,17,17,18,17,
	 0,12,12,15,16, 0,17,18,19,20, 0,16,16,16,16, 0,
	17,16,18,19, 0, 0,22,21,22, 0,14,14,16,16, 0,19,
	19, 0, 0, 0,16,16,16,16, 0,16,16,18,17, 0, 0,22,
	21,21, 0,14,14,16,16, 0,22,20,22, 0, 0,16,16,15,
	15, 0, 9, 9,13,13, 0,14,14,15,15, 0,14,14,14,14,
	 0,22,22,18,18, 0, 0,22,18,18, 0,12,12,15,15, 0,
	16,16,18,17, 0,14,14,14,14, 0,20,21,18,18, 0,22,
	21,17,17, 0,13,13,15,15, 0,17,17,18,18, 0,14,14,
	14,14, 0, 0,21,18,19, 0, 0,22,17,17, 0,22,22,19,
	18, 0, 0, 0, 0, 0, 0,19,21,17,17, 0, 0, 0,22,20,
	 0, 0,21,18,19, 0, 0,22,18,18, 0, 0, 0, 0,22, 0,
	20,22,17,17, 0, 0, 0,20,22, 0, 0, 0,18,18, 0,19,
	21,16,16, 0,20,22,16,17, 0,20, 0,17,17, 0,22, 0,
	18,17, 0,21, 0,18,19, 0,20,20,17,17, 0,22, 0,18,
	18, 0,21,20,17,17, 0, 0,20,20,19, 0, 0,21,18,17,
	 0,21,21,17,17, 0,22, 0,18,17, 0,19,19,17,17, 0,
	 0,22,20,21, 0, 0,21,17,17, 0,22, 0,18,18, 0, 0,
	 0,20,22, 0,20,19,16,16, 0, 0, 0, 0, 0, 0,22,22,
	17,17, 0,22, 0,18,19, 0, 0, 0,21,20, 0,19,21,16,
	17, 0, 0, 0, 0, 0, 0,22,22,17,16, 0,11,11,13,13,
	 0,13,13,15,15, 0,13,13,15,15, 0,17,17,22,21, 0,
	18,18,22, 0, 0,12,13,16,15, 0,15,16,18,18, 0,13,
	13,16,16, 0,17,17, 0,22, 0,17,17,22,22, 0,13,13,
	16,16, 0,16,16,19,18, 0,13,13,16,16, 0,18,17, 0,
	20, 0,18,17,20, 0, 0,17,17,21, 0, 0, 0, 0, 0, 0,
	 0,15,15,21,22, 0,19,20, 0, 0, 0,18,18, 0, 0, 0,
	18,17, 0, 0, 0, 0, 0, 0, 0, 0,16,16,22,22, 0,20,
	20, 0, 0, 0,21,19, 0, 0, 0,15,15,20,19, 0,16,16,
	22,20, 0,17,17, 0,22, 0,18,18, 0,22, 0,19,17, 0,
	 0, 0,15,16,22,20, 0,18,19, 0, 0, 0,16,16,22,20,
	 0,18,18, 0,22, 0,18,18,22, 0, 0,16,16,21,20, 0,
	19,20, 0,22, 0,16,16, 0,22, 0,18,18, 0,22, 0,18,
	18, 0,21, 0,19,18, 0,22, 0, 0, 0, 0, 0, 0,16,16,
	21,20, 0,20, 0, 0, 0, 0,18,18,21, 0, 0,18,18, 0,
	 0, 0, 0, 0, 0, 0, 0,16,16,21,19, 0, 0, 0, 0, 0,
	 0,18,18, 0,21,
};

static const static_codebook _44p3_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p3_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p3_p5_0,
	0
};

static const long _vq_quantlist__44p3_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p3_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p3_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p3_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p3_p5_1,
	0
};

static const long _vq_quantlist__44p3_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p6_0[] = {
	 1, 6, 6, 7, 7, 7, 7, 8, 8, 7, 9, 9,11,11,11, 9,
	 8, 8, 8, 9, 9,12,11,11, 9, 8, 8, 6, 7, 7,10,11,
	10,10,10,10,11,11,10,14,13,14,12,11,11,11,11,11,
	15,14,14,13,12,12, 5, 6, 6, 8, 5, 5, 8, 7, 7, 8,
	 8, 8,12,10,10, 9, 7, 7, 9, 7, 8,12,10,10,10, 7,
	 7, 7, 8, 8,12,10,10,12,10,10,11,10,10,15,13,13,
	13,10,10,11,10,10,16,13,14,14,10,10, 7, 7, 7,12,
	11,11,12,11,11,11,11,11,16,15,15,14,12,12,12,11,
	11,16,15,16,14,12,12,10, 9, 9,14,11,11,13,11,11,
	12,11,11,16,14,14,14,11,11,12,11,11,17,15,15,14,
	11,11, 7, 8, 8,12,11,11,12,10,10,12,10,10,16,14,
	13,14,10,10,12,10,10,17,14,14,14,10,10, 8, 7, 7,
	13,11,11,12,11,11,12,11,11,16,15,14,14,12,12,12,
	11,11,16,15,14,15,12,12,11,10,10,13,11,11,13,12,
	11,13,11,11,17,14,14,14,11,11,13,11,11,17,14,15,
	14,11,11,
};

static const static_codebook _44p3_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p3_p6_0,
	0
};

static const long _vq_quantlist__44p3_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p6_1[] = {
	 2, 6, 6, 7, 7, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9, 9,
	 7, 7, 8, 8, 8, 9, 9, 9, 9, 7, 8, 6, 7, 7, 8, 8,
	 8, 8, 8, 8, 9, 8, 8,10, 9, 9,10, 8, 8,10, 8, 8,
	10, 9, 9,10, 8, 8, 6, 6, 6, 8, 6, 6, 8, 7, 7, 8,
	 7, 7,10, 8, 8, 9, 7, 7, 9, 7, 7,10, 8, 9, 9, 7,
	 7, 7, 7, 7,10, 8, 8,11, 8, 8,10, 8, 8,12, 9, 9,
	12, 8, 8,11, 9, 9,12, 9, 9,11, 8, 8, 7, 7, 7,10,
	 9, 9,10, 9, 9,10, 9, 9,11,10,10,10, 9, 9,11, 9,
	 9,11,10,10,11, 9, 9, 9, 8, 8,10, 9, 9,10, 9, 9,
	11, 9, 9,11,10,10,11, 9, 9,11, 9, 9,11,10,10,11,
	 9, 9, 8, 8, 8,11, 9, 9,11, 9, 9,11, 9, 9,12, 9,
	 9,12, 8, 8,12, 9, 9,12, 9, 9,12, 8, 8, 8, 7, 7,
	10, 9, 9,10, 9, 9,11, 9, 9,11,11,11,11, 9, 9,11,
	10,10,11,11,11,11, 9, 9,10, 9, 9,11, 9, 9,11, 9,
	10,11,10, 9,11,10,10,11, 9, 9,11, 9,10,11,10,10,
	11, 9, 9,
};

static const static_codebook _44p3_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p3_p6_1,
	0
};

static const long _vq_quantlist__44p3_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p3_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p3_p7_0,
	0
};

static const long _vq_quantlist__44p3_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p3_p7_1[] = {
	 1, 9, 9, 6, 9, 9, 5, 9, 9, 8, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,
};

static const static_codebook _44p3_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p3_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p3_p7_1,
	0
};

static const long _vq_quantlist__44p3_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p3_p7_2[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p3_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p3_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p3_p7_2,
	0
};

static const long _vq_quantlist__44p3_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p3_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p3_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p3_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p3_p7_3,
	0
};

static const char _huff_lengthlist__44p3_short[] = {
	 4, 5,16, 9, 9,12,17,18, 4, 2,18, 6, 5, 9,13,15,
	10, 7, 7, 6, 7, 9,13,13, 8, 5, 6, 5, 5, 7,11,12,
	 8, 4, 7, 4, 3, 6,10,12,11, 8, 9, 7, 6, 8,11,12,
	15,13,13,11, 9, 7,10,12,16,12,16,12, 6, 5, 8,11,
};

static const static_codebook _huff_book__44p3_short = {
	2, 64,
	(char *)_huff_lengthlist__44p3_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p4_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p4_l0_0[] = {
	 1, 4, 4, 8, 8, 9, 8, 9, 9,10,10,10,10, 4, 6, 5,
	 8, 7, 9, 9, 9, 9,10, 9,10,10, 4, 5, 6, 7, 8, 9,
	 9, 9, 9, 9,10, 9,10, 8, 9, 8, 9, 8,10, 9,11, 9,
	12,10,11,10, 8, 8, 9, 8, 9, 9,10, 9,11,10,11,10,
	12, 9,10,10,11,10,11,11,12,11,12,12,12,12, 9,10,
	10,11,11,11,11,11,12,12,12,12,12,10,11,11,12,12,
	12,12,12,12,12,12,12,12,10,11,11,12,12,12,12,12,
	12,12,12,12,12,11,12,12,12,12,12,12,12,12,12,13,
	12,12,11,12,11,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,13,12,12,12,12,12,12,11,13,12,12,
	12,13,12,12,12,12,12,12,12,
};

static const static_codebook _44p4_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p4_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p4_l0_0,
	0
};

static const long _vq_quantlist__44p4_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p4_l0_1[] = {
	 3, 4, 4, 5, 5, 4, 4, 5, 5, 5, 4, 5, 4, 5, 5, 5,
	 5, 6, 5, 6, 5, 6, 5, 6, 5,
};

static const static_codebook _44p4_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p4_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p4_l0_1,
	0
};

static const long _vq_quantlist__44p4_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p4_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p4_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p4_l1_0,
	0
};

static const char _huff_lengthlist__44p4_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p4_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p4_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p4_long[] = {
	 3, 5,13, 9, 9,12,16,18, 4, 2,20, 6, 7,10,15,20,
	10, 7, 5, 5, 6, 8,10,13, 8, 5, 5, 3, 5, 7,10,11,
	 9, 7, 6, 5, 5, 7, 9, 9,11,10, 8, 7, 6, 6, 8, 8,
	15,15,10,10, 9, 7, 8, 9,17,19,13,12,10, 8, 9, 9,
};

static const static_codebook _huff_book__44p4_long = {
	2, 64,
	(char *)_huff_lengthlist__44p4_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p4_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p4_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p4_p1_0,
	0
};

static const long _vq_quantlist__44p4_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p4_p2_0[] = {
	 3, 9, 9, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0,
	12,12, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0, 0, 0, 0,
	 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0,
	 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,12,12, 0, 0,
	 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0,12,12, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0, 0, 0,
	 5, 5, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5,
	 0, 0, 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 0,
	 0, 0, 0, 0, 0, 0, 0, 5, 5, 0, 0, 0, 7, 7, 0, 0,
	 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,
	 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0, 9, 9, 0,
	 0, 0,10,10, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0,
	 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	10,10, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0,
	 0, 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,11,11, 0,
	 0, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 9, 9, 0, 0, 0, 7, 7, 0, 0, 0, 8, 8, 0, 0,
	 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0,
	 0, 0, 0, 0, 0, 7, 7, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,11,11, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5,
	 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 7, 7,
	 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0,
	 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0,
	 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0, 0,
	 0, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9,
	 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0, 0, 0, 7, 7, 0, 0, 0, 8, 8, 0,
	 0, 0,10,11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0,
	 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 7,
	 7, 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0,11,11,
	 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 7,
	 7, 0, 0, 0, 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 7, 8, 0,
	 0, 0, 9, 9, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0,
	 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0, 9, 9, 0, 0, 0,
	 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9,
	 9, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0,10,10, 0, 0, 0, 9, 9, 0, 0, 0,10,10,
	 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0,
	 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0,10,10, 0, 0,
	 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,
	 9, 9, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,11,
	11, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 0, 0, 0,12,12,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 7, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0, 0, 0,
	 9, 9, 0, 0, 0,10,10, 0, 0, 0,12,12, 0, 0, 0, 0,
	 0, 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9,
	 0, 0, 0,10,10, 0, 0, 0, 0, 0, 0, 0, 0,10,10, 0,
	 0, 0, 0, 0, 0, 0, 0, 8, 8, 0, 0, 0,10,10, 0, 0,
	 0, 0, 0, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0,
	10,10, 0, 0, 0,11,11, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0,
};

static const static_codebook _44p4_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p4_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p4_p2_0,
	0
};

static const long _vq_quantlist__44p4_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p3_0[] = {
	 1, 6, 6, 5, 7, 8, 0, 8, 8, 6, 9, 9, 7,10,10, 0,
	 8, 8, 0, 9, 9, 0,12,12, 0, 8, 8, 4, 7, 7, 6,10,
	10, 0,12,12, 7,11,11, 8,12,12, 0,12,12, 0,13,12,
	 0,15,15, 0,12,12, 0, 7, 7, 0, 7, 7, 0, 7, 7, 0,
	 8, 8, 0,10,10, 0, 7, 7, 0, 8, 8, 0,11,11, 0, 7,
	 7, 5, 7, 7, 8, 9, 9, 0,10,10, 8, 9, 9,11,11,11,
	 0,10, 9, 0,11,11, 0,13,13, 0,10,10, 6, 7, 7, 8,
	10,10, 0,12,12, 9,10,10,10,12,12, 0,12,12, 0,12,
	12, 0,15,15, 0,12,12, 0,10,10, 0,11,11, 0,11,11,
	 0,11,11, 0,13,13, 0,11,11, 0,11,11, 0,15,15, 0,
	10,10, 0, 8, 8, 0,10,10, 0,12,12, 0,11,11, 0,12,
	12, 0,12,12, 0,12,12, 0,15,15, 0,11,11, 0, 7, 7,
	 0,10,10, 0,12,12, 0,10,10, 0,12,12, 0,12,12, 0,
	13,13, 0,14,14, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44p4_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p4_p3_0,
	0
};

static const long _vq_quantlist__44p4_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p3_1[] = {
	 3, 5, 5, 0, 8, 8, 0, 8, 8, 0, 9, 9, 0,10,10, 0,
	 8, 8, 0, 8, 8, 0,10,10, 0, 8, 8, 0, 7, 7, 0, 8,
	 8, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0, 8, 8, 0, 8, 8,
	 0, 8, 8, 0, 8, 8, 0, 7, 7, 0, 6, 6, 0, 7, 7, 0,
	 7, 7, 0,10,10, 0, 6, 6, 0, 7, 7, 0,10,10, 0, 5,
	 5, 0, 8, 8, 0, 7, 7, 0, 8, 8, 0, 8, 8, 0, 9, 9,
	 0, 7, 7, 0, 8, 8, 0, 9, 9, 0, 7, 7, 0, 6, 6, 0,
	 9,10, 0,10,10, 0,10,10, 0,11,11, 0, 9, 9, 0,10,
	10, 0,11,11, 0, 9, 9, 0, 8, 8, 0, 8, 8, 0, 8, 8,
	 0, 9, 9, 0, 9, 9, 0, 7, 7, 0, 8, 8, 0, 9, 9, 0,
	 7, 7, 0, 8, 8, 0, 7, 7, 0, 7, 7, 0, 8, 8, 0, 9,
	 9, 0, 7, 7, 0, 7, 7, 0, 8, 8, 0, 6, 6, 0, 6, 6,
	 0,10,10, 0,10,10, 0,10,10, 0,12,12, 0, 9, 9, 0,
	10,10, 0,12,12, 0, 9, 9, 0, 8, 8, 0, 7, 7, 0, 7,
	 7, 0, 8, 8, 0, 9, 9, 0, 7, 7, 0, 8, 8, 0, 9, 9,
	 0, 6, 6,
};

static const static_codebook _44p4_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p4_p3_1,
	0
};

static const long _vq_quantlist__44p4_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p4_0[] = {
	 1, 6, 6, 6, 7, 7, 7, 8, 8, 7, 8, 8,10,11,11, 9,
	 8, 8, 8, 8, 8,11,11,12, 9, 8, 8, 5, 7, 7, 9,11,
	11,10,11,11,10,11,11,12,14,14,11,12,12,10,12,12,
	13,14,14,12,12,12, 5, 6, 6, 7, 6, 6, 8, 7, 7, 8,
	 7, 7,11,10,10,10, 7, 7, 9, 8, 8,12,11,11,10, 7,
	 7, 7, 7, 7,11,10,10,12,10,10,11,10,10,15,13,13,
	13,10,10,12,11,11,15,13,13,14,11,11, 7, 7, 7,11,
	11,11,12,11,11,12,11,11,14,14,14,13,12,12,12,12,
	12,16,15,15,14,12,12, 0,10,10, 0,11,11, 0,12,12,
	 0,11,11, 0,14,14, 0,11,11, 0,12,12, 0,15,15, 0,
	11,11, 7, 8, 8,12,11,10,12,10,10,12,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,14,10,10, 8, 7, 7,
	12,11,11,12,11,11,12,11,11,15,14,14,14,12,12,13,
	12,12,15,14,14,15,13,13, 0,11,11, 0,12,12, 0,12,
	12, 0,12,12, 0,15,15, 0,12,12, 0,13,13, 0,15,14,
	 0,12,12,
};

static const static_codebook _44p4_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p4_p4_0,
	0
};

static const long _vq_quantlist__44p4_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p4_p4_1[] = {
	 4, 5, 5, 9, 9,12, 9, 9,12,12,12,10,10,13,13,13,
	11,11,12,12,13,13,13,12,12,13,10,10,13,13,13,13,
	13,13,13,13,10,10,13,12,13,11,11,13,13,13,14,14,
	13,12,13,10,10,13,13,12,13,13,13,13,13,10,10,12,
	12,13,11,11,13,13,13,14,14,12,12,13,12,12,13,13,
	13,13,13,13,13,13,11,11,12,12,13,11,11,13,13,13,
	14,14,12,12,13,14,14,13,13,14,13,13,14,14,14,14,
	14,12,12,13,14,14,13,13,14,14,14,12,12,12, 8, 8,
	12,12,13,12,12,11,11,13,11,11,11,11,14,12,12,11,
	11,14,12,12,10,11,14,12,12,12,12,14,12,12,12,12,
	13,13,13,11,11,14,12,12,11,11,14,12,12,12,12,14,
	12,12,12,12,14,12,12,12,12,14,13,13,11,11,14,12,
	12,11,11,14,12,12,12,12,14,13,13,12,12,14,12,12,
	12,12,14,13,13,11,11,14,12,12,11,11,14,13,13,11,
	11,15,13,13,12,12,14,12,12,12,12,15,13,13,12,12,
	14,12,12,11,11,15,13,13,11,11,12, 9, 9,11,11,13,
	 7, 7,11,11,13, 8, 8,12,12,14,10,10,10,10,14,14,
	14,11,11,14, 8, 8,12,12,14,14,14,12,12,14, 7, 7,
	11,11,14, 9, 9,12,12,14,14,14,11,11,14, 8, 8,12,
	12,14,14,14,12,12,14, 7, 7,11,11,14, 9, 9,12,12,
	14,14,14,11,11,14,10,10,12,12,14,14,14,13,13,14,
	 9, 9,11,11,14,10,10,12,11,15,14,14,11,11,14,15,
	15,12,12,15,14,14,14,14,15,14,14,11,11,15,14,14,
	12,12,15,14,14,11,11,14,11,11,10,10,15,10,10,10,
	10,15,10,10,10,10,15,11,11, 9, 9,15,12,13, 9, 9,
	15,11,11,11,11,15,13,13,11,11,15,10,10,10,10,15,
	11,11,10,10,15,13,13,11,11,15,11,11,11,11,15,13,
	13,11,11,15,10,10,10,10,15,11,11,10,10,15,13,13,
	10,11,15,12,12,11,11,15,13,13,11,10,15,11,11,10,
	10,15,11,12,10, 9,15,13,13,10,10,15,14,14,11,11,
	15,13,13,11,11,15,14,14,10,10,15,13,13,10,10,15,
	14,14,10,10,14,13,13,10,10,15,13,13,10,10,15,13,
	13,10,10,14,14,14, 8, 9,15,14,14, 9, 9,15,14,14,
	11,11,15,14,14,10,10,15,14,14,10,10,15,14,14,11,
	11,15,14,14,10,10,15,14,14,11,11,15,14,14,10,10,
	15,14,14,10,10,15,14,14,10,10,15,14,14, 9, 9,15,
	14,14,11,11,15,14,14,11,11,15,14,14,10,10,15,14,
	14,10,10,14,14,14, 9, 9,15,15,15,11,11,15,14,14,
	12,12,15,15,15,10,10,15,14,15,10,10,15,15,15, 9,
	 9,15,10,10,13,13,17, 8, 8,12,12,17,10, 9,13,13,
	18,11,11,12,12,18,14,14,12,12,17, 9, 9,13,13,17,
	13,13,12,12,18, 8, 8,12,12,18,10,10,12,12,18,14,
	14,12,12,18,10,10,13,13,18,13,13,13,13,18, 9, 9,
	12,12,18,10,10,13,13,18,14,14,12,12,18,11,11,13,
	13,18,14,14,13,13,18,10,10,12,12,17,11,11,12,12,
	18,14,14,12,12,18,14,14,13,13,18,14,14,13,13,19,
	14,15,12,12,18,14,14,12,12,18,15,15,12,12,13, 7,
	 7,11,11,14,15,15,11,11,14,16,15,11,11,14,15,15,
	11,11,14,15,15,11,11,14,15,15,11,12,14,15,15,12,
	12,13,15,15,11,11,14,15,15,11,11,15,15,15,12,12,
	14,15,15,12,12,14,16,16,12,12,14,15,15,11,11,14,
	15,15,11,11,15,15,15,12,12,15,15,15,12,12,14,15,
	15,12,12,14,15,15,11,11,14,15,15,11,11,15,14,15,
	12,12,15,15,15,12,12,15,16,16,12,12,15,15,15,12,
	12,14,15,15,12,12,15,15,15,12,12,13,13,13,11,11,
	14,14,15,11,11,14,14,14,12,12,14,15,15,10,10,15,
	15,15,11,11,14,15,15,12,12,14,14,14,11,11,14,15,
	15,11,11,14,15,15,12,12,15,15,15,11,11,14,15,15,
	12,12,14,14,15,11,11,14,15,15,11,11,14,15,15,12,
	12,15,15,15,11,11,15,15,15,12,12,14,15,15,12,12,
	14,15,15,10,10,14,15,15,11,11,15,15,15,10,10,15,
	15,15,12,12,15,15,15,14,14,15,15,15,11,11,15,15,
	15,11,11,15,15,15,11,11,14,10,10,10,10,15, 9, 9,
	12,11,15,10,10,12,12,15,11,11,11,11,15,13,13,12,
	12,16,10,10,12,12,15,13,13,12,12,15, 9, 9,11,11,
	15,10,10,13,12,15,13,13,11,11,15,10,10,12,12,15,
	13,13,12,12,15, 9, 9,11,11,15,10,10,12,12,15,13,
	13,11,11,15,11,11,12,12,15,13,13,13,13,15,10,10,
	11,11,15,11,11,12,12,15,13,14,11,11,15,14,14,13,
	13,16,14,14,20,19,15,14,14,11,11,15,13,14,12,12,
	15,14,14,11,11,14,13,13,10,10,14,14,13,11,11,15,
	13,14,12,12,15,14,14,12,12,15,14,14,11,11,15,14,
	14,12,12,15,15,14,13,13,15,14,14,11,11,15,14,14,
	11,11,15,14,14,13,13,15,14,14,12,12,15,14,14,13,
	13,15,14,14,11,11,15,14,14,11,11,15,14,14,13,13,
	15,14,14,12,12,15,14,14,12,12,15,14,14,12,12,15,
	14,14,11,11,15,15,15,12,12,15,15,15,13,13,16,14,
	14,12,12,15,15,15,13,13,15,15,15,12,12,15,15,15,
	12,12,14,10,10,13,13,17, 9, 9,12,12,17, 9, 9,13,
	13,17,11,11,12,12,18,14,14,12,12,18,10,10,13,13,
	18,14,13,12,12,18, 9, 9,12,12,18,10,10,12,13,18,
	14,14,12,12,17, 9, 9,12,12,17,13,14,12,12,17, 9,
	 9,12,12,17,10,10,12,12,17,14,14,11,11,18,11,11,
	12,12,18,14,14,12,13,18,10,10,12,12,18,11,11,12,
	12,18,14,14,11,11,18,15,15,12,12,18,14,14,13,13,
	18,14,15,12,12,17,14,14,12,12,17,15,15,12,12,13,
	 7, 7,11,11,14,15,15,11,11,14,15,15,11,11,14,15,
	15,11,11,14,15,15,11,11,14,15,15,11,11,14,15,15,
	12,12,14,15,15,11,11,14,15,15,11,11,15,15,15,12,
	12,14,15,15,11,11,14,15,15,12,12,14,15,15,11,11,
	15,15,15,11,11,15,15,15,12,12,14,15,15,12,12,14,
	15,16,12,12,14,15,15,11,11,14,15,15,11,11,15,15,
	15,12,12,15,15,15,12,12,15,16,16,12,12,15,15,15,
	12,12,15,15,15,12,12,15,15,15,12,12,13,13,13,12,
	12,14,14,14,11,11,14,14,14,12,12,14,14,14,10,10,
	15,15,15,11,11,14,15,15,12,12,14,14,14,11,11,14,
	15,15,11,11,14,14,14,12,12,15,15,14,11,11,14,15,
	15,12,12,14,14,14,11,11,14,15,15,11,11,14,14,14,
	11,11,15,14,14,10,10,14,15,15,12,12,14,14,14,12,
	12,14,15,15,10,10,14,15,15,11,11,15,15,15,10,10,
	15,15,15,12,12,15,14,14,13,13,15,15,15,10,10,15,
	14,14,11,11,15,15,15,10,10,14,10,10,10,10,14, 9,
	 9,12,12,15,10,10,12,12,14,11,11,11,11,15,13,14,
	12,12,15,10,10,13,13,15,13,13,12,12,15, 9, 9,12,
	12,15,10,10,13,13,15,13,14,11,11,15,10,10,12,12,
	15,13,13,12,12,15, 9, 9,11,11,15,10,10,12,12,15,
	13,13,11,11,15,11,11,12,12,15,13,13,13,13,15,10,
	10,11,11,15,11,11,12,12,15,14,14,11,11,15,14,14,
	13,13,15,14,14,20,19,15,14,14,11,11,15,14,14,12,
	12,15,14,14,11,11,14,13,13,11,11,15,13,13,11,11,
	15,14,13,12,12,15,14,14,11,12,15,14,14,11,11,15,
	14,14,12,12,14,14,14,13,13,15,14,14,11,11,15,14,
	14,11,11,15,14,14,13,13,15,14,14,12,12,15,14,14,
	13,13,14,14,14,11,11,15,14,14,11,11,15,14,14,13,
	13,15,14,14,12,12,15,14,14,12,12,15,14,14,12,12,
	15,14,14,11,11,14,14,14,12,12,15,15,15,13,13,16,
	14,14,12,12,15,15,15,13,13,15,14,14,12,12,15,15,
	15,12,12,15,11,11,13,13,18,10,10,12,12,17,11,11,
	12,12,18,12,12,11,11,18,14,14,12,12,18,10,10,13,
	13,18,14,14,12,12,18,10,10,12,12,18,11,11,12,12,
	18,14,14,12,12,18,11,11,12,13,18,14,14,12,12,18,
	10,10,12,12,18,11,11,12,12,18,14,14,11,11,18,11,
	11,12,12,18,14,14,12,12,17,10,10,11,11,17,12,12,
	11,11,17,14,14,11,11,18,15,15,12,12,18,14,14,13,
	13,18,15,15,11,11,18,15,14,12,12,18,15,15,11,11,
	14, 8, 8,11,11,14,15,15,10,10,14,15,15,11,11,14,
	15,15,11,11,15,15,15,12,12,15,15,15,11,11,15,15,
	15,12,12,14,15,15,10,10,15,15,15,11,11,15,15,15,
	12,12,15,15,15,11,11,15,15,15,13,13,14,15,15,10,
	10,15,15,15,11,11,15,15,15,12,12,15,15,15,12,12,
	15,16,16,12,12,15,14,14,11,11,15,15,15,11,11,15,
	15,15,12,12,16,15,15,13,13,15,16,16,13,13,16,15,
	15,12,12,15,15,15,12,12,15,15,15,12,12,14,13,13,
	11,11,14,14,14,11,11,14,14,14,12,12,15,14,14,11,
	11,15,15,14,11,11,15,14,14,12,12,15,14,14,12,12,
	14,15,15,11,11,15,14,14,12,12,15,14,14,11,11,15,
	14,15,12,12,15,14,14,12,12,14,15,15,11,11,15,14,
	14,11,11,15,14,14,11,11,15,15,14,12,12,15,14,14,
	12,12,15,15,15,10,11,15,14,14,11,11,15,15,15,10,
	10,15,15,15,12,12,16,14,14,13,13,15,15,15,11,11,
	15,14,14,11,11,15,15,15,11,11,14,11,11, 9, 9,14,
	10,10,12,12,15,11,11,12,12,15,12,12,12,12,15,14,
	14,13,13,15,11,11,12,12,15,14,14,13,13,14,10,10,
	12,12,15,11,11,13,13,15,14,14,12,12,15,10,10,12,
	12,14,14,14,13,13,14,10,10,11,11,15,11,11,12,12,
	15,14,14,12,12,15,12,12,13,13,15,14,14,14,14,15,
	11,11,11,11,15,12,11,12,12,15,14,14,11,11,15,15,
	15,13,14,15,14,14,20,19,15,14,14,12,12,15,14,14,
	13,13,15,14,14,12,12,14,13,13,10,10,14,13,13,11,
	11,14,13,13,11,11,15,14,14,12,12,15,14,14,12,12,
	15,14,14,12,11,14,14,14,13,13,15,14,14,11,11,15,
	14,14,11,11,15,14,14,14,14,15,14,14,11,12,15,14,
	14,13,13,14,14,14,11,11,15,14,14,11,11,15,14,14,
	14,14,15,14,14,12,12,15,14,14,13,13,15,14,14,11,
	11,14,14,14,12,12,15,14,14,13,13,15,15,15,13,13,
	15,14,14,13,13,15,15,15,13,13,15,14,14,13,13,15,
	15,15,13,13,15,14,14,13,13,18,15,15,12,12,18,15,
	15,12,12,18,16,16,11,11,18,17,17,12,12,18,15,15,
	13,13,18,17,17,12,12,18,15,15,12,12,18,15,16,12,
	12,18,17,17,12,12,18,15,15,13,12,17,16,17,12,12,
	17,15,15,11,12,18,15,15,12,12,18,17,17,11,11,18,
	16,16,12,12,18,17,16,12,12,18,15,15,11,11,18,15,
	15,12,12,18,17,17,11,11,18,17,17,12,12,18,16,16,
	13,13,18,17,17,11,11,17,16,16,11,11,18,17,17,11,
	11,15,15,15,11,11,16,15,15,11,11,16,15,15,11,11,
	16,15,15,12,12,17,15,15,14,14,16,15,15,11,11,17,
	15,15,14,14,16,15,15,11,11,16,15,15,12,12,18,15,
	15,13,13,16,15,15,11,11,17,15,15,14,14,16,15,15,
	11,11,16,15,15,12,12,17,15,15,13,13,16,15,15,12,
	12,17,16,15,14,14,16,15,15,11,11,16,15,15,12,12,
	18,15,15,13,13,17,15,15,14,14,17,16,16,15,15,18,
	14,15,13,13,18,15,15,14,14,18,15,15,13,13,15,13,
	13,12,12,15,14,14,12,12,16,14,14,12,12,16,14,14,
	12,12,17,14,15,12,12,16,14,14,12,12,17,14,14,13,
	13,16,15,15,12,12,16,14,14,12,12,17,14,14,12,12,
	16,14,14,12,12,17,14,14,13,13,15,15,15,11,11,16,
	14,14,12,12,17,14,14,12,12,16,15,15,12,12,17,14,
	14,13,12,16,15,15,11,11,16,14,14,12,12,17,15,15,
	11,11,17,15,15,13,13,17,14,14,13,13,18,15,15,12,
	12,17,14,14,12,12,17,15,15,12,12,14,15,15, 9, 9,
	14,15,15,12,12,15,16,15,13,13,15,15,15,14,14,15,
	15,15,21,19,15,15,15,13,13,15,15,15,19,19,15,15,
	15,12,12,15,16,16,14,14,15,15,15,19,19,15,16,15,
	13,13,15,16,16,19,20,15,15,15,12,13,15,16,16,14,
	14,15,15,15,20,19,15,15,15,14,14,15,16,16,19,19,
	15,15,15,14,13,15,15,15,14,14,15,15,15,19,19,15,
	16,16,20,19,15,17,16,21,20,15,15,15,20,19,15,16,
	16,20,20,15,15,15,19,20,14,13,13,10,10,14,14,14,
	11,11,14,14,14,12,12,15,14,14,13,13,15,15,14,20,
	20,15,14,14,12,12,14,14,14,19,19,15,14,14,11,11,
	15,14,14,12,12,15,14,14,20,19,15,14,14,12,12,14,
	14,14,20,20,14,14,14,11,11,15,14,14,12,12,15,14,
	14,20,21,15,14,14,13,13,15,14,14,20,20,15,14,14,
	12,12,15,14,14,13,13,14,15,15,20,20,15,15,15,20,
	19,15,14,14,20,19,15,15,15,20,20,15,14,14,21,20,
	15,15,15,20,20,
};

static const static_codebook _44p4_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p4_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p4_p4_1,
	0
};

static const long _vq_quantlist__44p4_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p4_p5_0[] = {
	 1, 7, 6,15,15, 7, 8, 8,15,15, 8, 8, 8,15,15, 0,
	13,13,16,16, 0,14,14,16,16, 7, 9, 9,16,16,10,11,
	11,17,17,10, 8, 8,15,16, 0,14,14,18,18, 0,14,14,
	16,16, 9, 9, 9,16,16,12,11,11,17,17,10, 9, 9,15,
	15, 0,14,14,19,19, 0,14,14,16,16, 0,15,15,18,17,
	 0, 0, 0,20,20, 0,13,13,16,16, 0,17,17,22,20, 0,
	15,15,17,17, 0,15,15,18,18, 0,22,21,20,21, 0,13,
	13,16,16, 0,18,18, 0,22, 0,15,15,17,17, 6, 7, 7,
	13,13, 9,10,10,15,15,11,10,10,15,15, 0,21,22,18,
	18, 0, 0, 0,18,18,10,10,10,15,15,12,13,13,17,17,
	12,11,11,15,15, 0,22,22,18,18, 0, 0,21,18,18,12,
	11,11,15,15,15,14,14,18,18,13,11,11,15,15, 0, 0,
	21,18,19, 0,21,22,18,19, 0,22, 0,18,19, 0, 0, 0,
	 0, 0, 0,21,21,18,18, 0,22, 0, 0,21, 0, 0, 0,19,
	18, 0, 0, 0,18,19, 0, 0, 0, 0, 0, 0,20,20,18,17,
	 0, 0,22, 0,21, 0, 0, 0,19,19, 6, 6, 6,13,13, 8,
	 6, 6,11,11, 9, 7, 7,13,13, 0,10,10,11,11, 0,12,
	12,14,14, 9, 8, 8,14,14,12,10,10,13,13,10, 7, 7,
	13,13, 0,11,11,15,15, 0,11,11,13,13, 9, 8, 8,14,
	14,13,10,10,13,14,11, 7, 7,13,13, 0,11,11,15,15,
	 0,11,11,13,13, 0,12,12,15,15, 0,21,21,17,17, 0,
	10,10,13,13, 0,14,14,20,20, 0,12,12,13,13, 0,12,
	12,15,15, 0,21,22,17,18, 0,10,10,13,13, 0,16,16,
	20,21, 0,12,12,13,13, 0,11,11,13,13, 0,12,12,16,
	16, 0,12,12,16,16, 0,16,16, 0,21, 0,17,18, 0, 0,
	 0,12,12,15,15, 0,15,15,18,18, 0,12,12,16,16, 0,
	16,16,21,22, 0,17,17,22,21, 0,12,12,16,16, 0,15,
	15,19,19, 0,12,12,16,16, 0,16,16,22,22, 0,17,16,
	22, 0, 0,17,18, 0, 0, 0, 0, 0, 0, 0, 0,15,15,21,
	20, 0,19,20, 0,22, 0,18,18, 0, 0, 0,18,17, 0, 0,
	 0, 0, 0, 0, 0, 0,16,16,22,21, 0,20,20, 0,22, 0,
	20,19, 0, 0, 0,11,11,12,12, 0,10,10,11,11, 0,11,
	11,12,12, 0,12,12,10,10, 0,13,13,12,12, 0,11,11,
	13,13, 0,13,13,12,12, 0,10,10,12,12, 0,13,13,14,
	13, 0,12,12,12,12, 0,12,12,13,13, 0,14,14,13,13,
	 0,10,10,12,12, 0,13,13,14,14, 0,13,12,12,12, 0,
	14,14,14,14, 0,21,21,16,16, 0,12,12,12,12, 0,16,
	16,20,21, 0,13,13,11,11, 0,14,14,14,14, 0,20,20,
	16,15, 0,12,12,12,12, 0,17,17,20,20, 0,13,13,11,
	11, 7, 8, 8,16,16,11,10,10,15,15,12,10,10,17,17,
	 0,14,14,16,15, 0,15,15,17,17,11, 9, 9,16,16,14,
	12,12,17,17,13, 9, 9,16,15, 0,14,14,19,18, 0,14,
	14,16,16,12,10,10,17,18,16,13,13,17,18,14,10,10,
	16,16, 0,14,14,19,19, 0,14,15,17,17, 0,15,15,18,
	19, 0, 0, 0,20,20, 0,13,13,17,17, 0,17,18, 0,22,
	 0,15,15,16,17, 0,15,15,18,18, 0, 0, 0,20,21, 0,
	14,14,17,17, 0,19,18, 0, 0, 0,16,16,17,17, 8, 7,
	 7,14,14,12,11,11,15,15,13,11,11,15,15, 0, 0, 0,
	18,19, 0,21,20,18,18,12,10,11,15,16,14,13,13,18,
	18,14,11,11,15,15, 0,20,20,19,18, 0,20, 0,18,18,
	13,11,11,16,16,17,15,15,19,19,14,12,12,15,15, 0,
	21, 0,18,20, 0,22,22,18,19, 0,22,22,19,19, 0, 0,
	 0, 0, 0, 0,21,22,19,18, 0, 0, 0, 0,21, 0, 0, 0,
	19,19, 0, 0,22,20,20, 0, 0, 0, 0, 0, 0,22, 0,18,
	18, 0, 0, 0, 0,22, 0, 0, 0,19,20,11,10,10,14,14,
	14,11,11,13,13,14,11,11,15,15, 0,14,13,12,12, 0,
	15,15,16,16,13,11,11,15,15,16,13,13,15,15,15,10,
	10,14,15, 0,14,14,16,16, 0,14,14,15,15,13,11,11,
	15,15,18,14,14,15,15,15,10,10,15,14, 0,14,14,16,
	16, 0,14,14,15,15, 0,15,15,17,16, 0,21,22,18,18,
	 0,13,13,14,14, 0,18,17,20,21, 0,15,15,14,14, 0,
	15,16,16,17, 0, 0, 0,19,18, 0,13,13,15,14, 0,19,
	19, 0, 0, 0,15,15,14,14, 0,12,12,14,13, 0,13,13,
	16,16, 0,12,12,16,16, 0,16,16,22, 0, 0,17,18, 0,
	22, 0,13,13,16,16, 0,15,15,18,18, 0,12,12,16,16,
	 0,16,16,22,22, 0,17,17, 0, 0, 0,13,13,17,17, 0,
	16,16,19,20, 0,12,12,17,17, 0,17,17,22, 0, 0,17,
	17,22,21, 0,18,18, 0, 0, 0, 0, 0, 0, 0, 0,16,16,
	21,21, 0,19,19, 0, 0, 0,18,18, 0,22, 0,18,18, 0,
	22, 0, 0, 0, 0, 0, 0,16,16,22, 0, 0,20,20, 0, 0,
	 0,19,18, 0, 0, 0,12,12,15,15, 0,12,12,15,14, 0,
	13,13,15,15, 0,14,14,14,14, 0,15,15,16,16, 0,13,
	13,15,16, 0,15,15,16,16, 0,12,12,15,15, 0,14,14,
	16,16, 0,14,14,15,15, 0,13,13,15,16, 0,15,15,16,
	16, 0,12,12,15,15, 0,15,15,17,17, 0,14,14,15,15,
	 0,15,15,17,17, 0,21,21,19,19, 0,13,13,14,14, 0,
	17,17,22, 0, 0,14,14,15,15, 0,15,15,17,17, 0,22,
	 0,18,20, 0,13,13,15,15, 0,18,18, 0,22, 0,15,15,
	14,15, 8, 8, 8,17,16,12,10,10,16,16,13,10,10,17,
	16, 0,15,15,17,17, 0,15,15,17,17,12,11,11,18,18,
	15,12,12,18,18,15,10,10,16,17, 0,14,14,18,18, 0,
	14,14,17,17,13,10,10,16,16,17,14,14,17,17,15,10,
	10,16,15, 0,15,15,19,20, 0,14,14,15,16, 0,16,16,
	19,19, 0, 0, 0,21,22, 0,13,13,17,17, 0,18,17, 0,
	21, 0,15,15,17,17, 0,15,15,18,19, 0, 0,22, 0,21,
	 0,13,13,16,17, 0,19,19, 0,22, 0,16,15,16,16, 9,
	 8, 8,14,14,12,11,11,15,15,13,11,11,15,15, 0,21,
	20,19,18, 0, 0, 0,19,18,12,11,11,16,15,15,13,13,
	17,18,14,11,11,15,15, 0,22,22,19,18, 0,22,21,18,
	18,14,11,11,15,15,17,14,14,18,18,15,12,12,15,15,
	 0,22,22,20,19, 0, 0,21,18,18, 0, 0,22,20,20, 0,
	 0, 0, 0, 0, 0,20,21,18,18, 0, 0, 0,21,21, 0, 0,
	 0,20,19, 0,22,21,19,19, 0, 0, 0, 0, 0, 0, 0,22,
	17,18, 0, 0,22, 0,22, 0,22, 0,19,19, 0,11,11,15,
	15, 0,11,11,14,14, 0,12,12,15,15, 0,15,15,14,14,
	 0,16,16,16,16, 0,12,12,16,16, 0,14,14,16,16, 0,
	11,11,15,15, 0,15,15,17,17, 0,15,15,15,15, 0,12,
	12,16,16, 0,14,14,15,15, 0,11,11,15,15, 0,15,15,
	17,17, 0,15,15,14,15, 0,16,16,17,17, 0, 0, 0,19,
	19, 0,14,14,15,15, 0,18,18,21, 0, 0,15,15,14,15,
	 0,16,16,17,17, 0,21, 0,19,19, 0,14,14,15,15, 0,
	20,20,22, 0, 0,16,15,14,14, 0,12,12,13,13, 0,12,
	12,16,16, 0,12,12,16,16, 0,16,16,22,21, 0,18,17,
	21, 0, 0,13,13,16,16, 0,15,15,18,19, 0,12,12,16,
	16, 0,16,17,22, 0, 0,17,17, 0,22, 0,13,13,17,16,
	 0,15,15,19,19, 0,12,12,16,16, 0,16,16,21,20, 0,
	17,16,22, 0, 0,18,18,22,21, 0, 0, 0, 0, 0, 0,15,
	16,21,21, 0,19,19, 0, 0, 0,18,17, 0, 0, 0,18,18,
	21, 0, 0, 0, 0, 0, 0, 0,16,16,22,22, 0,20,21, 0,
	 0, 0,18,19, 0,22, 0,13,13,16,16, 0,12,12,15,15,
	 0,13,13,16,16, 0,14,14,15,15, 0,15,15,17,17, 0,
	13,13,17,16, 0,15,15,17,17, 0,12,12,16,16, 0,15,
	15,17,17, 0,14,14,16,16, 0,13,13,16,17, 0,15,15,
	17,17, 0,12,12,16,16, 0,14,14,17,17, 0,14,14,16,
	16, 0,16,16,17,17, 0,21, 0,21,19, 0,13,13,16,16,
	 0,17,17, 0, 0, 0,15,15,16,16, 0,16,15,18,18, 0,
	22, 0,20,20, 0,13,13,15,15, 0,18,18, 0, 0, 0,15,
	15,15,15, 0,12,12,17,17, 0,14,14,17,17, 0,14,14,
	17,17, 0,17,17,18,17, 0,17,17,19,18, 0,13,13,17,
	17, 0,16,16,18,18, 0,13,13,16,16, 0,17,17,19,19,
	 0,16,16,17,17, 0,13,13,18,18, 0,17,17,18,18, 0,
	13,13,17,17, 0,17,17,19,19, 0,16,17,17,17, 0,17,
	17,19,19, 0,21, 0,21,19, 0,14,14,16,16, 0,20,19,
	 0,21, 0,16,16,16,16, 0,17,18,19,19, 0, 0, 0, 0,
	21, 0,15,15,16,17, 0,21,20, 0, 0, 0,17,18,16,17,
	 0, 9, 9,14,14, 0,14,14,15,16, 0,14,14,15,15, 0,
	 0, 0,18,18, 0,21, 0,18,19, 0,12,12,15,15, 0,16,
	16,17,17, 0,14,14,14,14, 0,22, 0,19,18, 0,22, 0,
	17,18, 0,14,14,16,15, 0,18,18,19,18, 0,14,15,15,
	15, 0, 0,21,20,20, 0, 0, 0,18,18, 0,21,21,19,19,
	 0, 0, 0, 0, 0, 0,21,21,18,18, 0,22, 0,20,20, 0,
	22, 0,19,19, 0,22, 0,19,20, 0, 0, 0, 0, 0, 0, 0,
	21,17,18, 0, 0, 0,22,22, 0, 0, 0,19,18, 0,18,20,
	16,16, 0,21,20,17,17, 0, 0,21,18,18, 0,22,21,18,
	18, 0, 0,22,19,19, 0,20,20,17,17, 0, 0, 0,18,18,
	 0,19,20,17,17, 0,22, 0,19,21, 0,22,21,18,18, 0,
	20,19,17,18, 0, 0, 0,19,19, 0,20,20,17,17, 0,22,
	22,21,21, 0,20, 0,18,18, 0,22,22,18,18, 0, 0, 0,
	20,22, 0,20,20,16,16, 0, 0, 0,21, 0, 0,21,20,16,
	17, 0,22, 0,19,20, 0, 0, 0,21,20, 0,19,21,17,17,
	 0, 0, 0, 0, 0, 0,21,21,17,17, 0,12,12,13,13, 0,
	14,14,16,16, 0,14,14,16,16, 0,18,18, 0, 0, 0,19,
	18,22, 0, 0,13,13,16,16, 0,16,16,18,18, 0,13,13,
	16,16, 0,17,18,21, 0, 0,18,18,21, 0, 0,13,13,16,
	16, 0,17,17,19,20, 0,13,13,16,17, 0,18,18,21, 0,
	 0,18,18,21, 0, 0,18,19, 0,21, 0, 0, 0, 0, 0, 0,
	16,16,21,20, 0,20,20, 0, 0, 0,18,19, 0, 0, 0,18,
	18, 0, 0, 0, 0, 0, 0, 0, 0,16,16, 0,21, 0,22,22,
	 0, 0, 0,19,19, 0, 0, 0,16,16,19,20, 0,17,16,22,
	21, 0,17,17,21,20, 0,19,18, 0,22, 0,19,19,22,22,
	 0,16,15,22,22, 0,19,19, 0,21, 0,15,15,20,20, 0,
	18,19, 0,21, 0,18,18,22,22, 0,16,16,21,20, 0,20,
	19,21,22, 0,16,15,20,20, 0,19,19, 0,22, 0,18,18,
	21, 0, 0,19,18,21,22, 0, 0, 0, 0, 0, 0,16,16,19,
	21, 0,20,22, 0,22, 0,18,18,20,21, 0,19,18, 0,22,
	 0, 0, 0,22, 0, 0,16,16,20,20, 0,21,21, 0, 0, 0,
	18,18,21, 0, 0,12,12,17,17, 0,15,14,17,17, 0,14,
	14,18,18, 0,17,17,17,18, 0,18,18,18,18, 0,13,13,
	18,18, 0,16,17,19,18, 0,13,13,16,17, 0,17,17,18,
	19, 0,17,17,17,17, 0,13,13,17,17, 0,17,18,18,18,
	 0,13,13,16,16, 0,18,18,19,20, 0,16,17,17,16, 0,
	17,18,19,18, 0, 0, 0,22,21, 0,15,15,16,16, 0,20,
	20,21,22, 0,17,17,16,16, 0,16,17,18,18, 0, 0, 0,
	21,21, 0,15,15,16,16, 0,21,20, 0, 0, 0,17,17,16,
	16, 0,10,10,14,14, 0,14,14,15,15, 0,14,14,15,15,
	 0,22, 0,18,18, 0, 0, 0,19,19, 0,13,13,15,16, 0,
	17,16,18,18, 0,14,14,15,15, 0,21,21,19,18, 0,22,
	21,18,17, 0,14,14,15,15, 0,18,18,19,18, 0,15,15,
	14,14, 0,22,21,19,19, 0,22,21,17,18, 0, 0, 0,19,
	19, 0, 0, 0, 0, 0, 0,20,22,17,17, 0, 0,22,22,20,
	 0, 0, 0,19,18, 0,21,22,19,18, 0, 0, 0, 0, 0, 0,
	22,22,17,18, 0, 0, 0,21,22, 0, 0, 0,19,18, 0,20,
	20,17,17, 0,21,21,17,18, 0,21,22,18,18, 0,21, 0,
	18,18, 0,22, 0,19,19, 0,19,21,18,18, 0, 0,22,18,
	18, 0,22,21,17,17, 0,22, 0,20,20, 0, 0, 0,18,18,
	 0,22,21,18,18, 0,21, 0,19,19, 0,20,21,17,17, 0,
	 0,22,22,20, 0,21,22,17,17, 0, 0,21,19,18, 0, 0,
	 0,21,21, 0,21,20,16,17, 0, 0, 0, 0, 0, 0,21, 0,
	17,17, 0,21, 0,19,20, 0, 0, 0,20,22, 0,20,20,17,
	17, 0, 0, 0, 0, 0, 0,21,21,17,17, 0,12,12,13,13,
	 0,14,14,16,16, 0,14,14,16,16, 0,18,18,21, 0, 0,
	19,19,22, 0, 0,13,13,16,16, 0,16,16,18,18, 0,13,
	13,16,16, 0,18,18,21,22, 0,18,18, 0,22, 0,13,13,
	16,16, 0,17,17,20,18, 0,13,13,16,16, 0,19,18, 0,
	22, 0,18,18,22,21, 0,18,19, 0, 0, 0, 0, 0, 0, 0,
	 0,16,16,21,21, 0,21,21, 0, 0, 0,18,19, 0, 0, 0,
	19,19,21, 0, 0, 0, 0, 0, 0, 0,16,16, 0,21, 0,20,
	20, 0, 0, 0,20,20, 0, 0, 0,16,16,21,20, 0,18,17,
	21,22, 0,17,18, 0,21, 0,18,19,22,22, 0,19,19, 0,
	22, 0,16,17,21,22, 0,20,19, 0, 0, 0,16,16,20,21,
	 0,19,19, 0, 0, 0,19,19, 0,22, 0,17,17,21,21, 0,
	19,20, 0, 0, 0,16,16, 0,20, 0,19,20, 0,21, 0,18,
	18, 0,22, 0,19,20,22,22, 0, 0, 0, 0,22, 0,17,17,
	 0,21, 0,21,21, 0, 0, 0,18,19,23,21, 0,20,19, 0,
	 0, 0, 0, 0, 0, 0, 0,17,17, 0,20, 0, 0, 0, 0, 0,
	 0,19,19,23,22,
};

static const static_codebook _44p4_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p4_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p4_p5_0,
	0
};

static const long _vq_quantlist__44p4_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p4_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p4_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p4_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p4_p5_1,
	0
};

static const long _vq_quantlist__44p4_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p6_0[] = {
	 1, 7, 7, 7, 8, 8, 7, 8, 8, 7, 9, 9,11,11,11, 9,
	 8, 8, 8, 9, 9,12,11,12, 9, 8, 8, 6, 7, 7,10,11,
	11,10,10,10,11,11,11,14,14,14,12,11,12,11,11,11,
	15,15,14,13,12,12, 5, 6, 6, 8, 5, 5, 8, 7, 7, 8,
	 7, 7,12,10,10,10, 7, 6, 9, 8, 8,12,10,10,10, 6,
	 6, 7, 8, 8,12,10,10,12,10,10,11,10,10,16,14,14,
	13,10,10,12,10,10,15,14,14,14,10,10, 7, 7, 7,13,
	11,11,13,11,11,12,11,11,16,14,14,14,12,12,12,11,
	11,18,15,15,14,12,12,10, 9,10,14,11,11,13,11,11,
	12,11,11,17,14,14,14,11,11,13,11,11,16,15,15,14,
	11,11, 7, 8, 8,13,11,11,12,10,10,12,10,10,16,14,
	13,13,10,10,12,10,10,17,14,14,14,10,10, 8, 7, 7,
	12,11,11,13,11,11,12,11,11,16,15,14,14,12,12,12,
	11,11,16,15,15,14,12,12,11,10,10,14,11,11,13,11,
	11,13,11,11,17,14,14,14,11,11,13,11,11,18,14,15,
	15,11,10,
};

static const static_codebook _44p4_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p4_p6_0,
	0
};

static const long _vq_quantlist__44p4_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p6_1[] = {
	 2, 6, 6, 6, 7, 7, 7, 7, 7, 7, 8, 8, 9, 9, 9, 9,
	 7, 7, 8, 8, 8, 9, 9, 9, 9, 8, 8, 6, 7, 7, 8, 8,
	 8, 8, 8, 8, 9, 8, 8, 9, 8, 9, 9, 8, 8,10, 8, 8,
	10, 9, 9,10, 8, 8, 6, 6, 6, 8, 6, 6, 8, 7, 7, 8,
	 7, 7,10, 8, 8, 9, 7, 7, 9, 7, 7,10, 8, 8, 9, 7,
	 7, 7, 7, 7,10, 8, 8,11, 9, 9,10, 9, 9,11, 9, 9,
	11, 8, 8,11, 9, 9,12, 9, 9,12, 8, 8, 7, 7, 7,10,
	 9, 9,10, 9, 9,10, 9, 9,11,10,10,10, 9, 9,11, 9,
	10,11,10,11,10, 9, 9, 9, 8, 8,10, 9, 9,10, 9, 9,
	11, 9, 9,11,10,10,11, 9, 9,11, 9, 9,11,10,10,11,
	 9, 9, 8, 8, 8,11, 9, 9,11, 9, 9,11, 9, 9,12, 9,
	 9,12, 8, 8,11, 9, 9,12, 9, 9,12, 8, 8, 8, 7, 7,
	10, 9, 9,10, 9, 9,10, 9, 9,11,11,11,11, 9, 9,11,
	10,10,11,11,11,11, 9, 9,10, 9, 9,11, 9, 9,11, 9,
	10,11,10,10,11,10,10,11, 9, 9,11,10,10,11,10,10,
	11, 9, 9,
};

static const static_codebook _44p4_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p4_p6_1,
	0
};

static const long _vq_quantlist__44p4_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p4_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p4_p7_0,
	0
};

static const long _vq_quantlist__44p4_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p4_p7_1[] = {
	 1, 9, 9, 7, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8,
	 9, 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 7, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 6, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 5, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 5,10, 9,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10, 8,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,
};

static const static_codebook _44p4_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p4_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p4_p7_1,
	0
};

static const long _vq_quantlist__44p4_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p4_p7_2[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p4_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p4_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p4_p7_2,
	0
};

static const long _vq_quantlist__44p4_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p4_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p4_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p4_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p4_p7_3,
	0
};

static const char _huff_lengthlist__44p4_short[] = {
	 3, 5,16, 9, 9,13,18,21, 4, 2,21, 6, 6,10,15,21,
	16,19, 6, 5, 7,10,13,16, 8, 6, 5, 4, 4, 8,13,16,
	 8, 5, 6, 4, 4, 7,12,15,13,10, 9, 7, 7, 9,13,16,
	18,15,13,12, 9, 7,10,14,21,18,13,13, 7, 5, 8,12,
};

static const static_codebook _huff_book__44p4_short = {
	2, 64,
	(char *)_huff_lengthlist__44p4_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p5_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p5_l0_0[] = {
	 1, 4, 4, 8, 8,10,10,10,10, 9, 8,11,11, 4, 6, 5,
	 8, 6,10,10,10,10,10, 9,10, 9, 4, 5, 6, 6, 9,10,
	10,10,10, 9,10, 9,10, 8, 9, 8, 9, 8, 9, 9,10, 9,
	11,10,12,10, 8, 8, 9, 8, 9, 9, 9, 9,10,10,11,10,
	12, 9,10,10,11,10,11,10,12,11,12,11,13,11, 9,10,
	10,10,11,10,11,11,12,11,12,11,12,11,12,12,12,12,
	13,12,13,12,13,12,13,13,11,12,12,12,12,12,12,12,
	13,13,13,13,13,12,12,12,13,13,13,13,13,13,13,13,
	13,13,12,13,12,13,13,13,13,13,13,13,13,13,13,12,
	13,13,13,14,14,13,13,13,13,13,13,13,12,13,12,13,
	13,13,13,13,13,13,13,13,13,
};

static const static_codebook _44p5_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p5_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p5_l0_0,
	0
};

static const long _vq_quantlist__44p5_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p5_l0_1[] = {
	 4, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 4, 4, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p5_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p5_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p5_l0_1,
	0
};

static const long _vq_quantlist__44p5_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44p5_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p5_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p5_l1_0,
	0
};

static const char _huff_lengthlist__44p5_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44p5_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p5_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p5_long[] = {
	 3, 7,12,14,14,16,18,19, 6, 2, 4, 6, 8, 9,12,14,
	12, 3, 3, 5, 7, 8,11,13,13, 6, 4, 5, 7, 8,10,11,
	14, 8, 7, 7, 7, 7, 9,10,15, 9, 8, 7, 7, 6, 8, 9,
	17,11,11,10, 9, 8, 9, 9,19,14,13,11,10, 9, 9, 9,
};

static const static_codebook _huff_book__44p5_long = {
	2, 64,
	(char *)_huff_lengthlist__44p5_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p5_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p1_0[] = {
	 2, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 5, 7, 8, 8, 9,
	10, 8, 9,10, 8, 9,10, 9,10,12,10,11,11, 8,10,10,
	10,11,11, 9,11,11, 5, 8, 7, 8, 9, 9, 8,10, 9, 8,
	10,10, 9,11,11,10,11,11, 8,10, 9,10,11,11, 9,12,
	10, 5, 8, 8, 7, 9,10, 8,10, 9, 7, 9, 9, 9,10,11,
	 9,11,11, 8,10, 9,10,11,11,10,11,11, 7, 9, 9, 9,
	10,11, 9,11,11, 9, 9,11,10,10,13,11,11,12, 9,11,
	11,11,12,13,11,13,12, 7, 9, 9, 9,11,11, 9,11,10,
	 9,11,10,10,11,12,11,13,12, 9,11,11,11,12,13,11,
	13,11, 5, 8, 8, 8, 9,10, 7,10, 9, 8, 9,10,10,11,
	11,10,11,11, 7, 9, 9, 9,11,11, 9,11,10, 7, 9, 9,
	 9,10,11, 9,11,11, 9,11,11,11,11,13,11,13,12, 9,
	10,11,11,12,13,10,12,11, 7, 9, 9, 9,11,11, 9,11,
	10, 9,11,11,11,12,13,11,13,12, 9,11, 9,11,12,11,
	10,13,10,
};

static const static_codebook _44p5_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p5_p1_0,
	0
};

static const long _vq_quantlist__44p5_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p5_p2_0[] = {
	 4, 6, 6, 9, 9, 6, 7, 8,10,10, 6, 8, 7,10,10, 8,
	10,10,12,13, 8,10,10,13,12, 6, 7, 8,10,10, 7, 8,
	 9,10,11, 8, 9, 9,11,11,10,10,11,12,14,10,11,11,
	14,13, 6, 8, 7,10,10, 8, 9, 9,11,11, 7, 9, 8,11,
	10,10,11,11,13,14,10,11,10,14,12, 9,10,10,12,12,
	10,10,11,12,13,10,11,11,13,13,12,12,13,12,15,13,
	14,13,15,14, 9,10,10,12,12,10,11,11,13,13,10,11,
	10,13,12,13,13,14,14,15,12,13,12,15,12, 6, 7, 8,
	10,11, 8, 9,10,11,12, 8, 9, 9,11,12,10,11,12,13,
	14,10,11,11,14,13, 8, 9,10,11,12, 9,10,11,12,13,
	 9,10,11,12,13,11,12,13,13,15,12,12,13,15,14, 8,
	 9, 9,12,12, 9,10,11,12,13, 9,10,10,13,12,12,12,
	13,14,15,11,12,12,14,14,11,11,12,13,14,11,12,13,
	13,15,12,13,13,14,15,14,13,15,14,16,14,15,15,16,
	16,11,12,11,14,13,12,13,13,15,14,11,13,12,14,13,
	14,15,15,15,16,13,14,14,16,14, 6, 8, 7,11,10, 8,
	 9, 9,11,12, 8,10, 9,12,11,10,11,11,13,14,10,12,
	11,14,13, 8, 9, 9,12,12, 9,10,10,12,13, 9,11,10,
	13,12,11,12,12,13,14,12,13,12,15,14, 8,10, 9,12,
	11, 9,11,10,13,12, 9,11,10,13,12,12,13,12,14,15,
	11,13,12,15,13,11,11,12,13,14,11,12,13,13,15,12,
	13,13,14,15,13,14,14,14,16,14,15,15,16,16,11,12,
	11,14,13,12,13,13,15,14,11,13,12,15,13,14,15,15,
	16,16,13,15,13,16,14, 9,10,11,12,14,11,11,12,13,
	15,11,12,12,13,14,13,14,15,15,17,13,14,14,15,16,
	11,11,12,13,15,12,12,13,14,16,12,13,13,14,15,14,
	14,16,15,17,15,15,15,16,17,11,12,12,14,14,12,13,
	13,15,16,12,13,13,15,15,15,15,15,16,17,14,15,15,
	16,16,14,14,15,15,17,14,15,15,15,17,15,15,16,16,
	17,16,16,17,16,18,17,17,17,18,18,14,15,14,16,16,
	15,15,16,17,17,14,15,15,17,16,17,17,17,18,18,16,
	16,16,17,17, 9,11,10,14,12,11,12,12,14,13,11,12,
	11,15,13,13,14,14,16,15,13,15,14,17,15,11,12,12,
	15,14,12,13,13,15,15,12,13,13,15,15,14,15,15,16,
	16,15,15,15,17,16,11,12,11,15,13,12,13,13,15,14,
	12,13,12,16,14,15,15,15,17,16,14,15,14,17,15,14,
	14,15,16,16,14,15,15,16,16,15,16,15,17,17,16,16,
	16,17,17,17,17,17,18,17,14,15,14,16,15,15,15,15,
	17,16,15,15,15,17,15,17,17,17,18,18,16,17,16,18,
	16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,11,
	10,11,11,13,14,10,12,11,14,13, 7, 9, 9,11,12, 9,
	10,10,12,13, 9,10,10,13,13,11,11,12,13,15,11,12,
	12,15,14, 8, 9, 9,12,11, 9,11,10,13,13, 9,11,10,
	13,12,12,13,12,14,15,11,13,12,15,13,10,11,12,13,
	14,11,12,12,13,15,12,12,13,14,15,13,13,14,14,16,
	14,15,15,16,16,11,12,11,14,13,12,13,13,15,14,11,
	13,12,15,13,14,15,15,15,16,13,14,14,16,14, 7, 9,
	 9,11,12, 9,10,11,12,13, 9,10,10,13,12,11,12,12,
	14,15,11,12,12,15,14, 9, 9,11,11,13,10,10,12,12,
	14,10,11,12,13,14,12,12,13,14,16,12,13,13,15,15,
	 9,11,10,13,13,10,12,12,13,14,10,12,11,14,13,12,
	13,13,15,16,12,13,13,15,14,11,11,13,13,15,12,12,
	14,13,16,13,13,13,14,15,14,14,15,14,17,15,15,15,
	16,16,12,13,12,15,14,13,14,14,15,15,12,14,13,16,
	14,15,15,16,16,17,14,15,14,17,15, 7, 9, 9,12,11,
	 9,10,10,12,13, 9,11,10,13,12,11,12,12,14,14,11,
	13,12,15,14, 9,10,10,13,12,10,10,11,12,13,10,12,
	11,14,13,12,12,13,13,15,12,14,13,16,15, 9,10,10,
	13,12,11,11,12,13,13,10,12,10,14,12,13,13,13,15,
	15,12,13,12,15,13,11,12,12,14,14,12,12,13,14,15,
	13,14,13,15,15,14,13,15,13,16,15,16,15,17,16,12,
	13,12,14,14,13,14,14,15,15,12,13,12,15,14,15,15,
	16,16,17,14,15,13,16,13,10,11,12,13,14,11,12,13,
	14,15,12,13,13,15,15,14,14,15,15,17,14,15,15,16,
	16,12,12,13,12,15,12,12,14,13,16,13,13,14,14,16,
	14,14,16,15,17,15,15,16,16,17,12,13,13,15,15,13,
	14,14,16,16,13,14,13,16,15,15,16,16,17,17,14,15,
	15,17,16,14,14,15,14,17,15,15,16,15,17,15,15,16,
	15,17,16,16,17,16,18,17,17,17,17,18,14,15,15,17,
	16,15,16,16,17,17,15,16,15,17,16,17,17,17,18,18,
	16,17,16,18,17,10,12,11,14,14,12,13,13,15,15,12,
	13,12,15,14,14,15,15,16,16,14,15,15,17,16,11,13,
	12,15,14,12,13,13,15,15,13,14,13,16,14,15,15,15,
	16,16,15,16,15,17,16,12,13,13,15,15,13,14,14,16,
	16,12,14,13,16,15,15,16,16,17,17,15,16,15,17,16,
	14,15,15,16,16,14,15,15,16,16,15,16,16,17,16,16,
	16,16,16,17,17,18,17,18,17,14,15,15,17,16,15,16,
	16,17,17,15,16,15,17,16,17,17,18,18,18,16,17,16,
	18,16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,
	11,10,11,12,13,14,10,11,11,14,13, 8, 9, 9,11,12,
	 9,10,11,12,13, 9,10,11,13,13,11,12,13,13,15,12,
	12,12,15,14, 7, 9, 9,12,11, 9,10,10,13,13, 9,10,
	10,13,12,11,12,12,14,15,11,12,11,15,13,11,11,12,
	13,14,11,12,13,13,15,12,13,13,14,15,13,14,14,14,
	16,14,15,15,16,16,10,12,11,14,13,12,13,12,14,14,
	11,12,12,15,13,14,15,15,16,16,13,14,13,16,14, 7,
	 9, 9,11,12, 9,10,11,12,13, 9,10,10,13,12,11,12,
	13,14,15,11,12,12,14,14, 9,10,10,12,13,10,10,12,
	12,14,11,12,11,13,13,12,12,14,13,15,13,13,13,15,
	15, 9,10,10,12,13,10,11,12,13,14,10,11,10,13,12,
	13,13,14,15,16,12,13,12,15,13,12,13,13,14,14,12,
	12,13,14,15,13,14,14,15,15,14,13,15,13,16,15,16,
	15,17,16,11,12,12,14,14,13,13,14,15,15,12,13,12,
	15,14,15,15,16,16,17,14,14,13,16,13, 7, 9, 9,12,
	11, 9,10,10,12,13, 9,11,10,13,12,11,12,12,14,15,
	11,12,12,15,14, 9,10,11,13,13,10,11,12,13,14,10,
	12,12,14,13,12,13,13,14,16,12,13,13,16,15, 9,11,
	 9,13,11,10,12,11,13,13,10,12,10,14,12,12,13,13,
	15,15,12,13,12,16,14,12,12,13,14,15,12,13,14,14,
	15,13,14,14,15,15,14,14,15,15,17,15,16,15,17,16,
	11,13,11,15,13,13,14,13,15,14,12,14,12,16,13,15,
	15,15,16,16,14,15,14,17,14,10,11,12,14,14,12,12,
	13,14,15,12,13,13,15,15,14,15,15,16,17,14,15,15,
	16,16,12,12,13,15,15,13,13,14,15,16,13,14,14,16,
	16,15,15,16,16,17,15,16,16,17,17,11,12,13,14,15,
	13,13,14,15,16,12,13,13,15,15,15,15,16,16,17,15,
	15,15,16,16,14,15,15,16,17,15,15,16,16,17,15,16,
	16,17,17,16,16,17,16,18,17,17,17,18,18,14,15,15,
	16,16,15,16,16,16,17,15,15,15,16,16,17,17,17,18,
	18,16,16,16,17,16,10,12,11,14,13,12,13,13,15,15,
	11,13,12,15,14,14,15,15,16,16,14,15,14,17,15,12,
	13,13,15,15,13,13,14,16,16,13,14,14,16,16,15,15,
	15,16,17,15,16,16,17,17,12,13,12,15,12,13,14,13,
	16,14,12,14,12,16,13,15,16,15,17,16,14,16,14,17,
	15,14,15,15,16,17,15,15,16,17,17,15,16,16,17,17,
	16,16,17,17,18,17,18,17,18,18,14,15,14,17,14,15,
	16,15,17,15,15,16,15,17,15,17,17,17,18,17,16,17,
	16,18,16, 9,11,11,14,14,11,12,12,14,14,11,12,12,
	15,14,13,14,14,16,16,13,15,14,16,16,10,11,12,14,
	14,11,12,13,15,15,12,13,13,15,15,13,14,15,16,17,
	14,15,15,17,16,11,12,12,15,14,12,13,13,15,15,12,
	13,13,15,15,14,15,15,16,16,14,15,15,17,16,12,13,
	14,15,16,13,14,14,15,16,13,14,15,16,16,15,15,16,
	16,18,16,16,16,18,17,14,14,14,16,15,15,15,15,17,
	16,14,15,15,17,16,16,17,17,18,17,16,16,16,18,16,
	10,12,12,14,14,11,12,13,15,15,12,13,13,15,15,13,
	14,15,16,17,14,15,15,17,16,11,12,13,14,15,12,12,
	14,15,16,13,13,14,15,16,14,14,15,16,17,15,15,16,
	17,17,12,13,13,15,15,13,14,14,16,16,13,14,13,16,
	15,15,16,15,17,17,15,16,15,17,16,13,13,15,14,17,
	14,13,16,15,17,15,14,16,15,17,15,15,17,16,18,16,
	16,17,17,18,14,15,15,17,16,15,16,16,17,17,15,16,
	15,17,16,17,17,17,18,18,16,17,16,18,17,10,12,11,
	14,14,11,12,13,15,15,12,13,12,15,15,14,15,15,16,
	16,14,15,15,17,16,11,12,12,15,15,12,13,13,15,15,
	13,14,13,16,15,14,15,15,16,16,15,16,15,17,16,11,
	13,13,15,15,13,14,14,15,15,12,14,13,16,15,15,16,
	15,17,17,15,16,15,17,16,13,15,14,16,16,14,15,14,
	16,16,15,16,15,17,16,15,16,16,16,17,16,17,16,18,
	17,14,15,15,16,16,15,16,16,17,17,15,15,15,17,16,
	17,17,17,18,18,16,16,16,18,16,12,13,13,15,16,13,
	14,14,15,16,13,14,14,16,16,15,15,16,16,18,15,16,
	16,17,17,13,13,14,15,16,14,14,15,15,17,14,15,15,
	16,17,15,15,17,16,18,16,16,17,17,17,13,14,14,16,
	16,14,15,15,17,17,14,15,14,17,16,16,17,16,17,18,
	16,17,16,18,17,15,15,16,14,17,16,15,17,14,18,16,
	16,16,15,18,16,16,18,15,19,18,18,18,17,19,15,16,
	16,18,17,16,17,17,18,17,16,17,16,18,17,18,18,18,
	19,19,17,18,16,18,17,11,12,12,15,15,13,13,14,15,
	16,13,14,13,16,15,15,16,16,16,17,15,16,16,17,16,
	12,14,13,16,15,13,13,14,15,16,14,15,14,17,15,15,
	15,16,16,17,16,17,16,18,17,12,13,14,15,16,14,15,
	15,16,16,13,14,13,16,15,16,16,16,17,17,15,16,15,
	17,15,15,16,15,17,16,15,15,15,16,16,16,17,16,18,
	16,16,15,16,15,17,17,18,17,18,17,15,15,16,17,17,
	16,16,17,17,17,15,16,15,17,16,18,18,18,18,18,16,
	17,16,18,15, 9,11,11,14,14,11,12,12,14,15,10,12,
	12,15,14,13,14,15,16,16,13,14,14,16,16,11,12,12,
	14,15,12,12,13,15,15,12,13,13,15,15,14,15,15,16,
	17,14,15,15,16,16,10,12,12,14,14,12,13,13,15,15,
	11,13,12,15,15,14,15,15,16,17,13,15,14,16,16,14,
	14,14,15,16,14,15,15,16,17,14,15,15,16,17,16,16,
	17,16,18,16,17,17,17,17,12,14,13,16,15,13,15,14,
	16,16,13,14,14,16,15,16,16,16,17,17,15,16,15,17,
	16,10,11,11,14,14,12,12,13,14,15,11,13,12,15,14,
	14,15,15,16,17,14,15,15,16,16,12,13,13,15,15,12,
	13,14,15,16,13,14,14,15,15,15,15,16,16,17,15,15,
	16,17,17,11,12,12,15,15,13,13,14,15,16,12,13,13,
	15,15,15,15,16,16,17,14,15,15,16,16,14,15,15,16,
	16,15,15,15,16,17,15,16,16,17,17,16,16,17,16,18,
	17,17,17,17,18,13,14,15,16,16,15,15,16,16,17,14,
	14,14,16,16,16,16,17,17,18,16,16,16,17,16,10,12,
	12,14,14,12,13,13,15,15,11,13,12,15,15,14,15,15,
	16,17,13,15,14,17,16,12,13,13,15,15,13,13,14,15,
	16,13,14,14,16,16,15,15,16,16,17,15,15,16,17,17,
	11,13,12,15,14,13,14,13,16,15,12,14,12,16,15,15,
	16,15,17,17,14,15,14,17,16,14,15,15,16,17,15,15,
	16,16,17,15,16,16,17,17,16,16,17,17,18,17,17,17,
	18,18,13,15,13,17,14,14,16,14,17,16,14,15,13,17,
	15,16,17,16,18,17,15,17,15,18,16,11,12,12,15,15,
	13,13,14,15,16,13,14,13,16,15,15,16,16,16,17,15,
	16,16,17,16,12,14,13,16,15,13,13,14,15,16,14,15,
	15,16,16,16,15,16,16,17,16,16,16,17,17,12,13,14,
	15,16,14,14,15,15,17,13,14,13,16,15,16,16,17,17,
	18,15,16,15,17,15,15,16,15,17,17,15,15,16,16,17,
	16,17,16,17,17,16,15,17,15,18,17,18,17,18,18,15,
	15,16,16,17,16,16,17,16,18,15,15,15,16,16,17,17,
	18,17,18,16,16,15,17,15,12,13,13,15,15,13,14,14,
	16,16,13,14,14,16,16,15,16,16,17,18,15,16,15,18,
	16,13,14,14,16,16,14,14,15,16,17,14,15,15,17,17,
	16,16,17,17,18,16,16,17,18,17,13,14,13,16,14,14,
	15,15,17,16,14,15,14,17,15,16,17,17,18,17,15,17,
	15,18,16,15,16,16,17,17,16,16,17,17,18,16,17,17,
	18,18,17,16,18,17,19,18,18,18,18,18,15,16,15,17,
	14,16,16,16,18,15,16,17,15,18,14,18,18,18,18,17,
	17,18,16,19,15,
};

static const static_codebook _44p5_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p5_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p5_p2_0,
	0
};

static const long _vq_quantlist__44p5_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p3_0[] = {
	 1, 5, 6, 5, 7, 8, 5, 8, 7, 5, 7, 8, 7, 8,10, 8,
	10,10, 5, 8, 7, 8,10,10, 7,10, 8, 6, 8, 9, 8,10,
	11, 9,10,10, 9,10,11,10,11,12,11,12,12, 9,11,10,
	11,12,12,10,12,11, 6, 9, 8, 9,10,10, 8,11,10, 9,
	10,11,10,11,12,11,12,12, 9,11,10,11,12,12,10,12,
	11, 6, 9, 9, 8,10,11, 9,11,10, 8,10,10,10,10,12,
	11,12,12, 9,11,10,11,12,12,10,12,11, 8,10,10,10,
	11,12,10,12,11,10,10,12,11,11,13,12,13,13,10,12,
	11,12,13,13,11,13,11, 7,10,10,10,11,12,10,12,11,
	10,12,11,11,11,12,12,14,13,10,12,12,12,14,14,11,
	13,11, 6, 9, 9, 9,10,11, 8,11,10, 9,10,11,10,11,
	12,11,12,12, 8,11,10,11,12,12,10,12,10, 7,10,10,
	10,11,12,10,12,11,10,12,12,11,11,13,12,13,13,10,
	11,12,12,13,14,11,12,11, 8,10,10,10,11,12,10,12,
	11,10,11,12,11,11,13,12,13,13,10,12,10,12,13,13,
	11,13,11,
};

static const static_codebook _44p5_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p5_p3_0,
	0
};

static const long _vq_quantlist__44p5_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p3_1[] = {
	 5, 6, 6, 6, 7, 7, 6, 7, 7, 6, 7, 7, 7, 7, 8, 7,
	 8, 8, 6, 7, 7, 7, 8, 8, 7, 8, 7, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8,
	 8, 9, 9, 8, 9, 9, 7, 8, 7, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9,
	 8, 6, 8, 8, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 9,
	 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8, 8,
	 9, 9, 8, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 7, 8, 8, 8, 8, 9, 8, 9, 8,
	 8, 8, 8, 8, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9, 8,
	 9, 9, 6, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8,
	 8, 8, 9, 8, 9, 8, 8, 8, 9, 8, 9, 9, 9, 9, 9, 8,
	 8, 8, 9, 9, 9, 8, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9,
	 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p5_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p5_p3_1,
	0
};

static const long _vq_quantlist__44p5_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p4_0[] = {
	 1, 5, 5, 5, 7, 9, 5, 9, 7, 5, 7, 8, 7, 7,10, 9,
	10,10, 5, 8, 7, 9,10,10, 7,10, 7, 6, 8, 9, 9,10,
	12, 9,11,11, 9,10,11,11,11,13,12,13,13, 9,11,11,
	11,12,13,11,13,11, 6, 9, 8, 9,11,11, 9,12,10, 9,
	11,11,11,11,13,11,13,12, 9,11,10,12,13,13,11,13,
	11, 6, 9, 9, 8,10,11, 9,12,11, 9,10,11,10,10,12,
	11,13,13, 9,11,11,11,13,12,11,13,11, 8,10,10, 9,
	10,12,10,12,11,10,10,12,10,10,13,12,13,13,10,12,
	11,12,13,13,10,13,10, 7,10,10,11,11,13,11,14,11,
	10,12,11,11,11,13,13,14,13,10,12,12,14,14,14,11,
	14,11, 6, 9, 9, 9,11,12, 8,11,10, 9,11,11,11,11,
	13,11,12,13, 8,11,10,11,13,13,10,12,10, 7,10,10,
	11,11,14,11,13,11,10,12,12,11,11,14,14,14,14,10,
	11,12,13,13,14,11,13,11, 8,10,10,10,11,12, 9,12,
	10,10,11,12,11,10,13,12,13,13,10,12,10,12,13,13,
	11,13,10,
};

static const static_codebook _44p5_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p5_p4_0,
	0
};

static const long _vq_quantlist__44p5_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p5_p4_1[] = {
	 5, 7, 7,10,10, 7, 8, 9,10,11, 7, 9, 8,11,10, 9,
	10,10,11,11, 9,10,10,11,11, 7, 9, 9,10,10, 8, 9,
	10,10,11, 9,10,10,11,11,10,10,11,11,11,10,11,11,
	12,12, 7, 9, 9,10,10, 9,10,10,11,11, 8,10, 9,11,
	10,10,11,11,11,11,10,11,10,11,11,10,10,10,11,11,
	10,10,11,11,11,11,11,11,11,11,11,11,12,11,12,11,
	12,11,12,12,10,10,10,11,11,10,11,11,11,11,10,11,
	10,11,11,11,12,11,12,12,11,12,11,12,11, 8, 9, 9,
	11,11, 9,10,10,11,12, 9,10,10,11,11,10,11,11,12,
	12,10,11,11,12,12, 9,10,10,11,11,10,10,11,11,12,
	10,11,11,12,12,11,11,12,12,12,11,12,12,12,12, 9,
	10,10,11,11,10,11,11,12,12,10,11,10,12,12,11,12,
	12,12,12,11,12,12,12,12,11,11,11,12,12,11,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12, 8, 9, 9,11,11, 9,
	10,10,11,11, 9,10,10,11,11,10,11,11,12,12,10,11,
	11,12,12, 9,10,10,11,11,10,10,11,12,12,10,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12, 9,10,10,11,
	11,10,11,11,12,12,10,11,10,12,11,11,12,12,12,12,
	11,12,11,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,
	11,12,12,11,12,12,12,12,11,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,10,11,11,12,12,11,12,12,12,
	12,11,12,12,12,12,12,12,13,13,13,12,12,12,13,13,
	11,12,12,12,12,12,12,12,12,13,12,12,12,13,13,12,
	12,13,13,13,12,13,13,13,13,11,12,12,12,12,12,12,
	12,13,13,12,12,12,13,13,12,13,13,13,13,12,13,13,
	13,13,12,12,12,12,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,12,12,12,13,12,
	13,13,13,13,13,12,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,10,11,11,12,12,11,12,12,12,12,11,12,
	11,12,12,12,12,12,13,12,12,12,12,13,13,11,12,12,
	12,12,12,12,12,13,13,12,12,12,13,13,12,13,13,13,
	13,12,13,13,13,13,11,12,12,12,12,12,12,12,13,13,
	12,12,12,13,12,12,13,13,13,13,12,13,12,13,13,12,
	12,12,12,13,12,13,13,13,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,12,12,12,13,12,13,13,13,
	13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13, 8, 9, 9,11,11, 9,10,10,11,11, 9,10,10,12,11,
	10,11,11,12,12,10,11,11,12,12, 9,10,10,11,11,10,
	10,11,11,12,10,11,11,12,12,11,11,12,12,12,11,12,
	12,12,12, 9,10,10,11,11,10,11,11,12,12,10,11,10,
	12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,12,
	12,11,11,12,12,12,11,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12, 9,10,
	10,11,11,10,10,11,12,12,10,11,11,12,12,11,11,12,
	12,12,11,12,12,12,12,10,10,11,11,12,11,11,12,12,
	12,11,11,12,12,12,11,11,12,12,13,12,12,12,12,12,
	10,11,11,12,12,11,12,11,12,12,11,12,11,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,12,12,12,12,12,
	12,12,12,12,12,12,12,13,12,12,13,12,13,12,12,13,
	13,13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,12,12,12,12,13,12, 8,10,10,11,11,
	10,11,11,12,12,10,11,10,12,12,11,12,12,12,12,11,
	12,12,12,12,10,11,10,12,12,10,10,11,12,12,11,12,
	12,12,12,12,12,12,12,13,12,12,12,13,13,10,11,11,
	12,12,11,12,12,12,12,10,12,11,12,12,12,12,12,13,
	13,12,13,12,13,12,11,12,12,12,12,11,12,12,12,13,
	12,12,12,13,13,12,12,13,12,13,12,13,13,13,13,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,13,
	13,13,13,12,13,12,13,12,11,11,11,12,12,11,12,12,
	12,13,11,12,12,12,12,12,12,12,13,13,12,12,13,13,
	13,11,12,12,12,12,12,12,12,12,13,12,12,13,13,13,
	12,12,13,13,13,13,13,13,13,13,11,12,12,12,12,12,
	13,12,13,13,12,12,12,13,13,12,13,13,13,13,12,13,
	13,13,13,12,12,12,12,13,12,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,10,11,11,12,12,11,12,12,12,13,11,
	12,12,13,12,12,13,13,13,13,12,13,13,13,13,11,12,
	12,12,12,12,12,12,13,13,12,13,12,13,13,13,13,13,
	13,13,13,13,13,13,13,11,12,12,13,12,12,13,12,13,
	13,12,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	12,13,13,13,13,12,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13, 8, 9, 9,11,11, 9,10,10,11,12, 9,10,10,11,
	11,10,11,11,12,12,10,11,11,12,12, 9,10,10,11,11,
	10,10,11,12,12,10,11,11,12,12,11,11,12,12,12,11,
	12,12,12,12, 9,10,10,11,11,10,11,11,12,12,10,11,
	10,12,12,11,12,12,12,12,11,12,11,12,12,11,11,11,
	12,12,11,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12, 8,
	10,10,11,11,10,10,11,12,12,10,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,10,11,11,12,12,10,11,12,
	12,12,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	13,10,10,11,12,12,11,12,12,12,12,10,11,10,12,12,
	12,12,12,13,13,12,12,12,13,12,11,12,12,12,12,11,
	12,12,12,13,12,12,12,13,13,12,12,13,12,13,12,13,
	13,13,13,11,12,12,12,12,12,12,12,13,13,11,12,12,
	13,12,12,13,13,13,13,12,13,12,13,12, 9,10,10,11,
	11,10,11,11,12,12,10,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,10,11,11,12,12,11,11,12,12,12,11,
	11,12,12,12,12,12,12,12,13,12,12,12,13,12,10,11,
	10,12,11,11,12,11,12,12,11,12,11,12,12,12,12,12,
	12,12,12,12,11,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,13,12,13,12,13,13,13,13,
	11,12,11,12,12,12,12,12,13,12,12,12,12,12,12,12,
	13,12,13,13,12,12,12,13,12,10,11,11,12,12,11,12,
	12,12,13,11,12,12,13,12,12,12,13,13,13,12,13,13,
	13,13,11,12,12,12,13,12,12,13,13,13,12,12,13,13,
	13,13,13,13,13,13,13,13,13,13,13,11,12,12,12,12,
	12,12,13,13,13,12,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13,12,13,13,13,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,12,12,13,
	13,13,13,13,13,13,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,11,11,11,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,13,13,13,12,13,12,13,13,11,
	12,12,12,12,12,12,13,13,13,12,12,13,13,13,12,13,
	13,13,13,12,13,13,13,13,11,12,12,12,12,12,13,12,
	13,13,12,12,12,13,12,13,13,13,13,13,12,13,12,13,
	13,12,12,12,13,13,12,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,12,12,12,13,12,13,
	13,13,13,13,12,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,10,11,11,12,12,11,12,12,12,12,11,12,12,
	12,12,12,12,12,13,13,12,12,12,13,13,11,12,12,12,
	12,11,12,12,13,13,12,12,12,13,13,12,12,13,13,13,
	12,13,13,13,13,11,12,12,12,12,12,12,12,13,13,12,
	12,12,13,12,12,13,13,13,13,12,13,12,13,13,12,12,
	12,12,12,12,12,13,13,13,12,13,13,13,13,12,13,13,
	13,13,13,13,13,13,13,12,12,12,13,12,12,13,13,13,
	13,12,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	10,11,11,12,12,11,12,12,12,13,11,12,12,13,12,12,
	12,12,13,13,12,12,12,13,13,11,12,12,12,12,12,12,
	13,13,13,12,12,12,13,13,12,12,13,13,13,12,13,13,
	13,13,11,12,12,12,12,12,12,12,13,13,12,12,12,13,
	13,12,13,13,13,13,12,13,13,13,13,12,12,12,12,13,
	12,12,13,13,13,12,13,13,13,13,12,13,13,13,13,13,
	13,13,13,13,12,12,12,13,13,13,13,13,13,13,12,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,10,11,11,
	12,12,11,12,12,12,13,11,12,12,13,12,12,13,13,13,
	13,12,13,12,13,13,11,12,12,13,13,12,12,12,13,13,
	12,12,13,13,13,12,13,13,13,13,13,13,13,13,13,11,
	12,12,13,12,12,13,12,13,13,12,13,12,13,13,13,13,
	13,13,13,12,13,13,13,13,12,12,12,13,13,12,13,13,
	13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,12,12,12,13,13,12,13,13,13,13,12,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,11,11,11,12,12,11,
	12,12,12,12,11,12,12,12,12,12,12,12,13,13,12,12,
	12,13,13,11,12,12,12,12,12,12,12,12,13,12,12,12,
	13,13,12,12,13,13,13,12,13,13,13,13,11,12,12,12,
	12,12,12,12,13,13,12,12,12,13,12,12,13,13,13,13,
	12,13,12,13,13,12,12,12,12,12,12,12,13,12,13,12,
	13,13,13,13,12,13,13,12,13,13,13,13,13,13,12,12,
	12,12,12,12,13,13,13,13,12,13,12,13,13,13,13,13,
	13,13,12,13,13,13,12,10,11,11,12,12,11,12,12,12,
	12,11,12,12,12,12,12,12,12,13,13,12,13,12,13,13,
	11,12,12,12,12,12,12,12,13,13,12,12,12,13,13,12,
	12,13,13,13,13,13,13,13,13,11,12,12,12,12,12,13,
	12,13,13,12,13,12,13,13,12,13,13,13,13,12,13,12,
	13,13,12,12,12,12,12,12,13,13,13,13,12,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,12,12,12,13,12,
	12,13,13,13,13,12,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13,10,11,11,12,12,11,12,12,12,12,11,12,
	12,12,12,12,12,12,13,13,12,12,12,13,13,11,12,12,
	12,12,12,12,12,13,13,12,12,12,13,13,12,12,13,13,
	13,12,12,13,13,13,11,12,11,12,12,12,12,12,13,13,
	11,12,12,13,13,12,13,13,13,13,12,13,12,13,13,12,
	12,12,12,12,12,13,13,13,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,12,12,12,13,12,12,13,13,
	13,13,12,13,12,13,13,13,13,13,13,13,12,13,13,13,
	13,10,11,11,12,12,11,12,12,12,13,11,12,12,13,12,
	12,12,13,13,13,12,13,13,13,13,11,12,12,13,13,12,
	12,13,13,13,12,12,13,13,13,12,13,13,13,13,13,13,
	13,13,13,11,12,12,13,12,12,13,12,13,13,12,12,12,
	13,13,12,13,13,13,13,13,13,13,13,13,12,12,13,13,
	13,12,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,12,12,12,13,13,13,13,13,13,13,12,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,10,12,
	11,12,12,11,12,12,12,13,11,12,12,12,12,12,12,12,
	13,13,12,12,12,13,13,11,12,12,12,13,12,12,12,13,
	13,12,12,12,13,13,12,13,13,13,13,12,13,13,13,13,
	11,12,12,13,12,12,12,12,13,13,12,12,12,13,13,12,
	13,13,13,13,12,13,12,13,13,12,13,12,13,13,12,13,
	13,13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,12,12,12,13,12,13,13,13,13,13,12,13,12,13,
	13,13,13,13,13,13,12,13,13,13,13,10,11,11,12,12,
	11,12,12,12,13,11,12,12,12,12,12,12,12,13,13,12,
	12,12,13,13,11,12,12,12,12,12,12,13,13,13,12,13,
	13,13,13,12,12,13,13,13,13,13,13,13,13,11,12,12,
	12,12,12,13,12,13,13,12,12,12,13,13,12,13,13,13,
	13,12,13,12,13,13,12,12,12,12,13,12,13,13,13,13,
	12,13,13,13,13,12,13,13,13,13,13,13,13,13,13,12,
	12,12,12,12,12,13,13,13,13,12,13,13,13,13,13,13,
	13,13,13,12,13,13,13,13,11,12,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	12,11,12,12,12,12,12,12,12,12,13,12,12,12,13,13,
	12,12,13,13,13,12,13,13,13,13,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,12,12,13,13,13,13,12,13,
	12,13,13,12,12,12,12,12,12,12,13,13,13,12,13,13,
	13,13,13,13,13,12,13,13,13,13,13,13,12,12,12,12,
	12,12,13,13,13,13,12,13,12,13,12,13,13,13,13,13,
	13,13,13,13,12,
};

static const static_codebook _44p5_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p5_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p5_p4_1,
	0
};

static const long _vq_quantlist__44p5_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p5_p5_0[] = {
	 1, 6, 6,10,10, 6, 7, 9,11,13, 5, 9, 7,13,11, 8,
	11,12,13,15, 8,12,11,15,13, 6, 7, 8,11,11, 7, 8,
	10,11,13, 9,10,10,13,13,11,11,13,12,16,12,13,13,
	16,15, 6, 8, 7,11,11, 9,10,10,13,13, 7,10, 7,13,
	11,12,13,13,15,16,11,13,11,16,12,10,11,11,11,13,
	11,11,13,12,15,13,13,13,14,15,13,12,15,12,17,15,
	16,16,16,16,10,11,11,14,11,13,13,13,15,14,11,13,
	11,15,12,15,15,16,16,16,13,15,12,17,12, 6, 8, 9,
	12,12, 9,10,12,13,15, 9,11,11,15,14,12,13,15,16,
	18,13,14,14,17,16, 9,10,11,13,14,11,10,13,14,16,
	11,12,12,15,15,14,13,16,15,18,14,15,15,17,17, 9,
	11,11,14,14,11,12,13,15,16,11,13,11,15,14,15,15,
	15,17,18,14,15,14,17,15,13,14,14,15,16,14,14,15,
	15,17,15,16,15,17,17,16,16,17,15,19,17,18,18,19,
	18,13,14,14,16,15,15,15,16,17,17,14,15,14,18,15,
	17,17,17,19,19,16,17,15,19,16, 6, 9, 8,13,12, 9,
	11,11,14,15, 9,12,10,15,13,13,14,14,16,17,12,15,
	13,18,16, 9,11,11,14,14,11,11,13,14,15,11,13,12,
	16,15,14,14,15,15,18,14,15,15,18,17, 9,11,10,14,
	13,11,12,12,15,15,11,13,10,16,14,14,15,15,16,18,
	14,16,13,18,15,13,14,14,16,16,14,14,15,15,17,15,
	16,15,17,17,16,16,17,16,19,17,18,17,18,19,13,14,
	14,16,15,15,15,15,17,17,14,15,14,17,15,17,17,17,
	18,19,16,17,15,19,15,11,13,13,15,16,13,14,15,16,
	18,14,15,15,17,17,16,16,18,18,20,17,18,17,19,20,
	13,14,14,16,17,15,15,16,17,18,15,16,16,17,17,18,
	17,19,18,19,18,18,18,19,21,14,14,15,16,17,15,15,
	16,18,18,15,16,16,17,18,18,18,19,19,21,18,19,19,
	22,20,16,16,17,17,19,17,17,17,18,20,17,18,18,20,
	19,19,19,20,19, 0,19,19,20,20,21,17,17,17,19,18,
	18,18,20,19,19,18,18,18,20,20,19,19,20,20,20,20,
	21,20,21,19,11,13,13,16,15,14,15,15,17,17,14,15,
	14,18,16,16,18,18,20,19,16,19,17,21,18,13,14,15,
	16,17,15,15,16,18,18,15,16,15,19,18,18,18,18,19,
	19,18,18,18,22,20,13,14,14,16,16,15,16,16,18,17,
	15,16,15,18,17,18,18,18,19,19,17,18,17,21,18,16,
	17,17,18,18,17,18,19,19,19,18,20,18,19,19,19,20,
	21,19,21,20,20,20, 0,21,16,17,17,19,19,18,18,18,
	19,21,17,18,18,19,18,20,19,21,20,21,19,20,20,22,
	19, 7, 9, 9,13,13, 8,10,11,14,15, 9,12,11,15,14,
	11,13,14,16,17,13,15,14,17,16, 8,10,11,14,14,10,
	10,12,14,16,11,12,12,16,15,13,12,15,15,18,14,15,
	15,19,17, 9,11,11,14,14,11,12,12,15,15,11,13,11,
	16,14,14,15,14,17,17,14,16,14,18,15,12,13,14,15,
	16,13,13,15,14,17,15,15,15,17,17,15,14,17,14,19,
	17,18,18,19,18,13,14,14,16,16,15,15,15,17,17,14,
	15,14,18,15,17,18,17,18,17,16,18,16,19,15, 7,10,
	10,13,13, 9,10,12,14,15,10,12,11,15,14,12,13,14,
	16,17,13,15,14,18,16,10,10,12,13,14,10,10,13,13,
	16,12,12,13,15,15,13,12,15,15,18,15,15,16,18,17,
	10,11,11,14,14,12,13,13,15,16,10,13,10,16,14,14,
	15,15,17,17,14,15,13,17,15,13,13,14,15,16,14,13,
	15,14,18,15,15,16,16,17,16,15,18,15,18,17,18,18,
	18,18,13,15,14,17,16,15,16,16,17,17,14,15,13,17,
	15,17,17,18,18,18,16,17,14,20,14, 8,10,10,14,14,
	11,11,13,14,16,11,13,11,16,14,14,15,16,16,18,14,
	16,15,18,16,10,12,11,15,14,11,11,13,14,16,13,14,
	13,16,15,15,14,16,15,19,16,17,16,20,18,10,11,12,
	14,15,13,13,14,16,16,11,14,11,16,14,16,16,17,18,
	19,15,17,14,20,15,14,15,14,17,16,13,14,15,15,18,
	16,17,16,19,18,16,15,18,15,19,18,19,18,21,21,14,
	14,15,16,17,16,16,17,18,18,13,15,14,17,15,18,18,
	19,18,22,16,18,15,21,15,12,13,14,16,16,14,14,16,
	16,18,14,15,15,17,18,16,16,18,18,20,18,18,17,20,
	20,13,14,15,15,17,15,14,16,16,18,16,16,16,17,19,
	17,15,18,17,21,18,18,18,19,19,14,15,15,18,17,15,
	16,16,18,19,15,16,15,18,18,17,18,18,20,21,17,19,
	17,20,19,16,16,17,16,19,17,17,18,17,20,18,18,18,
	18,19,19,18,20,17,22,20,20,19,20,20,17,17,18,18,
	19,18,18,20,21,20,17,18,17,20,20,21,21,21,21,21,
	19,21,18,22,20,11,13,13,17,16,14,14,16,16,18,14,
	16,14,18,16,17,18,19,19,20,18,19,18,21,19,14,15,
	14,17,16,14,14,16,18,18,16,17,16,18,17,18,17,19,
	18,20,19,19,18,20,20,13,14,15,16,17,16,16,17,18,
	19,14,16,14,19,17,18,19,18,20,20,18,20,17,21,18,
	17,17,17,19,18,16,17,18,18,19,18,19,18,21,21,18,
	18,20,17,21,19,20,20,22,21,16,17,18,18,19,18,18,
	19,21,20,16,17,17,20,18,21,21,22,21,22,18,21,18,
	 0,18, 7, 9, 9,13,13, 9,11,12,14,15, 8,11,10,15,
	14,13,14,15,16,18,11,14,13,17,15, 9,11,11,14,14,
	11,11,13,14,16,11,12,12,15,15,14,14,16,15,18,14,
	14,15,17,17, 8,11,10,14,14,11,12,12,15,15,10,12,
	10,16,14,14,15,15,17,18,13,15,12,18,15,13,14,14,
	16,16,14,14,15,15,17,15,15,15,16,17,16,15,17,15,
	19,17,17,17,18,18,12,14,13,16,15,15,15,15,17,17,
	13,15,13,17,14,17,18,18,18,19,15,17,14,19,14, 8,
	10,10,14,14,11,11,13,14,16,11,13,11,16,14,14,15,
	16,17,19,14,16,15,18,17,10,12,11,15,14,11,11,14,
	14,17,13,14,13,17,15,15,14,17,15,19,16,17,16,19,
	17,10,11,12,14,15,13,13,14,15,17,11,13,11,17,14,
	16,16,17,18,19,15,16,14,18,15,14,15,14,16,16,13,
	14,15,15,18,16,16,16,18,18,16,15,18,15,20,18,19,
	18,21,18,14,14,15,16,17,16,16,17,17,18,13,15,14,
	17,16,19,19,19,19,19,15,18,15,20,15, 7,10,10,13,
	13,10,11,12,14,15, 9,12,10,15,14,13,14,15,16,17,
	12,15,13,17,16,10,11,11,14,14,10,10,13,14,16,12,
	13,13,16,15,14,13,16,15,18,15,15,16,17,17,10,12,
	10,14,13,12,13,12,15,15,10,13,10,16,13,15,16,15,
	17,18,13,16,12,18,15,13,14,14,16,17,14,13,15,15,
	18,15,16,15,17,17,16,14,17,15,19,17,18,18,19,19,
	13,15,13,17,14,15,15,15,18,17,14,15,13,17,14,18,
	17,18,18,19,15,17,15,19,15,11,13,13,16,17,14,14,
	16,16,18,14,16,15,18,17,17,18,19,18,21,18,18,17,
	20,18,13,15,14,17,16,14,14,16,17,18,16,17,16,19,
	17,18,17,19,18,22,18,19,19,21,21,13,14,15,16,18,
	16,16,17,17,20,14,16,14,18,17,18,18,19,19,21,17,
	18,17,21,18,17,18,17,19,18,16,17,17,18,19,18,18,
	18,22,22,18,17,19,17, 0,20,21,19,21,20,17,17,18,
	18,21,18,18,18,19,21,17,17,17,19,19,20,20,22,21,
	21,19,20,18,20,17,12,14,13,17,16,14,15,15,17,18,
	14,16,14,18,16,17,18,18,21,20,16,18,16,21,18,14,
	15,15,17,17,15,15,16,18,18,15,17,16,18,18,17,17,
	19,19,20,18,19,18,20,19,14,15,14,17,15,15,16,16,
	18,17,15,16,14,19,15,18,18,18,19,20,17,20,15,21,
	17,16,17,18,18,19,17,17,18,18,20,18,19,18,19,21,
	19,18,19,19,21,20, 0,19,21,20,16,17,16,19,16,18,
	18,18,19,19,17,18,17,20,17,19,20,20,22, 0,19,20,
	17,21,17,11,13,14,16,17,14,15,15,17,18,14,15,15,
	18,18,16,17,17,19,20,16,18,17,19,21,13,14,15,17,
	17,14,15,16,17,19,15,16,16,18,19,16,17,18,19,21,
	17,18,20,21,21,13,15,15,17,17,15,16,16,18,19,15,
	16,16,18,19,17,17,18,19,22,17,19,18,22,19,15,16,
	17,19,19,16,17,18,18,20,17,18,18,19,20,19,18,20,
	18,22,20,19,19,22,21,16,17,17,18,19,18,18,18,19,
	20,17,18,18,20,19,20,19,20,22,20,19,20,21,21,20,
	12,14,14,16,16,13,14,16,17,18,14,16,15,18,18,15,
	17,17,19,19,17,18,18,19,19,13,14,15,16,17,14,14,
	16,16,20,15,16,16,17,19,16,15,18,17,20,18,17,19,
	19,19,14,15,15,17,17,16,16,16,18,18,15,16,15,19,
	18,17,18,18,20,21,17,18,17,21,18,16,15,17,17,19,
	17,15,18,17,20,19,17,18,19,20,18,16,19,17,22,20,
	19,20,19,20,17,17,18,19,19,18,18,19,20,20,17,18,
	17,18,18,21,21,20,20,21,18,20,17,21,19,11,14,14,
	16,17,15,14,16,17,19,14,16,14,18,17,18,18,19,19,
	21,17,19,18,20,20,13,15,14,17,17,14,14,16,17,18,
	16,17,16,19,18,18,17,19,18,20,18,21,18,20,20,13,
	15,15,16,17,16,16,17,18,19,14,16,15,19,18,19,19,
	19,21,20,18,19,17,20,18,16,17,16,19,18,16,17,17,
	19,20,17,19,18,20,19,18,17,21,18, 0,21,20,20, 0,
	20,17,17,18,18,19,18,19,19,20,22,16,17,17,20,18,
	21,22,20,20,22,18,22,18,22,18,12,14,14,17,17,14,
	15,16,17,19,14,16,15,17,17,17,17,18,18,21,17,19,
	17,20,19,14,15,15,16,18,15,14,16,16,19,16,17,16,
	19,18,17,16,20,17,20,18,20,19,19,20,14,15,15,18,
	17,16,16,17,18,19,14,16,15,19,17,18,21,18,19,21,
	17,18,17,19,18,17,17,18,17,20,17,16,18,17,21,18,
	19,19,19,19,18,17,19,17,20,20,21,20,21,20,17,17,
	17,19,19,19,18,18,20,21,16,18,16,19,18,20,20,21,
	21,20,18,19,16, 0,17,12,14,14,17,17,15,15,18,17,
	19,15,18,15,20,16,20,19,21,18,22,20,20,20,22,19,
	14,16,14,20,17,14,15,17,17,20,18,18,17,20,18,18,
	17,19,17,21,20,21,20, 0,21,14,15,16,17,19,18,17,
	19,18,21,14,18,15,21,17,21,20,21,20, 0,18,21,17,
	21,17,18,19,17,20,18,16,17,17,19,19,19,21,20, 0,
	20,18,17,21,17, 0,22, 0,21, 0,22,17,17,19,18,20,
	20,20,21,19,22,16,17,18,20,18,22,22, 0,22, 0,17,
	21,17,22,17,11,14,13,16,16,14,15,15,17,18,14,15,
	14,18,17,17,18,18,19,20,16,17,17,21,19,13,14,15,
	17,17,15,16,16,18,18,15,16,16,19,18,18,18,18,19,
	20,17,18,18,20,19,13,15,14,17,17,15,16,16,17,18,
	14,16,15,19,17,17,18,19,21,21,17,18,17,20,18,16,
	17,17,19,19,17,18,19,19,20,18,19,18,21,21,21,20,
	19,21,22,20,20,19,21,20,15,17,16,19,19,17,18,18,
	20,21,16,18,17,20,18,19,19,21,21,21,19,19,19,20,
	18,11,14,13,17,16,14,14,16,16,19,14,16,15,19,16,
	18,18,18,19,22,17,18,17,20,19,13,15,14,17,17,15,
	15,16,17,19,16,17,16,20,18,18,17,19,18,21,19,19,
	18,22, 0,13,14,15,17,18,16,16,17,17,19,14,16,15,
	19,18,18,19,19,20,21,18,18,17,20,18,17,18,17,20,
	18,16,17,17,18,20,18,19,18,20,20,18,18,21,17,21,
	20,21,21, 0,19,16,16,18,18,19,19,18,20,19,20,16,
	17,17,20,18,21,20,21,22,22,18,20,17,21,17,12,14,
	14,17,16,14,15,16,18,18,13,15,14,18,17,17,18,18,
	19,19,15,17,16,19,19,14,15,15,17,17,15,15,16,18,
	19,15,16,16,19,18,17,17,18,18,20,18,18,18,21,20,
	13,15,14,17,16,15,16,15,18,18,14,16,14,18,17,18,
	18,18,19,21,16,18,16,20,17,17,18,17,18,19,17,17,
	18,18,19,18,19,19,21,19,19,18,20,18,21,21,20,20,
	21,20,16,17,15,20,17,17,19,17,19,19,17,18,15,20,
	17,19,20,19,21,22,17,20,16, 0,17,12,14,14,17,18,
	16,15,18,16,20,16,18,15,21,17,20,18,21,19,22,19,
	21,19, 0,19,14,16,15,19,17,14,15,17,16,21,18,19,
	18,21,17,19,17,21,17,22,20,21,21, 0,21,14,15,16,
	17,19,18,17,19,18,21,14,17,15,20,17,21,22,21,20,
	22,18,21,17,21,17,17,19,17,21,18,16,17,17,19,20,
	19,21,20,21,20,17,18,20,17,21, 0,22,20,21,22,17,
	17,20,18,21,21,20,22,20,21,16,17,17,21,19, 0,22,
	 0,21,21,18,22,17,21,17,12,14,14,17,16,14,15,16,
	17,18,14,16,15,18,17,17,17,20,19,20,16,18,17,21,
	18,14,15,15,17,17,14,15,16,17,19,16,17,16,18,18,
	17,16,19,18,19,18,19,18,21,20,14,15,15,18,17,16,
	16,16,19,18,15,16,14,20,16,18,18,19,19,20,16,19,
	16,21,17,17,17,18,19,19,16,16,18,18,19,19,19,18,
	20,20,18,16,19,18,20,22,21,20,19,20,16,18,17,20,
	16,18,19,18,19,18,16,18,16,20,17,21,20,21,20,20,
	18,19,17,21,16,
};

static const static_codebook _44p5_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p5_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p5_p5_0,
	0
};

static const long _vq_quantlist__44p5_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p5_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p5_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p5_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p5_p5_1,
	0
};

static const long _vq_quantlist__44p5_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p6_0[] = {
	 1, 5, 5, 5, 7, 9, 5, 9, 7, 5, 7, 8, 7, 7,10, 9,
	 9,10, 5, 8, 7, 9,10, 9, 7,10, 7, 6, 9, 9, 9,10,
	12,10,12,11, 9,10,11,11,10,13,12,12,13,10,11,11,
	12,13,13,11,13,11, 6, 9, 9,10,11,12, 9,12,11,10,
	11,11,11,11,13,12,13,13, 9,11,10,12,13,13,11,13,
	10, 6, 9,10, 9,11,12,10,12,11, 9,10,11,10,10,13,
	11,13,13,10,11,11,12,13,12,11,13,11, 7, 9,10, 9,
	10,12,10,11,11,10,10,11,10,10,12,12,11,12,10,11,
	10,12,12,12,10,12,10, 7,10,10,11,11,13,11,13,11,
	10,12,11,11,10,13,13,14,13,10,11,12,13,13,14,11,
	13,10, 6,10, 9,10,11,12, 9,12,11, 9,11,11,11,11,
	13,12,12,13, 9,11,10,12,13,13,10,13,10, 7,10,10,
	11,11,14,11,13,11,10,12,11,11,10,14,13,14,13,10,
	11,12,13,13,14,11,13,10, 7,10, 9,10,10,12, 9,12,
	10,10,11,11,10,10,12,12,12,12, 9,11,10,11,12,12,
	10,12, 9,
};

static const static_codebook _44p5_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p5_p6_0,
	0
};

static const long _vq_quantlist__44p5_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p6_1[] = {
	 2, 6, 6, 5, 7, 8, 5, 8, 7, 6, 7, 7, 7, 7, 8, 8,
	 8, 8, 6, 7, 7, 7, 8, 8, 7, 8, 7, 6, 8, 8, 8, 9,
	10, 8, 9, 9, 8, 9, 9, 9, 9,10,10,10,10, 8, 9, 9,
	10,10,10, 9,10,10, 6, 8, 8, 8, 9, 9, 8,10, 9, 9,
	 9, 9, 9, 9,10,10,10,10, 8, 9, 9,10,10,10, 9,10,
	 9, 6, 8, 9, 8, 9, 9, 8, 9, 9, 8, 9, 9, 9, 9,10,
	 9,10,10, 8, 9, 9, 9,10,10, 9,10, 9, 7, 8, 9, 8,
	 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 7, 9, 9, 9,10,10, 9,10,10,
	 9,10, 9, 9, 9,10,10,10,10, 9,10, 9,10,10,10, 9,
	10, 9, 6, 8, 8, 8, 9, 9, 8, 9, 9, 8, 9, 9, 9, 9,
	10, 9,10,10, 8, 9, 9, 9,10,10, 9,10, 9, 7, 9, 9,
	 9,10,10, 9,10, 9, 9, 9,10,10, 9,10,10,10,10, 9,
	 9, 9,10,10,10, 9,10, 9, 7, 9, 8, 8, 9, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p5_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p5_p6_1,
	0
};

static const long _vq_quantlist__44p5_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p5_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p5_p7_0,
	0
};

static const long _vq_quantlist__44p5_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p5_p7_1[] = {
	 1, 7, 7, 6, 9, 9, 7, 9, 9, 6, 9, 9, 9, 9, 9, 9,
	 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 7, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,
};

static const static_codebook _44p5_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p5_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p5_p7_1,
	0
};

static const long _vq_quantlist__44p5_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p5_p7_2[] = {
	 1, 2, 3, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9,10,10,11,
	11,12,12,13,13,14,14,14,14,
};

static const static_codebook _44p5_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p5_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p5_p7_2,
	0
};

static const long _vq_quantlist__44p5_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p5_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p5_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p5_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p5_p7_3,
	0
};

static const char _huff_lengthlist__44p5_short[] = {
	 4, 7,12,14,15,18,20,20, 5, 3, 4, 6, 9,11,15,19,
	 9, 4, 3, 4, 7, 9,13,18,11, 6, 3, 3, 5, 8,13,19,
	14, 9, 6, 5, 7,10,16,20,16,11, 9, 8,10,10,14,16,
	21,14,13,11, 8, 7,11,14,21,14,13, 9, 6, 5,10,12,
};

static const static_codebook _huff_book__44p5_short = {
	2, 64,
	(char *)_huff_lengthlist__44p5_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p6_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p6_l0_0[] = {
	 1, 4, 4, 7, 7,10,10,12,12,12,12,13,12, 5, 5, 5,
	 8, 6,11, 9,12,12,13,12,12,12, 4, 5, 5, 6, 8, 9,
	11,12,12,13,12,12,12, 7, 7, 8, 9, 9,11, 8,12, 9,
	12,12,12,12, 7, 8, 8, 9, 9, 8,11, 9,12,12,12,11,
	12,10,10,10,11,11,11,11,11,10,11,11,12,11,10,10,
	10,11,11,11,11,10,11,11,11,11,12,11,11,11,12,11,
	12,11,12,11,13,11,13,11,11,11,11,11,12,11,12,10,
	13,11,12,11,13,12,12,12,13,12,13,13,13,12,14,12,
	14,13,12,12,12,12,13,13,13,12,14,12,14,13,14,13,
	14,14,14,14,14,14,14,14,15,14,15,14,13,14,13,14,
	14,14,14,14,15,14,14,14,15,
};

static const static_codebook _44p6_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p6_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p6_l0_0,
	0
};

static const long _vq_quantlist__44p6_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p6_l0_1[] = {
	 4, 4, 4, 5, 5, 4, 5, 5, 5, 5, 4, 5, 5, 5, 5, 5,
	 5, 5, 4, 5, 5, 5, 5, 5, 4,
};

static const static_codebook _44p6_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p6_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p6_l0_1,
	0
};

static const long _vq_quantlist__44p6_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_l1_0[] = {
	 1, 3, 2, 5, 5, 6, 6, 6, 6,
};

static const static_codebook _44p6_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44p6_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p6_l1_0,
	0
};

static const char _huff_lengthlist__44p6_lfe[] = {
	 2, 3, 1, 3,
};

static const static_codebook _huff_book__44p6_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p6_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p6_long[] = {
	 2, 7,13,15,16,17,19,20, 6, 3, 4, 7, 9,10,12,15,
	13, 4, 3, 4, 7, 8,11,13,14, 7, 4, 4, 6, 7,10,11,
	16, 9, 7, 6, 7, 8, 9,10,16, 9, 8, 7, 7, 6, 8, 8,
	18,12,10,10, 9, 8, 8, 9,20,14,13,12,11, 8, 9, 9,
};

static const static_codebook _huff_book__44p6_long = {
	2, 64,
	(char *)_huff_lengthlist__44p6_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p6_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p1_0[] = {
	 2, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 5, 7, 8, 8, 9,
	10, 8, 9, 9, 8, 9,10, 9,10,12,10,11,11, 8, 9,10,
	10,11,11, 9,11,11, 5, 8, 7, 8, 9, 9, 8,10, 9, 8,
	10, 9, 9,11,11,10,11,11, 8,10, 9,10,11,11, 9,12,
	10, 5, 8, 8, 7, 9,10, 8,10, 9, 7, 9, 9, 9,10,11,
	 9,11,11, 8,10,10,10,11,11,10,12,11, 7, 9, 9, 9,
	10,11, 9,11,11, 9, 9,11,10,10,13,11,11,12, 9,11,
	11,11,12,13,11,13,12, 7, 9, 9, 9,11,11, 9,12,10,
	 9,11,10,10,11,12,11,13,12, 9,11,11,11,13,13,11,
	13,11, 5, 8, 8, 8, 9,10, 7,10, 9, 8,10,10,10,11,
	11,10,11,11, 7, 9, 9, 9,11,11, 9,11,10, 7, 9, 9,
	 9,10,12, 9,11,11, 9,11,11,11,11,13,11,13,13, 9,
	10,11,11,12,13,10,12,11, 7, 9, 9, 9,11,11, 9,11,
	10, 9,11,11,11,12,13,11,13,12, 9,11, 9,11,12,11,
	10,13,10,
};

static const static_codebook _44p6_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p6_p1_0,
	0
};

static const long _vq_quantlist__44p6_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p6_p2_0[] = {
	 4, 6, 6, 9, 9, 6, 7, 8,10,10, 6, 8, 7,10,10, 8,
	10,10,12,13, 8,10,10,13,12, 6, 8, 8,10,10, 7, 8,
	 9,10,11, 8, 9, 9,11,11,10,10,11,12,13,10,11,11,
	14,13, 6, 8, 8,10,10, 8, 9, 9,11,11, 7, 9, 8,11,
	10,10,11,11,13,14,10,11,10,13,12, 9,10,10,12,12,
	10,10,11,12,13,10,11,11,13,13,12,12,13,12,15,13,
	14,13,15,14, 9,10,10,13,12,10,11,11,13,13,10,11,
	10,13,12,13,13,14,14,15,12,13,12,15,12, 6, 8, 8,
	10,11, 8, 9,10,11,12, 8, 9, 9,11,11,10,11,12,13,
	14,10,11,11,14,13, 8, 9, 9,11,12, 9,10,11,12,13,
	 9,10,11,12,13,11,11,13,13,15,11,12,12,14,14, 8,
	 9, 9,12,12, 9,10,11,12,13, 9,10,10,13,12,11,12,
	13,14,15,11,12,12,14,14,11,11,12,13,14,11,12,13,
	13,15,12,13,13,14,15,13,13,14,14,16,14,15,15,16,
	16,11,12,11,14,13,12,13,13,14,14,11,13,12,14,13,
	14,15,15,16,16,13,14,14,16,14, 6, 8, 8,11,10, 8,
	 9, 9,12,11, 8,10, 9,12,11,10,11,11,13,13,10,12,
	11,14,13, 8, 9, 9,12,12, 9,10,10,12,13, 9,11,10,
	13,12,11,12,12,14,14,11,13,12,15,14, 8, 9, 9,12,
	11, 9,10,10,13,12, 9,11,10,13,12,12,12,12,14,14,
	11,13,12,15,13,11,11,12,13,14,11,12,13,13,14,12,
	13,13,14,15,13,13,14,14,16,14,15,15,16,16,11,12,
	11,14,13,12,13,13,15,14,11,13,12,15,13,14,15,15,
	16,16,13,15,13,16,14, 9,10,11,12,13,11,11,12,13,
	14,11,12,12,13,14,13,13,14,14,16,13,14,14,15,16,
	11,11,12,13,14,12,12,13,14,15,12,13,13,14,15,14,
	14,15,15,17,14,15,15,16,17,11,12,12,14,14,12,13,
	13,14,15,12,13,12,15,15,14,15,15,16,17,14,15,15,
	16,16,13,14,14,15,16,14,14,15,15,17,15,15,15,16,
	17,16,16,17,16,18,16,17,17,18,18,13,14,14,16,15,
	14,15,15,17,16,14,15,15,16,16,16,17,17,18,18,16,
	16,16,17,16, 9,11,10,13,12,11,12,12,14,13,11,12,
	11,15,13,13,14,14,16,15,13,14,13,17,14,11,12,12,
	14,14,12,12,13,15,15,12,13,13,15,14,14,14,15,16,
	16,14,15,15,17,16,11,12,11,14,13,12,13,13,15,14,
	12,13,12,15,13,14,15,15,16,16,14,15,14,17,15,13,
	14,14,15,16,14,15,15,16,17,14,15,15,16,17,16,16,
	16,17,17,16,17,17,18,18,13,15,14,16,15,15,15,15,
	17,16,14,15,14,17,15,16,17,17,18,18,16,17,16,18,
	16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,11,
	10,11,11,13,14,10,12,11,14,13, 7, 9, 9,11,12, 9,
	10,10,12,13, 9,10,10,13,12,11,11,12,13,15,11,12,
	12,15,14, 8, 9, 9,12,11, 9,10,10,13,13, 9,11,10,
	13,12,12,12,12,14,15,11,13,12,15,13,10,11,11,13,
	14,11,12,12,13,15,11,12,12,14,14,13,13,14,14,16,
	14,15,14,16,16,11,12,11,14,13,12,13,13,15,14,11,
	13,12,15,13,14,15,15,16,16,13,14,14,16,14, 8, 9,
	 9,11,12, 9,10,11,12,13, 9,10,10,13,12,11,12,13,
	14,15,11,12,12,15,14, 9, 9,11,11,13,10,10,12,12,
	14,10,10,11,13,14,12,12,13,14,16,12,13,13,15,15,
	 9,11,10,13,12,10,11,11,13,14,10,12,11,14,13,12,
	13,13,15,16,12,13,13,15,15,11,11,13,13,15,12,12,
	14,13,15,13,13,14,14,15,14,14,15,14,17,15,15,15,
	16,16,12,13,12,15,14,13,14,14,15,15,12,14,13,15,
	14,15,15,15,17,17,14,15,14,17,15, 7, 9, 9,12,11,
	 9,10,10,12,12, 9,11,10,13,12,11,12,12,14,14,11,
	13,12,15,14, 9,10,10,12,12,10,10,11,12,13,10,11,
	11,14,13,12,12,13,14,15,12,13,13,16,15, 9,10,10,
	13,12,10,11,11,13,13,10,11,10,14,12,13,13,13,15,
	15,12,13,12,15,14,11,12,12,14,14,12,12,13,14,15,
	13,14,13,15,15,14,13,15,14,16,15,16,15,17,16,12,
	12,12,14,14,13,13,14,15,15,12,13,12,15,14,15,15,
	16,16,17,14,15,14,17,14,10,11,12,13,14,11,12,13,
	14,15,11,12,13,14,15,13,14,15,15,17,14,15,15,16,
	16,11,12,13,12,15,12,12,14,13,16,13,13,14,13,16,
	14,14,16,14,18,15,15,16,16,17,12,13,12,15,15,13,
	14,14,15,16,13,14,13,16,15,15,15,16,17,18,15,15,
	15,17,16,14,14,15,14,17,15,14,16,14,17,15,15,16,
	15,18,16,16,17,16,19,17,17,17,17,18,14,15,15,17,
	16,15,16,16,17,17,15,16,15,18,16,17,17,18,18,18,
	16,17,16,18,17,10,11,11,14,13,11,12,12,15,14,11,
	13,12,15,14,14,15,15,16,16,14,15,15,17,16,11,12,
	12,15,14,12,13,13,15,14,13,14,13,16,14,14,15,15,
	16,16,15,16,15,18,16,11,13,12,15,15,13,14,14,15,
	15,12,14,13,16,15,15,16,16,17,17,15,16,15,17,16,
	14,15,14,16,16,14,15,15,16,16,15,16,15,17,16,16,
	16,17,16,17,17,18,17,19,18,14,15,15,17,16,15,16,
	16,17,17,15,15,15,18,16,17,18,18,18,18,16,17,16,
	19,16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,
	11,10,11,12,13,14,10,11,11,14,13, 8, 9, 9,11,12,
	 9,10,11,12,13, 9,10,10,13,13,11,12,13,13,15,11,
	12,12,15,14, 7, 9, 9,12,11, 9,10,10,12,13, 9,10,
	10,13,12,11,12,12,14,15,11,12,11,14,13,11,11,12,
	13,14,11,12,13,13,15,12,13,13,14,15,13,14,14,14,
	16,14,15,15,16,16,10,11,11,14,13,11,12,12,14,14,
	11,12,12,15,13,14,14,14,16,16,13,14,13,16,14, 7,
	 9, 9,11,12, 9,10,10,12,13, 9,10,10,12,12,11,12,
	13,14,15,11,12,12,14,14, 9,10,10,12,13,10,10,11,
	12,14,10,11,11,13,13,12,12,13,14,15,13,13,13,15,
	15, 9,10,10,12,12,10,11,11,13,14,10,11,10,13,12,
	12,13,13,15,16,12,13,12,15,14,11,12,13,14,14,12,
	12,13,14,15,13,14,13,15,15,14,14,15,14,17,15,16,
	15,17,16,11,12,12,14,14,13,13,13,15,15,12,13,12,
	15,14,15,15,15,16,17,14,15,14,16,14, 8, 9, 9,12,
	11, 9,10,10,12,13, 9,11,10,13,12,11,12,12,14,15,
	11,12,12,15,14, 9,10,11,13,13,10,11,12,13,14,10,
	11,11,14,13,12,13,13,15,15,12,13,13,16,15, 9,11,
	 9,13,11,10,11,10,14,13,10,12,10,14,12,12,13,13,
	15,15,12,13,12,16,14,12,12,13,14,15,12,13,14,14,
	16,13,14,14,15,15,14,14,15,15,17,15,16,15,17,16,
	11,13,11,15,13,13,14,13,15,14,12,14,12,16,13,15,
	15,15,16,16,14,15,14,17,14,10,11,11,13,14,11,12,
	13,14,15,11,12,12,14,15,14,14,15,16,17,14,15,15,
	16,16,11,12,13,14,15,12,13,14,15,16,13,14,14,15,
	16,15,15,16,16,18,15,16,16,17,17,11,12,12,14,15,
	13,13,14,14,16,12,13,13,15,15,15,15,16,16,18,14,
	15,15,16,16,14,15,15,16,17,15,15,16,16,17,15,16,
	16,17,17,16,16,17,16,19,17,18,17,18,18,14,14,15,
	16,16,15,15,16,16,17,14,15,15,16,16,17,17,18,18,
	19,16,17,16,17,16,10,12,11,14,13,11,13,12,15,14,
	11,13,12,15,14,14,15,15,16,16,13,15,14,17,15,12,
	13,13,15,15,13,13,14,15,16,13,14,14,16,16,14,15,
	15,17,17,15,16,16,17,17,11,13,12,15,12,13,14,13,
	16,13,12,14,12,16,13,15,16,15,17,16,14,16,14,18,
	14,14,15,15,16,17,15,15,16,16,17,15,16,16,17,17,
	16,16,17,17,18,17,18,17,18,18,14,15,14,17,14,15,
	16,15,18,15,15,16,15,18,14,17,17,17,18,17,16,17,
	16,19,16, 9,11,11,13,13,10,12,12,14,14,11,12,12,
	15,14,13,14,14,16,16,13,14,14,16,16,10,11,12,14,
	14,11,12,13,14,15,12,13,13,15,15,13,14,15,16,16,
	14,15,15,17,16,11,12,12,15,14,12,13,13,15,15,12,
	13,12,15,15,14,15,15,16,17,14,15,14,17,16,12,13,
	14,15,16,13,13,14,15,16,13,14,15,16,16,14,15,16,
	16,18,15,16,16,18,18,13,14,14,16,15,14,15,15,17,
	16,14,15,15,17,16,16,17,17,18,18,16,17,16,18,17,
	10,12,12,14,14,11,12,13,15,15,12,13,13,15,15,13,
	14,15,16,17,14,15,15,17,16,11,11,13,14,15,12,12,
	14,15,16,13,13,14,15,16,14,14,15,16,17,15,15,16,
	17,17,12,13,12,15,15,13,14,14,16,16,13,14,13,16,
	15,15,16,15,17,17,15,16,15,18,16,13,12,15,14,17,
	14,13,16,14,17,14,14,16,15,18,15,14,17,16,18,16,
	16,17,17,18,14,15,15,17,16,15,16,16,17,17,15,16,
	15,18,16,17,17,17,18,18,16,17,16,19,17,10,11,11,
	14,14,11,12,12,15,15,11,13,12,15,15,14,15,14,16,
	16,14,15,15,17,16,11,12,12,15,14,12,12,13,15,15,
	13,14,13,16,15,14,15,15,16,16,15,16,15,18,17,11,
	13,12,15,15,13,14,13,15,15,12,14,13,16,15,15,16,
	15,17,17,15,16,15,18,16,13,14,13,16,16,14,15,14,
	16,16,14,15,15,17,16,16,16,16,16,18,16,18,17,19,
	18,14,15,15,17,16,15,16,16,17,17,15,15,15,17,16,
	17,17,18,18,19,16,17,16,18,16,12,13,13,15,16,13,
	14,14,16,17,13,14,14,16,16,15,15,16,17,18,15,16,
	16,18,17,13,13,14,14,17,14,14,15,15,17,14,14,15,
	16,17,15,15,17,16,18,16,17,17,18,18,13,14,14,17,
	16,14,15,15,17,17,14,15,14,17,16,16,17,17,18,18,
	16,17,16,18,17,15,14,16,13,18,16,15,17,14,19,16,
	16,17,15,18,17,16,18,15,19,18,18,18,17,19,15,16,
	16,18,17,16,17,17,18,18,16,17,16,19,17,18,19,18,
	19,19,17,18,17,20,18,11,12,12,15,15,13,13,14,15,
	16,13,14,13,16,15,15,16,16,17,17,15,16,16,18,17,
	12,14,13,16,15,13,13,14,15,16,14,15,14,17,16,16,
	16,16,16,17,16,17,17,19,17,12,13,14,16,16,14,15,
	15,16,17,13,15,13,17,15,16,17,17,18,18,16,17,16,
	18,16,15,16,15,17,16,15,15,15,17,17,16,17,16,18,
	17,17,16,17,16,18,18,19,18,20,18,15,16,16,17,17,
	16,17,17,18,18,15,16,15,18,17,18,18,19,19,19,17,
	18,16,19,16, 9,11,11,13,13,11,12,12,14,15,10,12,
	12,14,14,13,14,14,16,16,13,14,14,16,16,11,12,12,
	14,14,12,12,13,15,15,12,13,13,15,15,14,15,15,16,
	17,14,15,15,16,16,10,12,11,14,14,12,13,13,15,15,
	11,13,12,15,14,14,15,15,16,17,13,15,14,17,16,13,
	14,14,15,16,14,15,15,16,17,14,15,15,16,17,16,16,
	17,17,18,16,17,17,18,18,12,14,13,16,15,13,15,14,
	17,16,13,14,13,17,15,15,16,16,18,18,15,16,15,18,
	16,10,11,11,14,14,11,12,13,14,15,11,12,12,15,15,
	14,15,15,16,17,14,15,15,16,16,11,12,13,15,15,12,
	13,14,15,16,13,14,14,15,16,15,15,16,16,18,15,15,
	16,17,17,11,12,12,14,15,13,13,14,15,16,12,13,13,
	15,15,15,15,16,17,18,14,15,15,17,16,14,15,15,16,
	17,15,15,16,16,17,15,16,16,17,17,16,16,17,16,19,
	17,17,18,19,18,13,13,14,16,16,14,15,16,17,17,14,
	14,15,16,16,16,16,17,18,18,16,16,16,18,16,10,12,
	12,14,14,12,13,13,15,15,11,13,12,15,15,14,15,15,
	16,17,13,15,14,17,16,12,13,13,15,15,13,13,14,15,
	16,13,14,14,16,16,15,15,16,17,18,15,15,16,17,17,
	11,13,12,15,14,13,14,13,16,15,12,14,12,16,14,15,
	16,15,17,17,14,16,14,17,16,14,15,15,16,17,15,15,
	16,16,18,15,16,16,17,17,16,17,17,17,19,17,17,17,
	18,18,13,15,12,17,14,14,16,14,17,15,14,15,13,17,
	14,16,17,16,18,17,15,17,14,19,15,11,12,12,15,15,
	13,13,14,15,16,13,14,13,16,15,15,16,16,17,18,15,
	16,16,17,17,12,14,13,16,16,13,13,15,15,17,14,15,
	15,17,16,16,16,17,16,19,16,17,17,18,18,12,13,14,
	15,16,14,14,15,16,17,13,14,13,16,15,16,17,17,18,
	19,15,16,16,17,16,15,16,16,18,17,15,15,16,17,18,
	16,17,17,18,18,16,16,18,16,19,18,19,19,20,19,15,
	15,16,16,17,16,16,17,17,18,15,15,15,17,16,18,18,
	19,18,20,17,17,16,18,16,12,13,13,16,15,13,14,14,
	16,16,13,14,14,16,16,15,16,16,17,18,15,16,15,18,
	17,13,14,14,16,16,14,15,15,16,17,14,15,15,17,17,
	16,17,17,18,18,16,17,17,18,18,13,14,13,17,14,14,
	15,14,17,16,14,15,14,17,15,16,17,17,18,18,15,17,
	15,19,15,16,16,16,17,18,16,16,17,17,19,16,17,17,
	18,19,17,17,18,18,20,18,18,18,19,19,15,16,14,18,
	13,16,17,16,19,15,16,17,15,19,14,18,18,18,19,17,
	17,18,16,20,15,
};

static const static_codebook _44p6_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p6_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p6_p2_0,
	0
};

static const long _vq_quantlist__44p6_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p3_0[] = {
	 1, 5, 5, 5, 7, 8, 5, 8, 7, 5, 7, 8, 8, 8,10, 8,
	10,10, 5, 8, 7, 8,10,10, 8,10, 8, 6, 8, 9, 8,10,
	12, 9,11,11, 9,10,11,11,11,13,12,13,13, 9,11,11,
	11,13,13,11,13,12, 6, 9, 8, 9,11,11, 8,12,10, 9,
	11,11,11,12,13,11,13,13, 9,11,10,11,13,13,11,13,
	11, 5, 9, 9, 8,11,11, 9,12,11, 8,10,11,10,11,13,
	11,13,13, 9,11,11,11,13,13,11,13,12, 8,10,11,10,
	12,13,10,13,12,10,10,13,11,11,14,12,13,14,11,13,
	12,13,14,14,12,14,12, 8,11,10,11,12,13,11,14,12,
	10,13,12,12,12,13,13,15,14,11,12,13,13,14,15,12,
	14,12, 5, 9, 9, 9,11,12, 8,11,11, 9,11,11,11,12,
	13,11,13,13, 8,11,10,11,13,13,10,13,11, 8,10,11,
	11,12,14,11,13,12,11,13,12,12,12,14,13,15,14,10,
	12,13,13,14,15,12,13,12, 8,11,10,10,12,13,10,13,
	12,11,12,13,12,12,14,13,14,14,10,13,10,12,14,13,
	11,14,11,
};

static const static_codebook _44p6_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p6_p3_0,
	0
};

static const long _vq_quantlist__44p6_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p3_1[] = {
	 5, 7, 7, 6, 7, 7, 6, 7, 7, 6, 7, 7, 7, 8, 8, 7,
	 8, 8, 6, 7, 7, 7, 8, 8, 7, 8, 8, 7, 7, 8, 7, 8,
	 8, 7, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8,
	 8, 9, 9, 8, 9, 8, 7, 8, 7, 7, 8, 8, 7, 8, 8, 8,
	 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9,
	 8, 6, 8, 8, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 9,
	 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8, 8,
	 8, 9, 8, 9, 9, 8, 8, 9, 8, 9, 9, 9, 9, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9, 8,
	 8, 8, 8, 8, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9, 9,
	 9, 9, 6, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8,
	 8, 8, 9, 8, 9, 8, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8,
	 8, 8, 9, 9, 9, 8, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9,
	 8, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p6_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p6_p3_1,
	0
};

static const long _vq_quantlist__44p6_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p4_0[] = {
	 2, 5, 5, 5, 7, 8, 5, 8, 7, 5, 7, 7, 7, 7, 9, 7,
	 9, 9, 5, 7, 7, 8, 9, 9, 7, 9, 7, 6, 8, 8, 8, 9,
	10, 8, 9, 9, 8, 9,10, 9, 9,11,10,11,11, 8, 9, 9,
	10,11,11, 9,11,10, 6, 8, 8, 8, 9, 9, 8,10, 9, 8,
	 9, 9, 9,10,11,10,11,10, 8,10, 9,10,11,11, 9,11,
	 9, 6, 8, 8, 7, 9, 9, 8,10, 9, 7, 9, 9, 9, 9,10,
	 9,10,10, 8, 9, 9, 9,10,10, 9,11,10, 7, 9, 9, 8,
	10,10, 9,10,10, 9, 9,10,10,10,11,10,11,11, 9,10,
	10,10,11,11,10,11,10, 7, 9, 9, 9, 9,10, 9,10, 9,
	 8,10, 9, 9, 9,11,10,11,11, 9,10,10,10,11,11, 9,
	11, 9, 6, 8, 8, 8, 9,10, 7, 9, 9, 8, 9, 9, 9,10,
	10, 9,10,10, 7, 9, 9, 9,10,10, 9,10, 9, 7, 9, 9,
	 9, 9,10, 9,10, 9, 9,10,10, 9, 9,11,10,11,11, 8,
	 9,10,10,11,11, 9,11, 9, 7, 9, 9, 9,10,10, 8,10,
	10, 9,10,10,10,10,11,10,11,11, 9,10, 9,10,11,11,
	10,11,10,
};

static const static_codebook _44p6_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p6_p4_0,
	0
};

static const long _vq_quantlist__44p6_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p6_p4_1[] = {
	 6, 8, 8,10,10, 8, 9, 9,10,11, 8,10, 9,11,10, 9,
	10,10,11,11, 9,10,10,11,11, 8, 9, 9,10,10, 9, 9,
	10,11,11,10,10,10,11,11,10,11,11,11,11,10,11,11,
	11,11, 8, 9, 9,11,10,10,10,10,11,11, 9,10, 9,11,
	11,10,11,11,11,11,10,11,10,11,11,10,10,11,11,11,
	10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
	11,11,11,11,10,11,10,11,11,11,11,11,11,11,10,11,
	11,11,11,11,11,11,11,11,11,11,11,11,11, 8, 9,10,
	11,11,10,10,11,11,11,10,10,10,11,11,10,11,11,12,
	12,10,11,11,12,12,10,10,11,11,11,10,10,11,11,12,
	11,11,11,12,12,11,11,12,12,12,11,11,12,12,12,10,
	10,10,11,11,11,11,11,12,12,10,11,11,12,12,11,12,
	12,12,12,11,12,11,12,12,11,11,11,11,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,11,12,11,11,12,12,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12, 8,10, 9,11,11,10,
	10,10,11,11,10,11,10,11,11,10,11,11,12,12,10,11,
	11,12,12,10,10,10,11,11,10,11,11,12,12,11,11,11,
	12,12,11,11,12,12,12,11,12,12,12,12,10,11,10,11,
	11,11,11,11,12,12,10,11,10,12,11,11,12,11,12,12,
	11,12,11,12,12,11,11,11,12,12,11,11,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,
	11,12,11,11,12,12,12,12,11,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,10,11,11,11,12,11,11,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	11,11,12,12,12,11,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,13,11,12,11,12,12,12,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,13,12,13,12,12,12,12,13,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,13,13,12,
	12,12,13,12,10,11,11,12,11,11,11,12,12,12,11,12,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,12,11,12,12,12,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,12,12,12,13,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,13,12,13,12,13,
	12, 8,10,10,11,11,10,10,11,11,11,10,11,10,11,11,
	10,11,11,12,12,10,11,11,12,12, 9,10,11,11,11,10,
	10,11,12,12,10,11,11,12,12,11,11,12,12,12,11,12,
	12,12,12,10,11,10,11,11,11,11,11,12,12,10,11,11,
	12,12,11,12,12,12,12,11,12,11,12,12,11,11,11,12,
	12,11,11,12,12,12,11,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12, 9,10,
	10,11,11,10,11,11,12,12,10,11,11,12,12,11,11,12,
	12,12,11,12,12,12,12,10,11,11,12,12,11,11,12,12,
	12,11,11,12,12,12,11,11,12,12,12,12,12,12,12,12,
	10,11,11,12,12,11,12,12,12,12,11,12,11,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12, 9,10,10,11,11,
	10,11,11,12,12,10,11,11,12,11,11,12,12,12,12,11,
	12,12,12,12,10,11,11,12,12,11,11,11,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,10,11,11,
	12,12,11,12,12,12,12,11,12,11,12,12,12,12,12,12,
	12,12,12,12,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,11,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,13,12,12,13,13,13,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,12,12,12,12,13,13,12,13,
	12,13,13,12,12,12,12,12,12,12,12,12,13,12,13,13,
	13,13,12,13,13,13,13,13,13,13,13,13,12,12,12,12,
	12,12,12,13,13,13,12,13,12,13,13,12,13,13,13,13,
	12,13,13,13,13,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	12,13,12,12,12,13,13,11,12,12,12,12,12,12,12,12,
	13,12,12,12,13,12,12,13,12,13,13,12,13,12,13,13,
	12,12,12,12,12,12,12,13,13,13,12,12,13,13,13,12,
	13,13,12,13,13,13,13,13,13,12,12,12,12,12,12,13,
	12,13,13,12,13,12,13,12,12,13,13,13,13,12,13,13,
	13,13, 8,10,10,11,11,10,10,11,11,11, 9,11,10,11,
	11,10,11,11,12,12,10,11,11,12,12,10,10,11,11,11,
	10,11,11,12,12,11,11,11,12,12,11,11,12,12,12,11,
	12,12,12,12, 9,11,10,11,11,10,11,11,12,12,10,11,
	10,12,12,11,12,12,12,12,11,12,11,12,12,11,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12, 9,
	10,10,11,11,10,11,11,12,12,10,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,10,11,11,12,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,10,11,11,12,12,11,11,12,12,12,11,11,11,12,12,
	12,12,12,12,12,11,12,12,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12, 9,10,10,11,
	11,10,11,11,12,12,10,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,10,11,11,12,12,11,11,12,12,12,11,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,10,11,
	11,12,12,11,12,11,12,12,11,12,11,12,12,12,12,12,
	12,12,11,12,11,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,13,
	13,12,12,12,13,13,12,12,13,13,13,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,12,13,13,12,
	12,12,13,12,12,12,12,12,12,12,12,13,13,13,12,12,
	13,13,13,12,13,13,12,13,12,13,13,13,13,12,12,12,
	12,12,12,12,13,13,13,12,12,12,13,12,12,13,13,13,
	13,12,13,13,13,13,11,11,11,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,12,
	13,13,13,12,12,12,13,13,11,12,12,12,12,12,12,12,
	13,12,12,12,12,13,12,12,13,12,13,13,12,13,12,13,
	12,12,12,12,12,12,12,12,13,13,13,12,13,13,13,13,
	12,13,13,13,13,13,13,13,13,13,12,12,12,12,12,12,
	13,12,13,12,12,13,12,13,12,13,13,13,13,13,12,13,
	13,13,13,10,11,11,12,12,11,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,11,11,12,12,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,13,13,
	12,12,12,13,13,11,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,12,
	12,12,12,12,12,12,12,13,12,12,12,12,13,12,12,13,
	12,13,12,13,13,13,13,12,12,12,12,12,12,12,12,13,
	12,12,12,12,13,12,12,13,13,13,13,12,13,12,13,13,
	11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,12,
	12,12,12,13,12,12,12,13,12,11,12,12,12,12,12,12,
	12,12,13,12,12,12,12,13,12,12,13,13,13,12,12,13,
	13,13,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	12,12,13,12,13,13,12,13,12,13,13,12,12,12,12,12,
	12,12,13,12,13,12,12,13,13,13,12,12,13,13,13,13,
	13,13,13,13,12,12,12,12,12,12,13,13,13,13,12,13,
	12,13,12,12,13,13,13,13,12,13,13,13,13,11,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,13,11,12,12,12,12,12,12,12,12,13,
	12,12,12,13,13,12,12,13,13,13,12,12,13,13,13,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,13,
	12,13,13,12,13,12,13,13,12,12,12,12,12,12,12,12,
	12,13,12,13,12,13,13,12,13,13,13,13,12,13,13,13,
	13,12,12,12,12,12,12,13,12,13,13,12,12,12,13,13,
	12,13,13,13,13,12,13,12,13,13,11,12,12,12,12,11,
	12,12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,12,13,12,12,12,13,13,11,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,
	12,13,12,13,13,12,12,12,12,12,12,12,13,12,13,12,
	12,13,12,13,12,12,13,12,13,12,13,13,13,13,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,12,12,13,13,
	13,13,12,13,12,13,12,11,11,11,12,12,11,12,12,12,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,13,13,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,12,12,12,
	13,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,13,12,12,12,13,12,12,12,12,12,12,12,12,
	12,12,12,13,12,12,12,12,13,12,12,13,12,13,12,12,
	13,12,13,12,10,11,11,12,12,11,12,12,12,12,11,12,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,12,12,12,13,13,11,12,11,12,12,12,12,12,12,12,
	11,12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,
	12,12,12,12,12,12,12,12,13,12,12,12,12,13,12,13,
	13,12,13,12,13,13,13,13,12,12,12,12,12,12,12,12,
	13,13,12,12,12,13,12,12,13,13,13,13,12,13,12,13,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,13,13,12,12,12,13,12,11,12,12,12,12,12,
	12,12,12,13,12,12,12,13,13,12,12,13,13,13,12,12,
	13,13,13,11,12,12,12,12,12,12,12,13,13,12,12,12,
	13,12,12,13,12,13,13,12,12,12,13,13,12,12,12,12,
	12,12,12,13,13,13,12,12,13,13,13,12,12,13,13,13,
	12,13,13,13,13,12,12,12,12,12,12,12,13,13,13,12,
	12,12,13,12,12,13,13,13,13,12,13,13,13,13,11,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,
	12,13,12,12,12,13,13,11,12,12,12,12,12,12,12,12,
	13,12,12,12,13,13,12,12,13,13,13,12,12,13,13,13,
	11,12,12,12,12,12,12,12,13,12,12,12,12,13,12,12,
	13,12,13,13,12,13,12,13,13,12,12,12,12,12,12,12,
	12,13,13,12,13,12,13,13,12,13,13,13,13,13,13,13,
	13,13,12,12,12,12,12,12,13,12,13,13,12,13,12,13,
	12,12,13,13,13,13,12,13,12,13,13,11,11,11,12,12,
	11,12,12,12,12,11,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,13,12,12,12,13,13,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	13,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	12,12,12,12,13,12,12,12,12,13,12,12,13,12,13,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,12,12,12,13,12,12,12,11,12,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,12,12,12,12,12,12,12,12,13,12,12,12,12,12,
	12,12,12,13,13,12,12,12,13,13,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,13,13,12,12,
	12,13,12,12,12,12,12,12,12,12,12,12,13,12,12,12,
	13,13,12,12,13,12,13,12,13,13,13,13,12,12,12,12,
	12,12,12,12,13,12,12,12,12,13,12,12,13,12,13,13,
	12,13,12,13,12,
};

static const static_codebook _44p6_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p6_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p6_p4_1,
	0
};

static const long _vq_quantlist__44p6_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p6_p5_0[] = {
	 2, 6, 6,10,10, 5, 7, 8,11,12, 5, 8, 7,12,11, 9,
	11,11,13,15, 9,11,11,15,13, 6, 7, 8,11,11, 7, 7,
	 9,11,13, 8, 9, 9,13,12,11,11,12,12,15,11,12,12,
	15,14, 6, 8, 7,11,11, 8, 9, 9,12,13, 7, 9, 7,13,
	11,11,12,12,14,15,11,12,11,15,12,10,11,11,12,14,
	10,11,12,12,15,12,13,13,14,15,13,12,14,12,16,15,
	15,15,16,16,10,11,11,14,12,12,13,13,15,14,10,12,
	11,15,12,15,15,15,16,17,13,14,12,17,12, 6, 8, 8,
	12,12, 8, 9,10,13,13, 8, 9, 9,13,13,12,12,13,15,
	16,12,13,13,16,15, 8, 9,10,12,13, 9, 9,11,13,14,
	10,11,11,14,14,13,13,14,15,16,13,14,14,16,16, 8,
	10, 9,13,13,10,11,11,14,14, 9,10,10,14,13,13,14,
	14,16,17,13,13,13,16,15,12,13,13,14,16,13,13,14,
	14,16,14,14,14,16,16,15,15,16,15,18,16,17,17,18,
	18,12,13,13,15,15,14,14,14,16,16,13,14,13,16,15,
	16,16,17,18,18,15,16,15,18,15, 6, 8, 8,12,12, 8,
	 9, 9,13,13, 8,10, 9,13,13,12,13,13,15,16,12,13,
	12,16,15, 8, 9,10,13,13, 9,10,10,13,14,10,11,11,
	14,14,13,13,13,15,16,13,14,14,17,16, 8,10, 9,13,
	13,10,11,11,14,14, 9,11, 9,14,13,13,14,14,16,16,
	13,14,13,16,14,12,13,13,15,16,13,13,14,15,16,14,
	14,14,16,16,15,15,16,15,18,17,17,17,18,18,12,13,
	13,16,14,14,14,14,16,16,13,14,13,16,14,16,17,17,
	18,18,15,16,15,18,15,11,12,13,14,16,13,13,14,15,
	17,13,14,14,16,17,16,16,17,17,19,16,17,17,18,19,
	13,13,14,16,16,14,14,15,16,17,14,15,15,17,17,17,
	16,17,17,19,17,17,18,19,19,13,14,14,16,16,14,14,
	15,17,18,14,15,14,17,17,17,17,18,18,19,17,17,17,
	18,19,16,16,16,17,18,17,17,17,18,19,17,17,17,18,
	19,18,18,19,18,20,19,20,19,21,20,16,17,17,18,18,
	17,17,18,19,19,17,17,17,19,18,19,19,19,19,20,19,
	19,19,20,19,11,13,12,16,14,13,14,14,17,16,13,14,
	13,17,15,16,17,17,18,18,16,17,16,19,17,13,14,14,
	16,16,14,14,14,17,17,14,15,15,17,16,17,17,17,19,
	19,17,18,17,19,18,13,14,13,17,16,14,15,15,17,17,
	14,15,14,18,16,17,17,17,19,19,17,17,16,19,17,16,
	17,17,18,19,17,17,17,18,18,17,18,17,19,18,18,19,
	18,19,19,19,20,19,20,20,16,17,16,18,17,17,17,17,
	18,18,17,18,17,19,17,19,19,19,19,20,18,19,19,20,
	18, 6, 8, 8,12,12, 8, 9, 9,13,13, 8,10, 9,13,13,
	11,13,13,15,16,12,13,13,16,15, 8, 9, 9,13,13, 9,
	 9,10,13,14,10,11,11,14,14,12,12,13,14,16,13,14,
	14,17,16, 8,10, 9,13,13,10,11,11,14,14, 9,11,10,
	14,13,13,14,14,16,16,13,14,13,16,15,12,13,13,14,
	16,12,13,14,14,16,13,14,14,16,16,15,14,16,15,18,
	16,17,17,18,17,12,13,13,16,15,14,14,14,16,16,13,
	14,13,16,15,16,16,17,17,17,15,16,15,18,15, 7, 9,
	 9,13,13, 9, 9,11,13,14, 9,10,10,14,13,12,13,14,
	15,16,12,14,13,17,15, 9, 9,10,13,14,10, 9,11,13,
	15,11,11,11,14,14,13,12,14,14,17,14,14,14,17,16,
	 9,10,10,14,13,11,11,11,14,14,10,11,10,15,13,14,
	14,14,16,17,13,14,13,17,14,13,13,14,14,16,13,13,
	14,14,17,14,14,14,16,16,15,14,16,15,18,17,17,17,
	18,18,13,14,13,16,15,14,14,15,17,16,13,14,13,17,
	15,17,16,17,17,17,15,16,14,18,14, 7, 9, 9,13,13,
	 9,10,10,13,14, 9,11,10,14,13,13,14,14,16,16,13,
	14,14,17,15, 9,10,10,14,13, 9,10,11,13,14,11,12,
	11,15,14,13,13,14,14,16,14,15,15,17,17, 9,10,10,
	14,14,11,12,12,14,15,10,11,10,15,13,14,15,15,17,
	17,14,15,13,17,14,13,14,13,16,16,13,13,14,15,16,
	14,15,15,17,17,15,14,16,15,18,17,18,17,20,18,13,
	14,14,16,16,15,15,15,17,17,13,14,13,17,15,17,17,
	18,18,18,15,16,14,19,14,12,13,13,15,16,13,13,15,
	16,17,13,14,14,16,16,15,15,17,17,19,16,17,17,19,
	18,13,13,14,15,17,14,13,15,15,17,14,15,15,16,17,
	16,15,18,16,19,17,17,17,18,19,13,14,14,17,16,14,
	15,15,17,17,14,15,14,17,16,17,17,17,18,19,16,17,
	16,19,17,16,16,17,16,18,16,16,17,16,19,17,17,18,
	18,19,18,17,18,17,21,19,19,19,20,19,16,17,17,18,
	18,17,17,18,18,19,16,17,16,18,18,19,19,19,19,20,
	18,18,17,20,18,11,13,13,16,15,13,14,14,16,17,13,
	15,14,17,16,16,17,17,18,18,17,17,17,19,18,13,14,
	13,17,16,14,13,14,16,17,15,16,15,18,16,17,16,17,
	17,19,18,18,18,20,18,13,14,14,16,17,15,15,15,17,
	18,14,15,14,18,16,18,18,18,19,20,17,18,16,20,17,
	16,17,16,18,18,16,16,17,18,18,17,18,18,19,18,18,
	17,19,17,20,19,20,19,22,20,16,16,17,18,18,18,17,
	17,19,19,16,17,16,18,17,19,20,19,22,21,18,19,18,
	21,17, 6, 8, 8,12,12, 8, 9,10,13,13, 8, 9, 9,13,
	13,12,13,13,15,16,11,13,13,16,15, 8, 9,10,13,13,
	 9,10,11,13,14,10,11,11,14,14,13,13,14,15,16,13,
	14,14,16,16, 8, 9, 9,13,13,10,11,11,14,14, 9,10,
	 9,14,13,13,14,14,16,17,12,14,12,16,14,12,13,13,
	15,16,13,13,14,15,16,13,14,14,15,17,15,15,16,15,
	18,16,16,17,17,17,12,13,13,16,14,13,14,14,16,16,
	12,14,13,16,14,16,17,17,18,18,15,15,14,18,14, 7,
	 9, 9,13,13, 9,10,11,13,14, 9,10,10,14,13,13,14,
	14,15,17,13,14,14,16,15, 9,10,10,14,14,10,10,11,
	13,15,11,12,12,15,14,14,13,15,14,17,14,15,15,17,
	17, 9,10,10,13,14,11,11,12,14,15, 9,11,10,14,13,
	14,15,15,16,18,13,14,13,16,14,13,14,14,16,16,13,
	13,14,15,17,15,15,15,16,17,15,14,16,15,18,17,17,
	18,19,18,13,14,14,16,16,14,15,15,17,17,13,14,13,
	16,15,17,17,18,18,18,15,16,14,18,15, 7, 9, 9,13,
	13, 9,10,10,13,14, 9,11,10,14,13,12,13,14,15,16,
	12,14,13,16,15, 9,10,10,13,14,10,10,11,13,14,11,
	11,11,15,14,13,13,14,14,16,14,14,14,17,16, 9,10,
	 9,14,13,11,11,11,14,14,10,11, 9,15,13,14,14,14,
	16,16,13,14,12,17,14,13,13,14,15,16,13,13,14,15,
	16,14,15,14,16,17,15,14,16,14,18,16,17,17,18,18,
	13,14,13,16,14,14,14,14,16,16,13,14,13,17,14,17,
	17,17,18,18,15,16,14,18,15,11,13,13,16,16,13,14,
	15,16,17,13,14,14,17,16,16,17,17,18,19,17,17,17,
	19,18,13,14,14,17,17,13,13,15,16,18,15,15,15,17,
	17,17,16,18,17,20,18,17,18,19,19,13,14,14,16,17,
	15,15,16,16,18,14,15,14,16,16,17,17,18,18,20,17,
	18,16,18,17,16,17,16,19,18,16,16,17,18,19,18,18,
	18,19,19,18,17,18,17,21,20,19,19,21,21,16,16,17,
	18,18,17,17,18,19,19,16,17,16,19,18,20,20,20,19,
	21,18,18,17,20,18,12,13,13,16,15,13,14,14,16,16,
	13,14,13,17,16,16,17,17,18,18,15,17,15,19,17,13,
	14,14,16,17,14,14,15,16,17,14,15,15,17,17,16,16,
	17,17,18,17,17,17,19,19,13,14,13,17,15,14,15,15,
	17,16,14,15,13,17,15,17,18,17,19,18,16,17,15,20,
	16,16,17,17,18,18,16,16,17,18,18,17,18,17,19,18,
	17,17,18,18,20,19,20,19,20,19,16,16,16,19,16,17,
	17,17,19,18,16,17,16,19,16,19,19,19,19,19,18,19,
	17,19,17,11,13,13,16,16,13,14,14,17,17,13,14,14,
	17,17,15,17,17,19,19,16,18,17,20,19,12,14,14,17,
	17,13,14,15,17,18,14,15,15,17,18,16,16,17,18,20,
	17,18,18,20,18,13,14,14,17,17,14,15,15,17,18,14,
	15,15,17,17,17,18,17,19,19,17,18,17,19,19,15,16,
	16,18,18,15,16,17,18,19,16,17,17,19,19,17,17,18,
	18,21,18,19,19,21,19,16,17,17,18,18,17,17,18,19,
	19,17,18,17,19,19,19,19,19,20,20,18,19,18,21,19,
	12,13,13,16,16,13,14,14,16,17,13,15,14,17,16,15,
	16,17,17,19,16,17,17,19,18,13,13,14,16,17,14,13,
	15,16,17,14,15,15,17,17,15,15,17,17,20,17,17,18,
	19,18,13,14,14,17,16,15,15,15,17,18,14,15,14,17,
	16,17,17,17,18,18,16,17,16,19,17,16,15,17,17,19,
	16,15,17,16,19,17,16,17,18,19,17,16,19,16,20,19,
	18,19,19,19,16,17,17,18,18,17,17,17,18,19,16,17,
	16,19,18,20,19,19,20,19,18,18,17,20,17,11,13,13,
	16,16,13,14,15,16,17,14,15,14,18,16,17,17,17,18,
	21,17,18,17,20,19,13,14,14,17,16,13,14,15,16,18,
	15,16,15,18,17,17,16,17,17,19,17,18,18,20,19,13,
	14,14,16,17,15,15,16,17,18,14,15,14,18,17,17,18,
	18,19,20,17,18,16,19,17,16,17,15,19,18,16,16,16,
	18,18,17,18,17,20,19,18,17,18,17,20,20,20,19,22,
	20,16,17,17,18,19,18,18,18,19,20,16,17,16,19,18,
	20,19,19,20,20,18,19,17,20,17,13,14,14,16,17,14,
	14,16,16,18,14,16,15,17,16,16,16,17,17,18,17,17,
	16,19,18,14,14,15,16,17,14,14,16,16,18,16,16,16,
	17,17,16,15,17,16,19,18,18,18,19,19,14,15,15,17,
	17,15,16,16,17,18,14,16,14,18,16,17,17,18,18,19,
	16,17,16,19,17,16,16,17,16,18,16,16,17,16,19,18,
	18,18,17,18,17,16,18,16,20,19,19,19,19,19,16,17,
	17,18,18,17,17,18,19,19,16,17,16,19,17,18,19,19,
	19,20,17,18,16,20,16,11,14,13,17,17,14,14,16,16,
	18,14,16,14,19,16,18,18,19,18,19,18,19,18,21,18,
	13,15,14,18,16,14,14,16,16,18,16,17,16,19,17,18,
	16,19,17,20,19,19,19,21,19,13,14,15,17,18,17,16,
	17,17,19,14,16,14,18,16,20,19,19,20,21,18,19,16,
	21,17,17,18,16,19,17,16,16,17,18,18,19,19,18,21,
	18,17,17,18,17,20,20,20,20,22,20,17,17,18,18,20,
	19,19,19,18,20,16,17,17,19,19,21,21,21,20,21,17,
	19,17,23,17,11,13,13,16,16,13,14,14,17,17,13,14,
	14,17,17,16,17,17,19,20,15,16,16,19,19,13,14,14,
	16,17,14,15,15,17,18,14,15,15,17,17,17,17,18,19,
	19,17,17,18,19,19,13,14,14,17,16,14,15,15,17,17,
	13,15,14,18,17,17,18,18,19,20,16,17,16,19,18,16,
	16,17,18,18,17,17,17,18,19,17,18,17,19,19,19,19,
	19,19,20,19,20,19,20,20,15,16,16,18,17,16,17,17,
	20,18,15,16,16,19,17,19,19,19,20,20,17,18,17,21,
	17,11,13,13,16,16,13,14,15,16,17,13,15,14,17,16,
	17,17,18,18,20,17,17,17,19,19,13,14,14,17,17,14,
	14,15,17,18,15,15,15,18,17,17,17,18,17,20,18,18,
	17,20,18,13,14,14,16,17,15,15,16,17,18,14,15,13,
	17,17,17,18,18,19,20,17,17,16,19,17,16,17,17,18,
	18,16,16,17,18,18,18,18,18,19,19,18,17,19,18,21,
	19,20,20,20,20,16,15,17,18,18,17,17,18,18,20,16,
	16,16,18,17,20,19,20,21,22,17,18,17,20,17,12,13,
	13,16,16,13,14,15,16,17,13,14,14,17,16,16,17,18,
	18,19,15,16,16,19,18,13,14,14,16,17,14,14,15,16,
	17,14,15,15,17,17,16,16,17,17,19,17,17,17,19,18,
	13,14,13,17,16,14,15,15,17,17,13,15,13,17,16,17,
	17,17,19,19,15,17,15,19,17,16,17,17,18,18,16,16,
	17,17,19,17,18,17,19,19,18,17,19,17,19,19,19,19,
	20,19,15,17,15,19,16,17,17,16,19,18,16,17,15,18,
	16,19,19,19,20,19,17,19,16,19,16,11,14,14,17,17,
	15,14,16,16,18,15,16,14,18,16,18,18,19,18,21,18,
	19,18,20,18,13,15,14,18,17,14,14,16,16,18,16,17,
	16,19,17,17,17,19,17,22,19,19,19,21,19,13,14,15,
	17,18,17,16,17,17,19,14,16,14,18,16,19,19,19,20,
	21,18,18,16,20,17,17,18,16,19,18,15,17,17,19,19,
	19,19,18,21,19,18,17,20,17,21,22,21,20,21,21,17,
	16,19,18,20,19,18,19,18,20,16,17,16,19,18,21,20,
	21,19,23,18,19,16,20,17,13,14,14,17,16,14,14,15,
	16,18,14,16,14,17,16,16,16,17,17,19,16,17,16,19,
	17,14,15,15,17,17,14,14,16,16,17,15,16,16,18,17,
	16,16,17,17,19,17,18,17,19,18,14,15,14,17,16,16,
	16,16,17,17,14,16,14,17,16,18,18,18,18,19,16,17,
	15,19,16,17,17,17,18,18,16,15,17,17,18,18,18,18,
	19,19,17,16,18,16,19,19,19,19,19,19,16,17,16,19,
	16,18,18,17,19,18,16,17,16,19,16,19,19,20,19,19,
	17,18,16,20,16,
};

static const static_codebook _44p6_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p6_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p6_p5_0,
	0
};

static const long _vq_quantlist__44p6_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p6_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p6_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p6_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p6_p5_1,
	0
};

static const long _vq_quantlist__44p6_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p6_0[] = {
	 1, 5, 5, 5, 7, 9, 5, 9, 7, 5, 7, 8, 7, 7,10, 9,
	10,10, 5, 8, 7, 9,10,10, 7,10, 7, 6, 9, 9, 9,10,
	12, 9,11,11, 9,10,11,11,11,13,12,13,13, 9,11,11,
	12,13,13,11,13,11, 6, 9, 9, 9,11,11, 9,12,10, 9,
	11,11,11,11,13,12,13,13, 9,11,10,12,13,13,11,13,
	11, 6, 9, 9, 9,11,12, 9,12,11, 9,10,11,10,10,13,
	12,13,13, 9,11,11,12,13,12,11,13,11, 7, 9,10, 9,
	10,12,10,12,11,10,10,12,10,10,12,12,12,13,10,11,
	11,12,12,13,10,12,10, 7,10,10,11,11,14,11,14,11,
	10,12,11,11,11,14,14,14,14,10,11,12,14,14,14,11,
	14,11, 6, 9, 9, 9,11,12, 9,12,11, 9,11,11,11,11,
	13,12,12,13, 9,11,10,12,13,13,10,13,10, 7,10,10,
	11,11,14,11,14,11,10,12,11,11,11,14,14,15,14,10,
	11,12,13,14,15,11,14,11, 7,10, 9,10,11,12, 9,12,
	10,10,11,11,10,10,12,12,13,12, 9,12,10,12,13,12,
	10,12,10,
};

static const static_codebook _44p6_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p6_p6_0,
	0
};

static const long _vq_quantlist__44p6_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p6_1[] = {
	 2, 6, 6, 6, 7, 8, 6, 8, 7, 6, 7, 7, 7, 7, 8, 7,
	 8, 8, 6, 7, 7, 7, 8, 8, 7, 8, 7, 6, 8, 8, 8, 9,
	 9, 8, 9, 9, 8, 9, 9, 9, 9,10, 9,10,10, 8, 9, 9,
	 9,10,10, 9,10, 9, 6, 8, 8, 8, 9, 9, 8, 9, 9, 8,
	 9, 9, 9, 9,10, 9,10,10, 8, 9, 9, 9,10, 9, 9,10,
	 9, 6, 8, 8, 8, 9, 9, 8, 9, 9, 8, 9, 9, 9, 9,10,
	 9, 9,10, 8, 9, 9, 9,10, 9, 9,10, 9, 7, 8, 8, 8,
	 9, 9, 8, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8, 9,
	 9, 9,10, 9, 9, 9, 9, 7, 9, 9, 9, 9,10, 9,10, 9,
	 9, 9, 9, 9, 9,10,10,10,10, 9, 9, 9,10,10,10, 9,
	10, 9, 6, 8, 8, 8, 9, 9, 8, 9, 9, 8, 9, 9, 9, 9,
	10, 9,10,10, 8, 9, 9, 9,10, 9, 9,10, 9, 7, 9, 9,
	 9, 9,10, 9,10, 9, 9, 9, 9, 9, 9,10,10,10,10, 9,
	 9, 9,10,10,10, 9,10, 9, 7, 8, 8, 8, 9, 9, 8, 9,
	 9, 8, 9, 9, 9, 9,10, 9, 9,10, 8, 9, 8, 9, 9, 9,
	 9,10, 9,
};

static const static_codebook _44p6_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p6_p6_1,
	0
};

static const long _vq_quantlist__44p6_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p6_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p6_p7_0,
	0
};

static const long _vq_quantlist__44p6_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p6_p7_1[] = {
	 1, 4, 5, 5,10,10, 5,10,10, 5,10,10,10,10,10,10,
	10,10, 5,10,10,10,10,10,10,10,10, 7,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10, 6,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10, 6,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10, 9,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10, 9,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10, 6,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10, 9,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10, 9,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,11,11,11,11,11,
	11,11,11,
};

static const static_codebook _44p6_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p6_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p6_p7_1,
	0
};

static const long _vq_quantlist__44p6_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p6_p7_2[] = {
	 1, 2, 3, 4, 5, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p6_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p6_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p6_p7_2,
	0
};

static const long _vq_quantlist__44p6_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p6_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p6_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p6_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p6_p7_3,
	0
};

static const char _huff_lengthlist__44p6_short[] = {
	 2, 8,13,15,16,18,21,22, 5, 4, 6, 8,10,12,17,21,
	 9, 5, 5, 6, 8,11,15,19,11, 6, 5, 5, 6, 7,12,14,
	14, 8, 7, 5, 4, 4, 9,11,16,11, 9, 7, 4, 3, 7,10,
	22,15,14,12, 8, 7, 9,11,21,16,15,12, 9, 5, 6, 8,
};

static const static_codebook _huff_book__44p6_short = {
	2, 64,
	(char *)_huff_lengthlist__44p6_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p7_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p7_l0_0[] = {
	 2, 4, 4, 7, 7, 8, 8,10,10,11,11,12,12, 4, 5, 5,
	 7, 7, 9, 9,11, 9,12,11,12,12, 4, 5, 5, 7, 7, 9,
	 9, 9,10,10,11,12,12, 7, 7, 7, 7, 8, 9, 8,11, 5,
	12, 6,12,10, 7, 7, 7, 8, 7, 8, 9, 5,11, 6,12,10,
	12, 8, 9, 9, 9, 9,10,10,11, 7,11, 7,12, 9, 8, 9,
	 8, 9, 9,10,10, 7,11, 7,11, 9,11,10,10,10,10,10,
	10,10,11,10,11, 8,11, 9,10,10,10,10,10,10,10,10,
	11, 8,10, 9,11,10,11,11,11,11,11,10,11,10,12,10,
	12,11,10,11,11,11,11,10,11,10,11,10,12,11,12,11,
	12,12,12,12,12,12,12,12,12,12,13,12,11,12,11,12,
	12,12,12,12,11,12,11,12,13,
};

static const static_codebook _44p7_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p7_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p7_l0_0,
	0
};

static const long _vq_quantlist__44p7_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p7_l0_1[] = {
	 4, 4, 4, 5, 5, 4, 4, 5, 5, 5, 4, 5, 4, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p7_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p7_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p7_l0_1,
	0
};

static const long _vq_quantlist__44p7_l1_0[] = {
	54,
	29,
	79,
	0,
	108,
};

static const char _vq_lengthlist__44p7_l1_0[] = {
	 1, 2, 3, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44p7_l1_0 = {
	2, 25,
	(char *)_vq_lengthlist__44p7_l1_0,
	1, -514516992, 1620639744, 7, 0,
	(long *)_vq_quantlist__44p7_l1_0,
	0
};

static const char _huff_lengthlist__44p7_lfe[] = {
	 2, 3, 1, 3,
};

static const static_codebook _huff_book__44p7_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p7_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p7_long[] = {
	 2, 7,14,16,17,17,18,20, 6, 3, 5, 8,10,11,13,15,
	13, 5, 3, 5, 8, 9,11,12,15, 7, 4, 3, 5, 7, 9,11,
	16,10, 7, 5, 6, 7, 9,10,17,11, 8, 7, 7, 6, 8, 8,
	19,13,11, 9, 9, 8, 8, 9,20,14,13,11,10, 8, 9, 9,
};

static const static_codebook _huff_book__44p7_long = {
	2, 64,
	(char *)_huff_lengthlist__44p7_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p7_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p1_0[] = {
	 2, 5, 5, 4, 7, 7, 4, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 6, 7, 8, 8, 9,
	10, 8, 9,10, 8, 9,10,10,10,12,10,11,11, 8,10,10,
	10,11,12,10,11,11, 6, 8, 7, 8,10, 9, 8,10, 9, 8,
	10,10,10,11,11,10,12,11, 8,10, 9,10,11,11,10,12,
	10, 5, 8, 8, 8,10,10, 8,10,10, 7, 9,10, 9,10,11,
	 9,11,11, 8,10,10,10,11,12,10,12,11, 7, 9, 9, 9,
	10,11, 9,11,11, 9, 9,11,10,11,12,11,11,12, 9,11,
	11,11,12,12,11,12,12, 7, 9, 9,10,11,11,10,12,11,
	 9,11,10,11,11,12,11,13,12,10,11,11,12,13,13,11,
	13,11, 5, 8, 8, 8,10,10, 8,10,10, 8,10,10,10,11,
	12,10,12,11, 7,10, 9, 9,11,11, 9,11,10, 7, 9, 9,
	10,11,12,10,11,11,10,11,11,11,11,13,12,13,13, 9,
	10,11,11,12,13,11,12,11, 7, 9, 9, 9,11,11, 9,11,
	10, 9,11,11,11,12,12,11,12,12, 9,11, 9,11,12,11,
	10,12,11,
};

static const static_codebook _44p7_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p7_p1_0,
	0
};

static const long _vq_quantlist__44p7_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p7_p2_0[] = {
	 4, 6, 6, 9, 9, 6, 8, 8,10,10, 6, 8, 8,10,10, 8,
	10,10,12,13, 8,10,10,13,12, 6, 8, 8,10,10, 8, 8,
	 9,10,11, 8, 9, 9,11,11,10,10,11,12,13,10,11,11,
	13,13, 6, 8, 8,10,10, 8, 9, 9,11,11, 8, 9, 8,11,
	10,10,11,11,13,13,10,11,10,13,12, 9,10,10,12,12,
	10,10,11,12,13,10,11,11,13,13,12,12,13,12,15,13,
	13,13,15,14, 9,10,10,12,12,10,11,11,13,13,10,11,
	10,13,12,12,13,13,14,15,12,13,12,15,12, 6, 8, 8,
	10,11, 8, 9,10,11,12, 8, 9, 9,11,11,10,11,12,13,
	14,10,11,11,13,13, 8, 9, 9,11,12, 9,10,11,12,13,
	 9,10,10,12,13,11,12,13,13,15,11,12,12,14,14, 8,
	 9, 9,11,12, 9,10,11,12,13, 9,10,10,13,12,11,12,
	13,14,15,11,12,12,14,13,10,11,12,13,14,11,12,13,
	13,15,12,13,13,14,14,13,13,14,14,16,14,15,14,16,
	15,10,12,11,14,13,12,12,13,14,14,11,12,12,14,14,
	14,14,15,15,16,13,14,14,16,14, 6, 8, 8,11,10, 8,
	 9, 9,11,11, 8,10, 9,12,11,10,11,11,13,13,10,12,
	11,14,13, 8, 9, 9,12,11, 9,10,10,12,13, 9,11,10,
	13,12,11,12,12,14,14,11,13,12,15,14, 8, 9, 9,12,
	11, 9,10,10,13,12, 9,11,10,13,12,11,12,12,14,14,
	11,13,12,15,13,10,11,12,13,14,11,12,13,13,14,12,
	13,12,14,14,13,13,14,14,16,14,15,14,16,16,10,12,
	11,14,13,12,13,13,14,14,11,13,12,15,13,14,14,15,
	16,16,13,14,13,16,14, 9,10,11,12,13,11,11,12,13,
	14,11,11,12,13,14,13,13,14,14,16,13,14,14,15,15,
	11,11,12,13,14,12,12,13,13,15,12,13,13,14,15,14,
	14,15,15,17,14,14,15,16,16,11,12,12,13,14,12,12,
	13,14,15,12,13,12,14,15,14,14,15,15,17,14,15,14,
	16,16,13,14,14,15,16,14,14,15,15,17,14,15,15,16,
	16,15,16,17,16,18,16,17,16,17,17,13,14,14,16,15,
	14,15,15,16,16,14,15,14,16,15,16,16,17,17,18,16,
	16,16,17,16, 9,11,10,13,12,11,12,11,14,13,11,12,
	11,14,13,13,14,14,16,15,13,14,13,16,14,11,12,12,
	14,13,12,12,13,14,14,12,13,13,15,14,14,14,15,16,
	16,14,15,14,17,15,11,12,11,14,13,12,13,13,15,14,
	12,13,12,15,13,14,15,14,16,16,14,15,14,17,15,13,
	14,14,15,16,14,14,15,16,16,14,15,15,16,16,15,16,
	16,16,17,16,16,16,17,17,13,14,14,16,15,14,15,15,
	17,16,14,15,14,17,15,16,17,17,17,17,16,16,16,18,
	16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,11,
	10,11,11,13,14,10,11,11,14,13, 8, 9, 9,11,12, 9,
	10,10,12,13, 9,10,10,13,12,11,11,12,13,15,11,12,
	12,15,14, 8, 9, 9,12,11, 9,10,11,12,13, 9,11,10,
	13,12,11,12,12,14,15,11,13,12,15,14,10,11,11,13,
	14,11,12,12,13,14,11,12,12,14,14,13,13,14,14,16,
	13,14,14,16,15,11,12,11,14,13,12,13,13,14,14,11,
	13,12,14,13,14,14,15,16,16,13,14,14,16,14, 8, 9,
	 9,11,12, 9,10,10,12,13, 9,10,10,13,12,11,12,12,
	14,15,11,12,12,14,14, 9, 9,10,11,13,10,10,12,12,
	14,10,10,11,13,13,12,12,13,14,16,12,12,13,15,15,
	 9,10,10,13,12,10,11,11,13,14,10,12,11,14,13,12,
	13,13,15,15,12,13,13,15,15,11,11,12,13,15,12,12,
	13,13,15,12,13,13,14,15,14,14,15,15,17,14,15,15,
	16,16,11,13,12,15,14,13,13,13,15,15,12,14,13,15,
	14,15,15,15,16,16,14,15,15,17,15, 7, 9, 9,12,11,
	 9,10,10,12,12, 9,11,10,13,12,11,12,12,14,14,11,
	13,12,15,14, 9,10,10,12,12,10,10,11,12,13,10,11,
	11,14,13,12,12,13,14,15,12,13,13,15,14, 9,10,10,
	12,12,10,11,11,13,13,10,11,10,14,12,12,13,13,15,
	15,12,13,12,15,13,11,12,12,14,14,12,12,13,14,15,
	12,13,13,15,15,14,13,14,13,16,14,15,15,16,16,11,
	12,12,14,14,13,13,14,15,15,12,13,12,15,14,15,15,
	15,16,16,14,15,14,17,14,10,11,12,13,14,11,12,13,
	14,15,11,12,12,14,15,13,14,15,15,17,14,14,14,16,
	16,11,12,13,12,15,12,12,14,13,16,13,13,14,13,16,
	14,14,15,14,17,15,15,15,15,17,11,13,12,15,15,13,
	13,14,15,16,12,14,13,16,15,15,15,15,17,17,15,15,
	15,17,16,14,14,15,14,16,14,14,16,14,17,15,15,15,
	14,17,16,16,17,15,18,17,17,17,16,18,14,15,15,17,
	16,15,16,16,17,17,15,16,15,17,16,17,17,17,18,18,
	16,17,16,18,17,10,11,11,14,13,11,12,12,14,14,11,
	13,12,15,14,14,14,14,16,16,14,15,14,16,15,11,12,
	12,15,13,12,13,13,15,14,13,14,13,16,14,14,15,15,
	16,16,15,16,15,17,16,11,13,12,15,14,13,13,14,15,
	15,12,14,13,16,14,15,15,15,17,17,14,16,15,17,16,
	14,14,14,16,15,14,15,15,16,16,15,16,15,17,16,16,
	16,16,16,17,16,17,17,18,17,14,15,15,16,16,15,15,
	16,17,16,14,15,15,17,16,17,17,17,18,18,16,17,16,
	18,16, 6, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,
	11,10,11,12,13,14,10,11,11,14,13, 8, 9, 9,11,12,
	 9,10,11,12,13, 9,11,10,13,12,11,12,13,14,15,11,
	12,12,15,14, 8, 9, 9,12,11, 9,10,10,12,13, 9,10,
	10,13,12,11,12,12,14,15,11,12,12,14,13,11,11,12,
	13,14,11,12,13,13,15,12,13,13,14,14,13,14,14,14,
	16,14,15,14,16,16,10,11,11,14,13,11,12,12,14,14,
	11,12,12,14,13,13,14,14,15,16,13,14,13,16,14, 7,
	 9, 9,11,11, 9,10,11,12,13, 9,10,10,12,12,11,12,
	13,14,15,11,12,12,14,14, 9,10,10,12,12,10,10,11,
	12,13,10,11,11,13,13,12,12,13,13,15,12,13,13,15,
	15, 9,10,10,12,12,10,11,11,13,13,10,11,10,13,12,
	12,13,13,14,15,12,13,12,15,13,11,12,12,14,14,12,
	12,13,14,15,13,14,13,15,15,14,13,15,13,16,15,15,
	15,16,16,11,12,12,14,14,12,13,13,14,15,12,13,12,
	15,14,14,15,15,16,17,13,14,13,16,13, 8, 9, 9,12,
	11, 9,10,10,12,13, 9,10,10,13,12,11,12,12,14,15,
	11,12,12,15,14, 9,10,10,12,13,10,11,12,13,14,10,
	11,11,14,13,12,13,13,15,15,12,13,13,15,15, 9,10,
	 9,13,11,10,11,10,13,13,10,12,10,14,12,12,13,12,
	15,15,12,13,12,15,14,11,12,13,14,15,12,13,14,14,
	15,13,13,13,15,15,14,15,15,15,17,15,15,15,16,16,
	11,12,11,15,13,12,13,13,15,14,12,13,12,16,13,14,
	15,15,16,16,14,15,14,17,14,10,11,11,13,14,11,12,
	13,14,15,11,12,12,14,14,14,14,15,15,17,14,14,14,
	15,16,11,12,13,14,15,12,13,14,14,16,13,14,13,15,
	15,14,15,16,15,17,15,15,15,17,17,11,12,12,13,15,
	13,13,14,14,16,12,13,13,14,15,15,15,15,16,17,14,
	15,15,16,16,14,15,15,16,16,14,15,15,16,17,15,15,
	16,16,17,16,16,17,16,18,17,17,17,18,18,14,14,15,
	15,16,15,15,15,16,17,14,15,15,16,16,16,17,17,17,
	18,16,16,16,17,16,10,11,11,14,13,11,13,12,15,14,
	11,13,12,15,14,14,15,14,16,16,13,15,14,17,15,11,
	12,13,15,15,12,13,14,15,16,13,14,13,16,15,15,15,
	15,16,17,15,15,15,17,16,11,13,11,15,12,13,14,13,
	16,13,12,14,12,16,13,15,15,15,17,15,14,16,14,17,
	14,14,15,15,16,17,15,15,16,16,17,15,16,15,17,17,
	16,16,17,17,18,16,17,17,18,18,14,15,14,17,13,15,
	16,15,17,15,15,16,15,17,14,16,17,16,18,16,16,17,
	16,18,15, 9,11,11,13,13,10,12,12,14,14,11,12,12,
	14,14,13,14,14,15,16,13,14,14,16,16,10,11,12,14,
	14,11,12,13,14,15,11,13,13,15,15,13,14,14,15,16,
	14,15,15,16,16,11,12,12,14,14,12,13,13,15,15,12,
	13,12,15,14,14,15,15,16,16,14,15,14,17,16,12,13,
	13,15,16,13,13,14,15,16,13,14,14,16,16,14,15,16,
	16,17,15,16,16,17,17,13,14,14,16,15,14,15,15,17,
	16,14,15,14,17,15,16,16,17,17,17,16,16,16,18,16,
	10,11,12,14,14,11,12,13,14,15,11,13,12,15,15,13,
	14,15,16,16,14,15,15,17,16,11,11,13,14,15,12,12,
	14,14,16,12,13,14,15,15,14,14,15,16,17,15,15,15,
	17,17,12,13,12,15,15,13,14,14,16,15,13,14,13,16,
	15,15,16,15,17,17,15,16,15,17,16,13,12,15,14,16,
	14,13,15,14,17,14,13,15,15,17,15,14,17,15,18,16,
	15,17,17,18,14,15,15,17,16,15,16,16,17,17,15,16,
	15,17,16,16,17,17,18,18,16,17,16,18,17,10,11,11,
	14,14,11,12,12,14,15,11,13,12,15,14,13,14,14,16,
	16,14,15,14,16,16,11,12,12,14,14,12,12,13,15,15,
	12,13,13,15,15,14,14,15,16,16,14,15,15,17,16,11,
	12,12,15,15,13,13,13,15,15,12,13,13,15,15,15,15,
	15,17,17,14,15,15,17,16,13,14,13,16,15,14,14,14,
	16,16,14,15,14,17,16,15,15,16,16,17,16,17,16,18,
	17,14,15,15,16,16,15,15,15,17,17,14,15,15,17,16,
	16,17,17,18,18,16,17,16,18,16,12,13,13,15,15,13,
	14,14,16,16,13,14,14,16,16,14,15,16,16,18,15,16,
	16,17,17,13,13,14,14,16,14,14,15,15,17,14,14,15,
	15,17,15,15,17,15,18,16,16,17,17,18,13,14,14,16,
	16,14,15,15,16,17,14,15,15,17,16,16,17,16,17,18,
	16,17,16,18,17,15,14,16,13,18,16,15,17,14,18,16,
	15,17,14,18,17,16,18,15,19,17,17,18,16,19,15,16,
	16,17,17,16,17,17,18,18,16,17,16,18,17,18,18,18,
	19,18,17,18,17,19,17,11,12,12,15,15,13,13,14,15,
	16,13,14,13,16,15,15,15,15,16,17,15,16,15,17,16,
	12,13,13,15,15,13,13,14,15,16,14,15,14,16,15,15,
	15,16,16,17,16,16,16,18,17,12,13,13,15,15,14,14,
	15,16,16,13,14,13,16,15,16,16,16,17,17,15,16,15,
	18,16,15,15,15,17,15,14,15,15,16,16,16,17,16,17,
	16,16,16,17,16,17,17,18,17,19,18,15,15,16,17,17,
	16,16,16,17,17,15,16,15,17,16,17,18,18,18,18,16,
	17,16,18,16, 9,11,11,13,13,11,12,12,14,14,10,12,
	12,14,14,13,14,14,15,16,13,14,14,16,15,11,12,12,
	14,14,12,12,13,14,15,12,13,13,15,15,14,14,15,16,
	17,14,15,15,16,16,10,12,11,14,14,11,13,13,15,15,
	11,13,12,15,14,14,14,15,16,16,13,14,14,16,15,13,
	14,14,15,16,14,14,15,15,17,14,15,15,16,17,16,16,
	16,16,18,16,16,17,17,17,12,13,13,16,15,13,14,14,
	16,16,12,14,13,16,15,15,16,16,17,17,14,16,15,17,
	16,10,11,11,14,14,11,12,13,14,15,11,12,12,15,14,
	14,14,15,16,16,13,14,14,16,16,11,12,12,14,15,12,
	13,14,15,15,13,13,13,15,15,14,15,15,16,17,15,15,
	15,16,17,11,12,12,14,14,12,13,13,15,15,12,13,12,
	15,15,14,15,15,16,17,14,15,14,16,16,14,14,15,16,
	16,14,15,15,16,17,15,16,15,17,17,16,16,17,16,18,
	16,17,17,18,18,13,13,14,15,16,14,14,15,16,17,14,
	14,14,16,15,16,16,17,17,18,15,16,15,17,16,10,12,
	11,14,14,11,13,13,15,15,11,13,12,15,15,14,15,15,
	16,16,13,15,14,16,16,12,12,13,15,15,13,13,14,15,
	16,13,14,14,16,15,15,15,16,16,17,15,15,15,17,17,
	11,13,11,15,14,12,14,13,16,15,12,14,12,16,14,15,
	15,15,17,17,14,15,14,17,15,14,15,15,16,17,15,15,
	16,16,17,15,16,16,17,17,16,16,17,17,18,16,17,17,
	18,18,13,14,12,16,14,14,15,13,17,15,14,15,13,17,
	14,16,17,15,18,17,15,17,14,18,15,11,12,12,14,15,
	13,13,14,15,16,13,14,13,16,15,15,15,16,16,17,15,
	15,15,16,16,12,13,13,15,15,13,13,14,15,16,14,15,
	14,16,16,15,15,16,16,18,16,16,16,18,17,12,13,13,
	15,15,14,14,15,15,16,13,14,13,15,15,16,16,16,17,
	18,15,16,15,17,16,15,16,15,17,16,15,15,16,16,17,
	16,17,16,17,17,16,16,17,16,18,17,18,18,18,18,14,
	15,15,15,17,16,15,17,16,17,14,15,15,16,16,17,17,
	18,18,19,16,16,16,17,16,12,13,13,15,15,13,14,14,
	16,16,13,14,14,16,16,15,16,16,17,17,15,16,15,18,
	16,13,14,14,16,16,14,15,15,16,17,14,15,15,17,16,
	16,16,17,17,18,16,17,16,18,18,13,14,13,16,14,14,
	15,14,17,15,14,15,14,17,14,16,17,16,18,17,15,17,
	15,18,15,15,16,16,17,18,16,16,17,17,18,16,17,17,
	17,18,17,17,18,18,19,17,18,18,19,18,15,16,14,17,
	13,16,17,15,18,14,16,17,15,18,14,18,18,17,19,16,
	17,18,16,19,15,
};

static const static_codebook _44p7_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p7_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p7_p2_0,
	0
};

static const long _vq_quantlist__44p7_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p3_0[] = {
	 2, 5, 5, 4, 7, 7, 4, 7, 7, 5, 7, 8, 7, 8,10, 8,
	 9, 9, 5, 7, 7, 8, 9, 9, 7,10, 8, 5, 7, 8, 8, 9,
	10, 8,10,10, 8, 9,10,10,10,12,10,12,12, 8,10,10,
	10,12,12,10,12,11, 5, 8, 7, 8,10,10, 8,10, 9, 8,
	10,10,10,11,12,10,12,12, 8,10, 9,10,12,12,10,12,
	10, 5, 8, 8, 7,10,10, 8,10,10, 7, 9,10, 9,10,12,
	10,12,12, 8,10,10,10,12,12,10,12,11, 7, 9,10, 9,
	11,12,10,12,11, 9, 9,12,11,10,14,12,12,13,10,12,
	11,12,13,13,11,14,12, 7,10, 9,10,11,11,10,12,11,
	 9,11,11,11,11,13,12,14,13,10,12,12,12,14,14,11,
	14,12, 5, 8, 8, 8,10,10, 7,10,10, 8,10,10,10,11,
	12,10,12,12, 7,10, 9,10,12,12, 9,12,10, 7, 9,10,
	10,11,12,10,11,11,10,12,12,11,12,14,12,14,14, 9,
	11,11,12,13,14,11,13,11, 7,10, 9,10,11,12, 9,12,
	11,10,11,12,11,12,14,12,13,13, 9,12, 9,12,13,12,
	11,14,10,
};

static const static_codebook _44p7_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p7_p3_0,
	0
};

static const long _vq_quantlist__44p7_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p3_1[] = {
	 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 8, 8, 7,
	 8, 8, 7, 8, 7, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 8, 8,
	 8, 8, 8, 8, 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9,
	 8, 7, 8, 8, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 7, 8, 8, 8,
	 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8,
	 8, 9, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8,
	 9, 8, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 8, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8,
	 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8, 8, 8, 8, 8, 9,
	 8, 8, 8, 8, 8, 9, 9, 9, 9, 9, 8, 8, 8, 8, 9, 9,
	 8, 9, 8,
};

static const static_codebook _44p7_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p7_p3_1,
	0
};

static const long _vq_quantlist__44p7_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p4_0[] = {
	 1, 5, 5, 5, 7, 8, 5, 8, 7, 5, 7, 8, 7, 8,10, 8,
	10,10, 5, 8, 7, 8,10,10, 7,10, 8, 6, 8, 9, 9,10,
	12, 9,11,11, 9,10,11,11,11,13,11,13,13, 9,11,11,
	11,12,13,11,13,11, 6, 9, 8, 9,11,11, 9,12,10, 9,
	11,11,11,11,13,11,13,13, 9,11,10,11,13,13,11,13,
	11, 6, 9, 9, 8,10,11, 9,12,11, 8,10,11,10,11,13,
	11,13,13, 9,11,11,11,13,12,11,13,11, 8,10,10, 9,
	11,12,10,12,12,10,10,12,11,11,14,12,13,14,10,12,
	12,12,13,13,11,14,11, 8,11,10,11,12,13,11,14,12,
	10,12,11,11,12,14,13,15,14,10,12,12,13,14,15,12,
	14,12, 5, 9, 9, 9,11,12, 8,11,10, 9,11,11,11,11,
	13,11,12,13, 8,11,10,11,13,13,10,13,11, 8,10,11,
	11,12,14,11,13,12,10,12,12,12,12,14,14,15,14,10,
	11,12,13,14,15,11,14,12, 8,10,10,10,12,12, 9,12,
	11,10,12,12,11,11,14,12,13,13,10,12,10,12,14,13,
	11,13,11,
};

static const static_codebook _44p7_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p7_p4_0,
	0
};

static const long _vq_quantlist__44p7_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p7_p4_1[] = {
	 7, 8, 8,10,10, 8, 9, 9,10,11, 8, 9, 9,10,10, 9,
	10,10,11,11, 9,10,10,11,11, 8, 9, 9,10,10, 9, 9,
	10,11,11, 9,10,10,11,11,10,10,11,11,11,10,11,11,
	11,11, 8, 9, 9,10,10, 9,10,10,11,11, 9,10, 9,11,
	11,10,11,11,11,11,10,11,10,11,11,10,10,10,11,11,
	10,11,11,11,11,10,11,11,11,11,11,11,11,11,12,11,
	11,11,11,12,10,10,10,11,11,10,11,11,11,11,10,11,
	11,11,11,11,11,11,12,11,11,11,11,12,11, 8, 9,10,
	11,11, 9,10,11,11,11, 9,10,10,11,11,10,11,11,12,
	12,10,11,11,12,12,10,10,10,11,11,10,10,11,11,12,
	10,11,11,12,12,11,11,12,12,12,11,11,12,12,12,10,
	10,10,11,11,10,11,11,12,12,10,11,11,12,11,11,12,
	12,12,12,11,12,11,12,12,11,11,11,11,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12, 8,10, 9,11,11, 9,
	10,10,11,11, 9,10,10,11,11,10,11,11,12,12,10,11,
	11,12,12,10,10,10,11,11,10,11,11,12,12,10,11,11,
	12,12,11,11,12,12,12,11,12,12,12,12,10,10,10,11,
	11,10,11,11,12,12,10,11,10,12,11,11,12,11,12,12,
	11,12,11,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,
	11,12,11,11,12,12,12,12,11,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,10,11,11,11,12,11,11,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	11,11,12,12,12,11,12,12,12,12,12,12,12,12,12,12,
	12,13,12,13,12,12,12,13,13,11,12,11,12,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,13,13,12,12,12,
	13,13,12,12,12,12,12,12,12,12,12,13,12,12,13,13,
	13,12,13,13,13,13,12,13,13,13,13,12,12,12,12,12,
	12,12,13,13,13,12,12,12,13,12,12,13,13,13,13,12,
	13,13,13,13,10,11,11,12,11,11,11,11,12,12,11,12,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,13,
	13,12,12,12,13,13,11,12,11,12,12,12,12,12,12,12,
	11,12,11,12,12,12,12,12,13,13,12,12,12,13,12,12,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,12,13,
	13,13,13,12,13,13,13,13,12,12,12,12,12,12,12,12,
	13,13,12,13,12,13,12,12,13,13,13,13,13,13,13,13,
	13, 8,10,10,11,11, 9,10,10,11,11, 9,10,10,11,11,
	10,11,11,12,12,10,11,11,12,12, 9,10,10,11,11,10,
	10,11,11,12,10,11,11,12,12,11,11,12,12,12,11,11,
	12,12,12,10,10,10,11,11,10,11,11,12,12,10,11,10,
	12,11,11,12,11,12,12,11,12,11,12,12,11,11,11,12,
	12,11,11,12,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,12,12,11,11,11,12,11,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12, 9,10,
	10,11,11,10,11,11,11,12,10,11,11,12,12,11,11,11,
	12,12,11,11,11,12,12,10,10,11,11,12,11,11,12,12,
	12,11,11,11,12,12,11,11,12,12,12,11,12,12,12,12,
	10,11,11,12,12,11,11,11,12,12,11,12,11,12,12,11,
	12,12,12,12,11,12,12,12,12,11,11,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,12,
	12,13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12, 9,10,10,11,11,
	10,11,11,12,12,10,11,11,12,11,11,12,11,12,12,11,
	12,11,12,12,10,11,11,12,12,11,11,11,12,12,11,12,
	11,12,12,11,12,12,12,12,12,12,12,12,12,10,11,11,
	12,12,11,12,11,12,12,11,12,11,12,12,12,12,12,13,
	12,12,12,12,12,12,11,12,11,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12,12,12,12,13,11,
	12,12,12,12,12,12,12,13,12,11,12,12,12,12,12,12,
	12,13,12,12,12,12,13,12,10,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	13,11,11,12,12,12,12,12,12,12,13,12,12,12,12,12,
	12,12,13,12,13,12,12,13,13,13,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,12,12,13,12,13,13,12,13,
	12,13,13,12,12,12,12,12,12,12,13,12,13,12,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,13,
	13,12,13,13,13,13,12,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,10,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,11,12,
	12,12,12,12,12,12,12,13,12,12,12,13,12,12,12,13,
	13,13,12,13,13,13,13,11,12,12,12,12,12,12,12,13,
	13,12,12,12,13,12,12,13,13,13,13,12,13,12,13,13,
	12,12,12,12,12,12,13,13,13,13,12,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,12,12,12,13,12,12,13,
	13,13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13, 8,10,10,11,11, 9,10,10,11,11, 9,10,10,11,
	11,10,11,11,12,12,10,11,11,12,12,10,10,10,11,11,
	10,11,11,11,12,10,11,11,12,12,11,11,12,12,12,11,
	11,12,12,12, 9,10,10,11,11,10,11,11,12,12,10,11,
	10,12,11,11,12,11,12,12,11,12,11,12,12,11,11,11,
	12,12,11,11,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,11,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12, 9,
	10,10,11,11,10,11,11,12,12,10,11,11,12,12,11,11,
	12,12,12,11,12,12,12,12,10,11,11,12,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,10,11,11,12,12,11,11,12,12,12,11,11,11,12,12,
	12,12,12,12,12,11,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,12,13,12,12,
	12,13,12,11,12,12,12,12,12,12,12,12,12,11,12,12,
	12,12,12,12,12,13,12,12,12,12,13,12, 9,10,10,11,
	11,10,11,11,12,12,10,11,11,12,12,11,11,11,12,12,
	11,12,11,12,12,10,11,11,12,12,11,11,12,12,12,11,
	11,11,12,12,11,12,12,12,12,11,12,12,12,12,10,11,
	10,12,11,11,11,11,12,12,11,12,11,12,12,11,12,12,
	12,12,11,12,11,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,12,12,12,13,
	11,12,11,12,12,12,12,12,12,12,11,12,12,12,12,12,
	12,12,13,12,12,12,12,13,12,10,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,13,13,12,12,12,
	13,13,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	13,12,12,13,13,13,12,13,13,13,13,11,12,12,12,12,
	12,12,12,12,13,12,12,12,12,12,12,13,13,13,13,12,
	13,12,13,13,12,12,12,12,13,12,13,13,13,13,12,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,
	12,12,12,13,13,13,13,12,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,11,11,11,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,12,13,13,12,12,12,13,12,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,12,12,
	13,13,13,12,13,13,13,13,11,12,11,12,12,12,12,12,
	13,12,12,12,12,13,12,12,13,12,13,13,12,13,12,13,
	12,12,12,12,12,13,12,12,13,13,13,12,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,12,12,12,12,12,12,
	13,13,13,13,12,13,12,13,12,13,13,13,13,13,13,13,
	13,13,13,10,11,11,12,12,10,11,11,12,12,10,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,12,
	12,11,11,12,12,12,11,12,12,12,12,12,12,12,13,13,
	12,12,12,13,13,11,11,11,12,12,11,12,12,12,12,11,
	12,11,13,12,12,12,12,13,13,12,12,12,13,13,11,12,
	12,12,12,12,12,12,12,13,12,12,12,13,13,12,12,13,
	13,13,12,13,12,13,13,11,12,12,12,12,12,12,12,13,
	12,12,12,12,13,12,12,13,13,13,13,12,13,13,13,13,
	10,11,11,12,12,11,12,12,12,12,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,13,11,11,12,12,12,11,12,
	12,12,13,12,12,12,13,13,12,12,13,13,13,12,12,13,
	13,13,11,12,12,12,12,12,12,12,13,13,12,12,12,13,
	13,12,13,13,13,13,12,13,12,13,13,12,12,12,12,13,
	12,12,13,12,13,12,12,13,13,13,12,12,13,13,13,12,
	13,13,13,13,12,12,12,12,13,12,12,13,13,13,12,12,
	12,13,13,13,13,13,13,13,12,13,13,13,13,10,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,13,
	13,12,12,12,13,13,11,12,12,12,12,11,12,12,12,13,
	12,12,12,13,13,12,12,13,13,13,12,13,13,13,13,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,13,
	12,13,13,12,13,12,13,13,12,12,12,12,12,12,12,12,
	13,13,12,13,12,13,13,12,13,13,13,13,13,13,13,13,
	13,12,12,12,13,12,12,13,13,13,13,12,13,12,13,13,
	13,13,13,13,13,13,13,13,13,13,11,11,11,12,12,11,
	12,12,12,12,11,12,12,12,12,12,12,12,13,13,12,12,
	12,13,13,11,12,12,12,12,12,12,12,12,13,12,12,12,
	13,13,12,12,13,13,13,12,12,13,13,13,11,12,12,12,
	12,12,12,12,13,13,12,12,12,13,13,12,13,13,13,13,
	12,13,12,13,13,12,12,12,12,12,12,12,13,12,13,12,
	13,13,13,13,12,13,13,12,13,13,13,13,13,13,12,12,
	12,12,12,12,13,13,13,13,12,13,12,13,13,13,13,13,
	13,13,13,13,13,13,13,10,11,11,12,12,11,12,12,12,
	13,11,12,12,13,12,12,12,12,13,13,12,12,12,13,13,
	11,12,12,12,12,12,12,12,13,13,12,13,12,13,13,12,
	12,13,13,13,12,13,13,13,13,11,12,12,12,13,12,12,
	12,13,13,12,12,12,13,12,12,13,13,13,13,12,13,12,
	13,13,12,12,12,12,12,12,12,13,13,13,12,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,12,12,12,13,12,
	12,13,13,13,13,12,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13,10,11,11,12,12,10,11,11,12,12,10,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,
	12,12,11,11,12,12,13,11,12,12,12,12,12,12,12,13,
	13,12,12,12,13,13,10,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,12,12,12,13,13,12,12,12,13,12,11,
	12,12,12,12,12,12,12,12,13,12,12,12,13,13,12,12,
	13,13,13,12,13,13,13,13,11,12,12,12,12,12,12,12,
	13,13,12,12,12,13,12,12,13,13,13,13,12,13,12,13,
	13,10,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,13,13,12,12,12,13,13,11,12,12,12,12,12,
	12,12,12,13,12,12,12,13,13,12,12,13,13,13,12,12,
	13,13,13,11,12,12,12,12,12,12,12,13,13,11,12,12,
	13,12,12,13,13,13,13,12,13,12,13,13,12,12,12,12,
	13,12,12,13,13,13,12,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,12,12,12,13,12,12,12,13,13,13,12,
	12,12,13,13,13,13,13,13,13,12,13,13,13,13,10,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,
	13,13,12,12,12,13,13,11,12,12,12,12,12,12,12,12,
	13,12,12,12,13,13,12,12,13,13,13,12,12,13,13,13,
	11,12,11,12,12,12,12,12,13,13,11,12,12,13,12,12,
	13,12,13,13,12,13,12,13,13,12,12,12,12,12,12,12,
	13,13,13,12,13,12,13,13,12,13,13,13,13,13,13,13,
	13,13,12,12,12,13,12,12,13,12,13,13,12,13,12,13,
	13,13,13,13,13,13,12,13,12,13,13,10,11,11,12,12,
	11,12,12,12,12,11,12,12,13,12,12,12,12,13,13,12,
	12,12,13,13,11,12,12,12,12,12,12,12,12,13,12,12,
	12,13,13,12,12,13,13,13,12,13,13,13,13,11,12,12,
	12,12,12,12,12,13,13,12,12,12,13,12,12,13,13,13,
	13,12,13,12,13,13,12,12,12,12,13,12,12,13,13,13,
	12,13,13,13,13,13,13,13,13,13,13,13,13,13,13,12,
	12,12,12,12,12,13,13,13,13,12,13,12,13,13,13,13,
	13,13,13,13,13,13,13,13,11,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,13,13,12,12,12,13,
	13,11,12,12,12,12,12,12,12,13,13,12,12,12,13,13,
	12,12,13,13,13,12,13,13,13,13,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,12,12,13,12,13,13,12,13,
	12,13,13,12,12,12,12,12,12,13,13,13,13,12,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,12,
	12,12,13,13,13,13,12,13,12,13,12,13,13,13,13,13,
	13,13,13,13,12,
};

static const static_codebook _44p7_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p7_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p7_p4_1,
	0
};

static const long _vq_quantlist__44p7_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p7_p5_0[] = {
	 2, 6, 6, 9, 9, 5, 7, 8,10,11, 5, 8, 7,11,10, 8,
	10,11,12,13, 8,11,10,13,12, 6, 7, 8,10,11, 7, 8,
	10,10,12, 8, 9, 9,12,11,10,10,12,11,14,10,11,12,
	14,13, 6, 8, 7,11,10, 8, 9, 9,11,12, 7,10, 8,12,
	10,10,12,12,13,14,10,12,10,14,11, 9,10,11,11,12,
	10,10,11,11,13,11,12,12,13,13,12,11,13,11,15,13,
	14,13,14,14, 9,11,10,12,11,11,12,12,13,13,10,11,
	10,13,11,13,13,14,14,14,12,13,11,14,11, 7, 8, 9,
	11,12, 9, 9,11,12,13, 9,10,10,13,12,11,12,13,13,
	15,11,12,12,14,14, 9,10,10,12,13,10,10,12,12,14,
	11,11,11,13,13,12,12,13,13,15,12,13,13,15,14, 9,
	10,10,12,13,10,11,11,13,14,10,12,11,14,13,12,13,
	13,14,15,12,13,13,15,14,12,12,13,13,14,12,13,13,
	13,15,13,14,14,14,15,14,14,15,14,16,14,15,15,16,
	16,12,13,13,14,14,13,13,14,15,14,12,13,13,15,14,
	14,15,15,15,16,14,15,14,16,14, 7, 9, 8,12,11, 9,
	10,10,12,13, 9,11, 9,13,12,11,12,12,14,14,11,13,
	12,15,13, 9,10,10,13,12,10,11,12,13,14,10,12,11,
	14,13,12,13,13,14,15,13,13,13,15,14, 9,10,10,13,
	12,11,11,11,13,13,10,12,10,14,12,13,13,13,14,15,
	12,13,12,15,13,12,13,13,14,14,12,13,13,14,15,13,
	14,13,15,15,14,14,15,14,16,14,15,15,16,15,12,13,
	12,14,13,13,13,13,15,14,12,13,13,15,13,14,15,15,
	16,15,14,15,14,16,14,11,12,12,13,14,12,13,14,14,
	15,12,13,13,14,15,14,14,15,15,16,14,15,15,16,16,
	12,13,13,14,15,13,13,14,14,16,13,14,14,15,15,15,
	15,16,15,17,15,15,15,16,16,12,13,13,14,15,13,14,
	14,15,16,13,14,14,15,15,15,15,16,16,17,15,15,15,
	17,16,14,15,15,16,16,15,15,16,15,16,15,16,16,16,
	17,16,16,17,16,18,16,16,17,18,17,14,15,15,16,16,
	15,16,16,16,17,15,16,15,17,16,16,17,17,17,18,16,
	16,16,17,16,11,12,12,14,13,12,13,13,15,14,12,14,
	13,15,14,14,15,15,16,16,14,15,14,16,15,12,13,13,
	15,14,13,14,14,15,15,13,14,14,16,15,15,15,15,16,
	16,15,16,15,17,16,12,13,13,15,14,13,14,14,15,15,
	13,14,13,16,14,15,15,15,16,16,15,15,15,17,15,14,
	15,15,16,16,15,15,15,16,16,15,16,16,17,17,16,16,
	17,17,17,16,17,17,18,17,14,15,15,16,15,15,15,16,
	16,16,15,15,15,17,15,17,17,17,18,17,16,17,16,18,
	16, 6, 9, 9,12,12, 8,10,10,12,13, 9,11,10,13,12,
	10,12,12,14,14,11,13,12,14,14, 8,10,10,12,12, 9,
	10,11,12,14,10,11,11,13,13,12,12,13,13,15,12,13,
	13,15,14, 9,10,10,13,13,10,11,11,13,13,10,12,10,
	14,13,12,13,13,14,15,12,13,13,15,14,11,12,12,13,
	14,12,12,13,13,15,12,13,13,14,14,13,13,14,13,16,
	14,15,15,16,15,11,12,12,14,14,13,13,13,15,14,12,
	13,13,15,14,14,15,15,16,15,14,14,14,16,14, 7, 9,
	10,12,12, 9,10,11,13,13, 9,11,10,13,13,11,12,13,
	14,15,12,13,13,15,14, 9,10,11,12,13,10,10,12,13,
	14,11,11,12,14,14,12,12,14,14,15,13,13,13,15,15,
	 9,11,11,13,13,11,12,12,14,14,10,12,10,14,13,13,
	14,13,15,15,12,14,13,15,14,12,12,13,13,15,12,12,
	14,13,15,13,14,14,15,15,14,14,15,14,17,14,15,15,
	16,16,12,13,13,15,14,13,14,14,15,15,12,14,13,15,
	14,14,15,15,16,16,14,15,14,16,14, 7,10,10,12,12,
	10,11,11,12,13,10,12,10,14,12,12,13,13,14,15,12,
	13,13,15,14, 9,11,10,13,12,10,10,12,12,14,11,13,
	12,14,13,13,13,14,13,15,13,14,14,15,14,10,11,11,
	13,13,12,12,12,13,14,10,12,10,14,12,13,14,14,15,
	15,13,14,13,15,13,12,13,13,14,14,12,12,13,14,15,
	13,14,14,15,15,13,13,14,13,15,14,15,15,16,16,12,
	13,13,14,14,13,14,14,15,15,12,13,13,15,13,15,15,
	15,16,16,13,14,13,16,13,11,12,13,14,14,12,13,14,
	14,15,12,13,13,15,15,14,14,15,15,17,14,15,15,16,
	16,12,13,14,14,15,13,13,14,14,16,13,14,14,15,16,
	14,14,16,15,17,15,15,16,16,16,12,13,13,15,15,13,
	14,14,15,16,13,14,14,15,16,15,15,16,17,17,15,16,
	15,17,16,14,15,15,15,16,15,15,16,15,17,15,15,16,
	16,17,16,16,16,16,18,16,16,17,17,17,14,15,15,16,
	16,15,16,16,16,17,15,16,15,17,16,16,17,17,17,17,
	16,17,16,18,17,11,12,12,14,14,13,13,14,14,15,13,
	14,13,15,14,14,15,15,15,16,14,15,15,17,15,12,13,
	13,15,14,13,13,14,15,15,14,15,14,16,15,15,15,15,
	15,16,15,16,15,17,16,12,13,13,15,15,14,14,14,15,
	16,13,14,13,16,15,15,15,16,16,17,15,16,15,17,15,
	14,15,15,16,16,14,15,15,16,16,15,16,16,17,16,15,
	15,16,15,17,16,17,17,18,17,14,15,15,16,16,15,16,
	16,16,17,14,15,15,17,16,17,17,17,17,18,15,16,16,
	18,15, 6, 9, 9,12,12, 9,10,11,12,13, 8,10,10,13,
	12,11,12,13,14,14,10,12,12,14,13, 9,10,10,12,13,
	10,10,12,13,14,10,11,11,13,13,12,13,13,14,15,12,
	13,13,15,14, 8,10,10,12,12,10,11,11,13,13, 9,11,
	10,13,13,12,13,13,14,15,12,13,12,15,13,11,12,12,
	14,14,12,13,13,13,15,13,13,13,14,15,14,14,15,14,
	16,14,15,15,15,15,11,12,12,14,13,12,13,13,15,14,
	12,13,12,15,13,14,14,15,16,16,13,14,13,16,13, 7,
	10,10,12,12,10,10,12,12,14,10,11,11,13,12,12,13,
	13,13,15,12,13,13,15,14,10,11,11,13,13,10,10,12,
	12,14,12,12,12,14,13,13,13,14,13,15,13,14,14,15,
	14, 9,10,11,13,13,11,12,12,13,14,10,12,10,14,12,
	13,13,14,14,15,13,13,12,15,13,12,13,13,14,14,12,
	13,13,14,15,13,14,14,15,15,13,13,15,13,16,15,15,
	15,16,16,12,13,13,14,14,13,14,14,15,15,12,13,12,
	15,14,15,15,15,16,16,13,14,13,15,13, 7,10, 9,12,
	12, 9,10,11,13,13, 9,11,10,13,13,11,13,13,14,15,
	11,13,12,15,14, 9,11,11,13,13,10,10,12,13,14,11,
	12,12,14,14,12,13,14,14,15,13,13,13,15,15, 9,11,
	10,13,12,11,12,11,14,14,10,12,10,14,13,13,14,13,
	15,15,12,14,12,15,14,12,13,13,14,15,13,13,14,14,
	15,13,14,14,15,15,14,14,15,14,17,14,15,15,16,16,
	12,13,12,15,13,13,14,14,15,15,12,14,13,15,13,14,
	15,15,16,16,14,15,14,16,14,11,12,12,14,14,13,13,
	14,14,15,13,14,13,15,15,14,15,15,16,17,14,15,15,
	16,15,12,13,13,15,15,13,13,14,15,16,14,14,14,16,
	15,15,15,16,15,17,15,16,15,17,16,12,13,13,14,15,
	14,14,15,15,16,13,14,13,15,15,15,15,16,16,17,15,
	15,15,16,15,14,15,15,16,16,14,15,15,16,17,15,16,
	16,17,17,16,15,16,15,17,16,17,17,17,17,14,15,15,
	15,16,15,15,16,16,17,14,15,15,16,16,16,16,17,17,
	18,15,16,15,17,15,11,13,12,14,14,12,13,13,15,15,
	12,14,13,15,14,14,15,15,16,16,14,15,14,16,15,12,
	13,13,15,15,13,14,14,15,16,13,14,14,16,16,15,15,
	16,16,17,15,16,15,17,16,12,13,13,15,14,13,14,14,
	16,15,13,14,13,16,14,15,16,15,17,16,15,15,14,18,
	15,14,15,15,16,16,15,15,16,16,17,15,16,15,17,16,
	16,16,17,17,18,16,17,17,18,17,14,15,15,16,15,15,
	16,15,17,16,15,15,15,17,15,16,17,17,18,17,16,17,
	16,18,15,10,12,12,14,14,12,13,13,14,14,12,13,13,
	14,14,13,14,14,15,15,13,14,14,16,15,11,12,13,14,
	14,12,13,13,15,15,12,13,13,15,15,13,14,15,15,16,
	14,15,15,16,16,12,13,13,14,14,13,13,14,15,15,13,
	14,13,15,15,14,15,15,16,16,14,15,14,16,15,13,14,
	14,15,15,13,14,14,15,16,14,14,15,16,16,14,15,15,
	15,17,15,16,16,17,17,13,14,14,15,15,14,15,15,16,
	16,14,15,15,16,16,15,16,16,16,17,15,16,15,17,16,
	11,12,12,14,14,12,13,13,14,15,12,13,13,15,14,13,
	14,14,15,16,13,14,14,16,15,12,13,13,14,15,13,13,
	14,15,15,13,14,14,15,15,14,14,15,15,17,14,15,15,
	16,16,12,13,13,15,15,13,14,14,15,15,13,14,13,15,
	15,14,15,15,16,17,14,15,15,16,16,13,13,14,15,16,
	14,14,15,15,16,14,15,15,16,16,15,15,16,15,18,15,
	16,16,17,17,14,15,15,16,16,15,15,15,16,16,14,15,
	15,17,16,16,16,16,17,17,15,16,16,17,16,10,12,12,
	14,14,12,13,13,14,15,12,13,13,15,14,14,14,15,15,
	16,14,15,14,16,15,12,13,13,15,14,13,13,14,15,15,
	13,14,14,15,15,14,14,15,15,16,14,15,15,16,16,12,
	13,13,15,15,13,14,14,15,16,13,14,13,15,14,15,15,
	15,16,16,14,15,15,16,15,13,14,14,16,15,14,14,14,
	15,16,14,15,15,16,16,15,15,16,15,17,16,17,16,17,
	17,14,14,15,15,16,15,15,16,16,16,14,15,14,16,15,
	16,16,16,17,17,15,16,15,17,15,11,13,13,14,15,13,
	13,14,15,15,13,14,13,15,15,14,15,15,15,16,14,15,
	15,17,15,13,13,14,15,15,13,14,15,15,16,14,14,14,
	16,16,15,14,16,15,17,15,16,16,17,16,13,14,14,15,
	15,14,14,14,16,16,13,15,14,16,15,15,15,16,17,17,
	15,16,15,17,16,14,15,15,15,16,15,15,16,15,17,15,
	16,16,16,17,16,16,17,15,18,16,17,17,17,17,14,15,
	15,16,16,15,16,16,17,17,15,16,15,17,16,16,17,17,
	18,18,16,17,15,18,16,10,12,12,14,14,13,13,14,14,
	15,13,14,13,15,14,14,15,15,15,16,15,15,15,16,15,
	12,13,13,15,14,12,12,14,14,15,14,15,14,16,15,15,
	14,15,14,17,15,16,16,17,16,12,13,13,14,15,14,14,
	15,15,16,13,14,12,16,14,15,16,16,16,17,15,16,14,
	17,15,14,15,14,16,15,14,14,15,15,15,15,16,15,17,
	16,15,14,16,14,16,16,17,17,18,17,14,14,15,15,16,
	15,16,16,16,17,14,15,14,16,15,16,16,17,17,17,15,
	16,14,17,14,10,12,12,14,13,12,13,13,14,14,11,13,
	12,14,14,13,14,14,15,16,13,14,14,16,15,12,13,13,
	14,14,13,13,14,15,15,13,14,13,15,15,14,14,15,15,
	16,14,15,15,16,16,11,13,12,14,14,12,13,13,15,15,
	12,13,13,15,15,14,15,15,16,16,13,14,14,16,15,13,
	14,14,15,15,14,15,15,15,16,14,15,15,16,16,15,16,
	16,16,17,16,16,16,17,17,13,14,14,15,15,14,15,15,
	16,16,13,14,14,16,15,15,16,16,17,17,15,15,15,17,
	15,11,12,12,14,14,12,13,13,14,15,12,13,13,15,14,
	14,14,15,15,16,14,14,14,16,15,12,13,13,15,14,13,
	13,14,15,15,13,14,14,16,15,14,15,15,15,16,15,15,
	15,16,16,12,13,13,14,15,13,13,14,15,15,13,14,13,
	15,15,15,15,15,16,16,14,15,14,16,15,14,14,15,16,
	16,14,15,15,15,16,15,16,15,16,16,15,15,16,15,17,
	16,16,16,17,17,13,14,14,15,16,14,15,15,16,16,14,
	14,14,16,16,16,16,16,17,17,15,15,15,17,15,11,12,
	12,14,14,12,13,13,14,15,12,13,13,15,14,14,14,14,
	15,16,13,14,14,16,15,12,13,13,15,15,13,13,14,15,
	16,13,14,14,15,15,14,15,15,16,17,14,15,15,17,16,
	12,13,13,15,14,13,14,14,15,15,13,14,13,15,15,14,
	15,15,16,16,14,15,14,17,15,14,15,15,16,16,14,15,
	15,16,17,15,15,15,17,17,15,16,16,16,17,16,17,16,
	17,17,13,15,14,16,15,14,15,15,16,16,14,15,14,16,
	15,16,16,16,17,17,15,16,15,17,15,10,12,12,14,14,
	13,13,14,14,15,13,14,13,15,14,14,15,15,15,17,14,
	15,15,16,15,12,13,13,15,14,12,12,14,14,15,14,15,
	14,16,15,15,14,16,15,17,15,16,16,17,16,12,13,13,
	14,15,14,14,15,15,16,12,14,12,15,14,15,16,16,16,
	17,15,16,14,17,14,14,15,14,16,16,14,14,15,15,16,
	15,16,16,17,16,15,14,16,14,17,16,17,17,18,17,14,
	14,15,15,16,15,15,16,16,17,14,15,14,16,15,16,17,
	17,17,18,15,16,14,17,14,11,13,13,15,14,13,13,14,
	15,15,12,14,13,15,15,14,15,15,15,17,14,15,14,16,
	15,13,14,14,15,15,13,14,15,15,16,14,15,14,16,16,
	15,15,16,16,17,15,16,16,17,17,13,14,13,15,15,14,
	14,14,16,16,13,15,14,16,15,15,16,16,17,17,15,16,
	14,17,15,15,15,15,16,17,15,15,16,16,17,15,16,16,
	17,17,16,15,17,16,17,17,17,17,18,18,14,15,15,17,
	15,15,16,16,17,16,15,16,15,17,15,16,17,17,17,17,
	16,17,15,18,15,
};

static const static_codebook _44p7_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p7_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p7_p5_0,
	0
};

static const long _vq_quantlist__44p7_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p7_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p7_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p7_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p7_p5_1,
	0
};

static const long _vq_quantlist__44p7_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p6_0[] = {
	 2, 5, 6, 5, 7, 8, 5, 8, 7, 5, 7, 7, 7, 7, 9, 8,
	 9, 9, 5, 7, 7, 8, 9, 9, 7, 9, 7, 6, 8, 8, 8, 9,
	10, 8, 9, 9, 8, 9,10, 9, 9,11,10,10,11, 8,10, 9,
	10,10,11, 9,10,10, 6, 8, 8, 8, 9, 9, 8,10, 9, 8,
	 9,10, 9,10,10,10,11,10, 8,10, 9,10,11,10, 9,11,
	 9, 6, 8, 8, 7, 9, 9, 8, 9, 9, 7, 9, 9, 9, 9,10,
	 9,10,10, 8, 9, 9, 9,10,10, 9,10, 9, 7, 9, 9, 9,
	10,10, 9,10,10, 9, 9,10,10, 9,11,10,11,11, 9,10,
	10,10,11,11,10,11,10, 6, 9, 8, 9,10,10, 9,10, 9,
	 8,10,10, 9, 9,10,10,11,11, 9,10,10,10,11,11, 9,
	11, 9, 6, 8, 8, 8, 9, 9, 7, 9, 9, 8, 9, 9, 9, 9,
	10, 9,10,10, 7, 9, 9, 9,10,10, 9,10, 9, 6, 8, 9,
	 9, 9,10, 9,10,10, 9,10,10, 9, 9,11,10,11,11, 8,
	10,10,10,11,11, 9,10, 9, 7, 9, 9, 9,10,10, 9,10,
	10, 9,10,10,10,10,11,10,11,11, 9,10, 9,10,11,11,
	10,11, 9,
};

static const static_codebook _44p7_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p7_p6_0,
	0
};

static const long _vq_quantlist__44p7_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p6_1[] = {
	 4, 7, 7, 6, 7, 8, 6, 8, 7, 7, 7, 8, 7, 7, 8, 8,
	 8, 8, 7, 7, 7, 8, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8,
	 8, 9, 9, 8, 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9,
	 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8, 8,
	 8, 9, 8, 9, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8,
	 8, 9, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8,
	 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8,
	 8, 8, 8, 9, 9, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 9, 9, 9,
	 8, 9, 8,
};

static const static_codebook _44p7_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p7_p6_1,
	0
};

static const long _vq_quantlist__44p7_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p7_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p7_0,
	1, -513979392, 1633504256, 2, 0,
	(long *)_vq_quantlist__44p7_p7_0,
	0
};

static const long _vq_quantlist__44p7_p7_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p7_p7_1[] = {
	 1, 5, 5, 4,10,10, 5,10,10, 5,10,10,10,10,10,10,
	10,10, 5,10,10,10,10,10, 9,10,10, 6,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10, 7,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10, 6,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10, 6,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,
	10,11,11,
};

static const static_codebook _44p7_p7_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p7_p7_1,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44p7_p7_1,
	0
};

static const long _vq_quantlist__44p7_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p7_p7_2[] = {
	 1, 3, 2, 4, 5, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p7_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p7_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p7_p7_2,
	0
};

static const long _vq_quantlist__44p7_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p7_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p7_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p7_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p7_p7_3,
	0
};

static const char _huff_lengthlist__44p7_short[] = {
	 3, 9,14,16,17,19,22,22, 5, 4, 6, 9,11,13,17,20,
	 9, 5, 5, 6, 9,11,15,19,11, 7, 5, 5, 7, 9,13,17,
	14, 9, 7, 6, 6, 7,11,14,16,11, 9, 7, 6, 4, 4, 8,
	19,15,13,11, 9, 4, 3, 4,21,16,16,15,12, 6, 4, 4,
};

static const static_codebook _huff_book__44p7_short = {
	2, 64,
	(char *)_huff_lengthlist__44p7_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p8_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p8_l0_0[] = {
	 2, 4, 4, 7, 7, 8, 8,10,10,11,11,12,12, 4, 5, 5,
	 7, 7, 9, 9,10, 9,12,10,12,12, 4, 5, 5, 7, 7, 9,
	 9, 9,10,10,12,12,12, 7, 7, 7, 7, 8, 9, 8,11, 5,
	12, 6,12,10, 7, 7, 7, 8, 7, 8, 9, 5,11, 6,12,10,
	12, 8, 9, 9, 9, 9, 9, 9,11, 7,11, 7,11, 9, 8, 9,
	 9, 9, 9, 9, 9, 7,10, 7,11, 9,11,10,10,10,10,10,
	10,10,11,10,11, 8,12, 9,10,10,10,10,10,10,10,10,
	11, 8,11, 9,12,10,11,11,11,11,11,11,11,11,12,10,
	12,11,10,11,11,11,11,11,11,11,11,10,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,11,12,12,12,
	12,12,12,12,12,12,11,12,12,
};

static const static_codebook _44p8_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p8_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p8_l0_0,
	0
};

static const long _vq_quantlist__44p8_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p8_l0_1[] = {
	 4, 4, 4, 5, 5, 4, 4, 5, 5, 5, 4, 5, 4, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p8_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p8_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p8_l0_1,
	0
};

static const long _vq_quantlist__44p8_l1_0[] = {
	54,
	29,
	79,
	0,
	108,
};

static const char _vq_lengthlist__44p8_l1_0[] = {
	 1, 2, 3, 6, 7, 7, 6, 7, 7, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8,
};

static const static_codebook _44p8_l1_0 = {
	2, 25,
	(char *)_vq_lengthlist__44p8_l1_0,
	1, -514516992, 1620639744, 7, 0,
	(long *)_vq_quantlist__44p8_l1_0,
	0
};

static const char _huff_lengthlist__44p8_lfe[] = {
	 2, 3, 1, 3,
};

static const static_codebook _huff_book__44p8_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44p8_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p8_long[] = {
	 2, 7,14,16,17,18,20,21, 7, 4, 6, 8,11,12,14,16,
	13, 5, 4, 4, 8, 9,11,13,15, 8, 4, 3, 5, 7, 9,10,
	17,11, 8, 4, 4, 6, 9, 9,17,11, 9, 7, 6, 5, 7, 8,
	19,13,11, 9, 9, 7, 8, 8,21,15,13,11,10, 8, 8, 7,
};

static const static_codebook _huff_book__44p8_long = {
	2, 64,
	(char *)_huff_lengthlist__44p8_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p8_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p1_0[] = {
	 2, 5, 5, 4, 7, 7, 4, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 6, 7, 8, 8, 9,
	10, 8, 9,10, 8, 9,10,10,10,12,10,11,12, 8,10,10,
	10,11,12,10,11,11, 6, 8, 7, 8,10, 9, 8,10, 9, 8,
	10,10,10,11,11,10,12,11, 8,10, 9,10,12,11,10,12,
	10, 5, 8, 8, 8,10,10, 8,10,10, 7, 9,10, 9,10,11,
	 9,11,11, 8,10,10,10,12,12,10,12,11, 7, 9, 9, 9,
	10,11, 9,11,11, 9, 9,11,10,11,12,10,11,12, 9,11,
	11,11,12,12,11,12,12, 7, 9, 9,10,11,11,10,12,11,
	 9,11,10,11,11,12,11,13,12,10,11,11,12,13,13,11,
	13,11, 5, 8, 8, 8,10,10, 8,10,10, 8,10,10,10,11,
	12,10,12,11, 7,10, 9, 9,11,11, 9,11,10, 7, 9, 9,
	10,11,12,10,11,11,10,11,11,11,11,13,12,13,13, 9,
	10,11,12,12,13,11,12,11, 7, 9, 9, 9,11,11, 9,11,
	10, 9,11,11,11,12,12,11,12,12, 9,11, 9,10,12,11,
	10,12,11,
};

static const static_codebook _44p8_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p8_p1_0,
	0
};

static const long _vq_quantlist__44p8_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p8_p2_0[] = {
	 4, 6, 6, 9, 9, 6, 8, 8,10,10, 6, 8, 8,10,10, 8,
	 9,10,12,12, 8,10, 9,12,12, 6, 8, 8,10,10, 8, 8,
	 9,10,11, 8, 9, 9,11,11, 9,10,11,12,13,10,11,11,
	13,13, 6, 8, 8,10,10, 8, 9, 9,11,11, 8, 9, 8,11,
	10,10,11,11,13,13, 9,11,10,13,12, 9,10,10,12,12,
	10,10,11,12,13,10,11,11,13,13,12,12,13,12,15,12,
	13,13,15,14, 9,10,10,12,12,10,11,11,13,13,10,11,
	10,13,12,12,13,13,14,15,12,13,12,15,12, 7, 8, 8,
	10,11, 8, 9,10,11,12, 8, 9, 9,11,11,10,11,11,13,
	14,10,11,11,13,13, 8, 9, 9,11,12, 9,10,11,11,13,
	 9,10,10,12,12,11,11,12,13,15,11,12,12,14,14, 8,
	 9, 9,11,11, 9,10,11,12,13, 9,10,10,12,12,11,12,
	12,14,15,11,12,12,14,14,10,11,12,13,13,11,12,12,
	13,14,12,12,12,14,14,13,13,14,14,16,14,14,14,16,
	15,10,11,11,13,13,12,12,12,14,14,11,12,12,14,13,
	14,14,14,15,16,13,14,13,16,14, 7, 8, 8,11,10, 8,
	 9, 9,11,11, 8,10, 9,12,11,10,11,11,13,13,10,11,
	11,14,13, 8, 9, 9,12,11, 9,10,10,12,12, 9,11,10,
	13,12,11,12,12,13,14,11,12,12,15,14, 8, 9, 9,12,
	11, 9,10,10,12,12, 9,11,10,13,11,11,12,12,14,14,
	11,12,12,14,13,10,11,11,13,13,11,12,12,13,14,12,
	13,12,14,14,13,13,14,14,16,13,14,14,16,15,10,11,
	11,13,13,12,12,12,14,14,11,12,12,14,13,13,14,14,
	15,15,13,14,13,16,14, 9,10,11,12,13,11,11,12,12,
	14,11,11,12,13,14,13,13,14,14,16,13,13,14,15,15,
	11,11,12,12,14,12,12,13,13,15,12,12,13,13,15,14,
	14,15,15,16,14,14,14,15,16,11,12,12,13,14,12,12,
	13,14,15,12,13,12,14,14,14,14,15,15,16,14,14,14,
	16,16,13,13,14,15,16,14,14,15,15,16,14,15,15,16,
	16,15,15,16,16,18,16,16,16,17,17,13,14,14,15,15,
	14,14,15,16,16,14,15,14,16,16,16,16,16,17,18,15,
	16,16,17,16, 9,11,10,13,12,11,12,11,14,13,11,12,
	11,14,12,13,14,13,15,14,13,14,13,16,14,11,12,12,
	14,13,12,12,13,14,14,12,13,12,15,14,14,14,14,16,
	16,14,15,14,17,15,11,12,11,14,12,12,13,12,15,13,
	12,13,12,15,13,14,14,14,16,15,14,15,14,16,15,13,
	14,14,15,15,14,14,15,16,16,14,15,14,16,16,15,15,
	16,16,17,16,16,16,17,17,13,14,14,16,15,14,15,15,
	16,16,14,15,14,17,15,16,16,16,17,17,15,16,15,18,
	16, 7, 8, 8,10,11, 8, 9, 9,11,12, 8, 9, 9,12,11,
	10,11,11,13,14,10,11,11,14,13, 8, 9, 9,11,11, 9,
	10,10,12,12, 9,10,10,12,12,11,12,12,13,14,11,12,
	12,14,14, 8, 9, 9,12,11, 9,10,11,12,13, 9,11,10,
	13,12,11,12,12,14,14,11,12,12,14,13,10,11,11,13,
	13,11,12,12,13,14,11,12,12,14,14,13,13,14,14,16,
	13,14,14,16,15,10,12,11,13,13,12,12,12,14,14,11,
	12,12,14,13,14,14,14,15,16,13,14,14,16,14, 8, 9,
	 9,11,11, 9,10,10,12,12, 9,10,10,12,12,11,11,12,
	13,14,11,12,12,14,14, 9, 9,10,11,12,10,10,11,12,
	13,10,10,11,12,13,12,12,13,14,15,12,12,13,14,15,
	 9,10,10,12,12,10,11,11,13,13,10,11,11,13,13,12,
	13,13,15,15,12,13,13,15,14,11,11,12,13,14,12,12,
	13,13,15,12,12,13,14,15,14,14,15,14,16,14,14,15,
	15,16,11,12,12,14,14,12,13,13,15,15,12,13,13,15,
	14,14,15,15,16,16,14,15,14,17,15, 8, 9, 9,11,11,
	 9,10,10,12,12, 9,11,10,13,12,11,12,12,14,14,11,
	13,12,15,13, 9,10,10,12,12,10,10,11,12,13,10,12,
	11,13,13,12,12,13,13,15,12,13,13,15,14, 9,10,10,
	12,12,11,11,12,13,13,10,12,10,13,12,12,13,13,15,
	15,12,13,13,15,13,11,12,12,14,14,12,12,13,14,14,
	12,13,13,15,14,13,13,14,13,16,14,15,14,16,16,11,
	12,12,14,14,13,13,13,15,15,12,13,12,15,14,14,15,
	15,16,17,14,15,13,16,13,10,11,11,13,14,11,12,12,
	13,15,11,12,12,14,14,13,14,14,15,16,13,14,14,16,
	16,11,11,12,12,14,12,12,13,13,15,12,13,13,13,15,
	14,14,15,14,17,14,14,15,15,16,11,12,12,14,14,12,
	13,13,15,15,12,13,13,15,15,14,15,15,16,17,14,15,
	15,16,16,13,14,14,14,16,14,14,15,14,17,14,15,15,
	14,17,16,16,17,15,18,16,16,17,16,18,13,14,14,16,
	16,14,15,15,17,16,14,15,15,17,16,16,17,17,18,18,
	16,17,16,18,17,10,11,11,14,13,11,12,12,14,14,11,
	13,12,15,14,14,14,14,16,15,14,15,14,16,15,11,12,
	12,14,13,12,13,13,15,14,13,14,13,15,14,14,15,15,
	16,16,14,15,15,17,15,11,12,12,14,14,13,13,13,15,
	15,12,13,13,15,14,15,15,15,17,17,14,15,15,17,15,
	13,14,14,16,15,14,15,15,16,16,15,15,15,17,16,16,
	16,16,16,17,16,17,16,18,17,14,14,14,16,16,15,15,
	15,16,16,14,15,14,17,16,16,17,17,17,18,16,17,16,
	18,16, 7, 8, 8,11,11, 8, 9, 9,11,12, 8, 9, 9,12,
	11,10,11,11,13,14,10,11,11,14,13, 8, 9, 9,11,12,
	 9,10,11,12,13, 9,11,10,13,12,11,12,12,13,14,11,
	12,12,14,14, 8, 9, 9,11,11, 9,10,10,12,12, 9,10,
	10,13,12,11,12,12,14,14,11,12,11,14,13,10,11,12,
	13,13,11,12,12,13,14,12,13,12,14,14,13,13,14,14,
	16,13,14,14,16,15,10,11,11,13,13,11,12,12,14,14,
	11,12,12,14,13,13,14,14,15,16,13,14,13,16,14, 8,
	 9, 9,11,11, 9,10,11,12,13, 9,10,10,12,12,11,12,
	13,13,14,11,12,12,14,14, 9,10,10,12,12,10,10,11,
	12,13,11,12,11,13,13,12,12,13,13,15,12,13,13,15,
	15, 9,10,10,12,12,10,11,12,13,14,10,11,10,13,12,
	12,13,13,14,15,12,13,12,15,13,12,12,12,14,14,12,
	12,13,14,15,13,13,13,15,15,14,14,15,13,16,14,15,
	15,16,16,11,12,12,14,14,12,13,13,14,15,12,13,12,
	14,14,14,14,15,16,16,13,14,13,16,14, 8, 9, 9,11,
	11, 9,10,10,12,12, 9,10,10,12,12,11,12,12,14,14,
	11,12,11,14,14, 9,10,10,12,12,10,11,11,13,13,10,
	11,11,13,13,12,13,13,14,15,12,13,13,15,14, 9,10,
	 9,12,11,10,11,10,13,12,10,11,10,13,12,12,13,12,
	15,14,12,13,12,15,14,11,12,12,14,14,12,13,13,14,
	15,12,13,13,15,15,14,14,15,15,17,14,15,15,16,16,
	11,12,11,14,13,12,13,12,15,14,12,13,12,15,13,14,
	15,14,16,15,13,15,14,17,14,10,11,11,13,14,11,12,
	13,13,15,11,12,12,14,14,14,14,15,15,17,13,14,14,
	15,16,11,12,12,14,14,12,12,13,14,15,13,13,13,15,
	15,14,15,15,15,17,15,15,15,16,16,11,12,12,13,14,
	13,13,14,14,15,12,13,13,14,15,14,15,15,16,17,14,
	15,15,16,16,14,14,14,16,16,14,14,15,15,17,15,15,
	15,17,16,16,16,17,16,18,16,17,17,18,17,13,14,14,
	15,16,14,15,15,16,17,14,15,15,16,16,16,17,17,17,
	18,16,16,16,17,16,10,11,11,14,13,11,12,12,14,14,
	11,12,12,15,13,13,14,14,16,15,13,14,14,16,15,11,
	12,12,14,14,12,13,13,15,15,12,13,13,15,15,14,15,
	15,16,17,14,15,15,17,16,11,12,11,14,12,12,13,13,
	15,13,12,13,12,15,13,14,15,15,16,15,14,15,14,17,
	14,13,14,14,16,16,14,15,15,16,17,14,15,15,16,17,
	16,16,17,17,18,16,17,17,18,18,13,14,14,16,13,14,
	15,15,17,14,14,15,14,17,14,16,17,16,17,16,16,17,
	16,18,15, 8,11,11,13,13,10,12,12,14,14,11,12,12,
	14,14,13,13,14,15,16,13,14,14,16,15,10,11,11,14,
	14,11,12,12,14,15,11,12,12,15,14,13,14,14,15,16,
	13,14,14,16,16,11,12,12,14,14,12,13,13,15,15,12,
	13,12,15,14,14,14,15,16,16,14,15,14,16,16,12,13,
	13,15,15,12,13,14,15,16,13,14,14,16,16,14,15,15,
	16,17,15,15,16,17,17,13,14,14,16,15,14,15,15,16,
	16,14,15,14,16,16,16,16,16,17,17,15,16,16,18,16,
	10,11,11,13,14,11,12,12,14,15,11,12,12,15,14,13,
	14,14,16,16,13,14,14,16,16,11,11,12,14,14,12,12,
	13,14,15,12,13,13,15,15,14,14,15,15,17,14,14,15,
	16,16,11,12,12,15,14,12,13,13,15,15,12,13,13,15,
	15,14,15,15,17,17,14,15,15,17,16,13,12,14,14,16,
	13,13,15,14,17,14,13,15,15,17,15,14,16,15,18,16,
	15,16,16,18,13,14,14,16,16,14,15,15,17,17,14,15,
	15,17,16,16,17,17,18,18,16,17,16,18,17,10,11,11,
	14,13,11,12,12,14,14,11,13,12,15,14,13,14,14,15,
	16,13,14,14,16,16,11,12,12,14,14,12,13,13,14,15,
	12,13,13,15,15,14,14,15,15,16,14,15,15,17,16,11,
	12,12,14,14,13,13,13,15,15,12,13,13,15,14,14,15,
	15,16,17,14,15,14,17,15,13,14,13,16,15,14,14,14,
	15,16,14,15,14,16,16,15,15,16,16,17,16,16,16,18,
	17,14,14,14,16,16,15,15,15,17,16,14,15,14,17,16,
	16,16,17,17,18,16,17,16,18,16,11,13,13,15,15,12,
	13,14,15,16,12,14,14,15,15,14,15,15,16,17,14,15,
	15,17,17,12,13,14,14,16,13,14,14,14,16,14,14,14,
	15,16,15,15,16,15,18,15,16,16,17,17,13,14,14,16,
	16,14,14,15,16,16,14,15,14,16,16,15,16,16,17,18,
	15,16,16,18,17,14,14,16,13,17,15,15,16,14,18,15,
	15,16,14,18,16,16,18,15,19,17,17,18,16,18,15,16,
	15,17,17,15,16,17,18,18,16,16,16,18,17,17,18,18,
	19,19,17,18,17,19,18,11,12,12,15,14,13,13,14,15,
	16,13,14,13,16,14,15,15,15,16,17,15,16,15,17,16,
	12,13,13,15,14,13,13,14,15,15,14,15,14,16,15,15,
	15,16,16,17,16,16,16,18,17,12,13,13,15,15,14,14,
	15,16,16,13,14,13,16,15,16,16,16,17,18,15,16,15,
	17,16,14,15,14,17,15,14,15,15,16,16,15,16,15,17,
	16,16,15,16,15,17,17,18,17,18,17,15,15,15,16,17,
	16,16,16,17,17,15,16,15,17,16,17,18,18,18,18,16,
	17,16,18,15, 8,11,11,13,13,11,12,12,14,14,10,12,
	12,14,14,13,14,14,15,16,13,14,13,16,15,11,12,12,
	14,14,12,12,13,14,15,12,13,13,15,15,14,14,15,15,
	16,14,14,14,16,16,10,11,11,14,14,11,12,12,14,15,
	11,12,12,15,14,13,14,14,16,16,13,14,14,16,15,13,
	14,14,15,16,14,14,15,16,16,14,15,15,16,16,15,16,
	16,16,18,16,16,16,17,17,12,13,13,15,15,13,14,14,
	16,16,12,14,13,16,15,15,16,15,17,17,14,16,15,17,
	16,10,11,11,13,14,11,12,13,14,15,11,13,12,14,14,
	14,14,15,16,16,13,14,14,16,16,11,12,12,14,14,12,
	13,13,14,15,13,14,13,15,15,14,15,15,16,17,14,15,
	15,17,16,11,12,12,14,14,12,13,13,15,15,12,13,12,
	15,14,14,15,15,16,17,14,15,15,16,16,14,14,14,16,
	16,14,14,15,16,16,15,15,15,16,16,16,16,17,16,18,
	16,17,17,18,18,13,13,14,15,16,14,14,15,16,17,13,
	14,14,16,16,16,16,17,17,18,15,16,15,17,16,10,11,
	11,14,13,11,12,12,14,14,11,12,12,15,14,13,14,14,
	16,16,13,14,14,16,16,11,12,12,14,14,12,13,13,15,
	15,12,13,13,15,15,14,15,15,16,17,14,15,15,17,16,
	11,12,11,14,14,12,13,13,15,15,12,13,12,15,14,14,
	15,14,16,16,14,15,14,17,16,14,14,14,16,16,14,15,
	15,16,17,14,15,15,17,17,16,16,17,17,18,16,17,17,
	18,18,13,14,12,16,14,14,15,13,17,15,13,15,13,17,
	14,16,16,15,18,16,15,17,14,18,15,11,12,12,14,15,
	13,13,14,14,16,13,14,13,15,14,15,15,16,16,17,15,
	16,15,17,16,12,13,13,15,15,13,13,14,15,16,14,15,
	14,16,16,15,15,16,15,18,16,16,16,18,17,12,13,13,
	15,15,14,14,15,15,16,13,14,13,15,15,16,16,16,16,
	18,15,16,15,17,16,15,15,15,17,16,15,15,16,16,17,
	16,16,16,18,17,16,16,17,15,18,17,18,17,19,18,14,
	14,15,15,17,15,15,16,16,17,14,15,15,16,16,17,17,
	18,17,19,16,17,15,17,15,11,13,12,15,15,12,14,14,
	15,15,12,14,13,16,15,15,15,15,17,17,14,15,15,17,
	16,12,14,14,16,16,14,14,15,16,16,14,14,14,16,16,
	15,16,17,17,18,15,16,16,18,17,12,14,13,16,14,13,
	14,14,16,15,13,15,14,16,14,15,16,16,17,17,15,16,
	15,18,15,15,15,16,17,17,15,16,16,17,18,16,16,16,
	18,18,17,17,18,18,19,17,17,18,19,19,14,15,14,17,
	13,15,16,15,18,14,15,16,15,18,14,17,18,17,18,16,
	16,18,16,19,15,
};

static const static_codebook _44p8_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p8_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p8_p2_0,
	0
};

static const long _vq_quantlist__44p8_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p3_0[] = {
	 2, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 5, 7, 8, 7, 9,
	10, 8, 9, 9, 8, 9,10, 9,10,12,10,11,11, 8,10, 9,
	10,11,12, 9,11,10, 5, 8, 7, 8,10, 9, 7,10, 9, 8,
	 9,10, 9,10,11,10,12,11, 8,10, 9,10,11,11, 9,12,
	10, 5, 8, 8, 7, 9,10, 8,10, 9, 7, 9,10, 9,10,11,
	 9,11,11, 8,10, 9,10,11,11,10,12,10, 7, 9,10, 9,
	10,12, 9,11,11, 9, 9,12,11,10,13,11,11,13,10,12,
	11,11,13,13,11,13,12, 7, 9, 9, 9,11,11, 9,12,11,
	 9,11,10,10,11,12,11,13,12, 9,11,11,12,13,13,11,
	13,11, 5, 8, 8, 8, 9,10, 7,10, 9, 8, 9,10,10,10,
	12,10,11,11, 7,10, 9, 9,11,11, 9,11,10, 7, 9, 9,
	 9,11,12, 9,11,11, 9,11,11,11,11,13,12,13,13, 9,
	10,11,11,12,13,10,12,11, 7,10, 9, 9,11,11, 9,12,
	10,10,11,12,11,12,13,12,13,13, 9,12, 9,11,13,11,
	10,13,10,
};

static const static_codebook _44p8_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p8_p3_0,
	0
};

static const long _vq_quantlist__44p8_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p3_1[] = {
	 6, 7, 7, 7, 7, 8, 7, 8, 7, 7, 7, 8, 7, 8, 8, 8,
	 8, 8, 7, 8, 7, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 8,
	 8, 8, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 9, 8,
	 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8,
	 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8, 9, 8,
	 8, 9, 8,
};

static const static_codebook _44p8_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p8_p3_1,
	0
};

static const long _vq_quantlist__44p8_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p4_0[] = {
	 2, 5, 5, 4, 7, 8, 4, 8, 7, 5, 7, 8, 7, 7,10, 8,
	 9, 9, 5, 7, 7, 8, 9, 9, 7,10, 7, 5, 7, 8, 8, 9,
	11, 8,10,10, 8, 9,10,10,10,12,11,12,12, 8,10,10,
	10,12,12,10,12,11, 5, 8, 7, 8,10,10, 8,11, 9, 8,
	10,10,10,11,12,10,12,12, 8,10, 9,11,12,12,10,12,
	10, 5, 8, 8, 7,10,10, 8,11,10, 7, 9,10, 9,10,12,
	10,12,12, 8,10,10,10,12,12,10,12,11, 7, 9,10, 9,
	11,12,10,12,11, 9, 9,12,10,10,13,12,12,13,10,12,
	11,12,13,13,11,13,11, 7,10, 9,10,11,12,10,13,11,
	 9,11,11,11,11,13,12,14,13,10,11,11,12,14,14,11,
	14,11, 5, 8, 8, 8,10,11, 7,10,10, 8,10,10,10,11,
	12,10,12,12, 7,10, 9,10,12,12, 9,12,10, 7, 9,10,
	10,11,13,10,12,11,10,11,11,11,11,14,12,14,14, 9,
	11,11,12,13,14,11,13,11, 7,10, 9,10,11,12, 9,12,
	10,10,11,12,11,11,13,12,13,13, 9,12, 9,12,13,12,
	10,13,10,
};

static const static_codebook _44p8_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p8_p4_0,
	0
};

static const long _vq_quantlist__44p8_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p8_p4_1[] = {
	 7, 9, 9,10,10, 9,10,10,10,11, 9,10,10,11,10, 9,
	10,10,11,11, 9,10,10,11,11, 9,10,10,11,11,10,10,
	10,11,11,10,10,10,11,11,10,11,11,11,11,10,11,11,
	11,11, 9,10,10,11,11,10,10,10,11,11, 9,10,10,11,
	11,10,11,11,11,11,10,11,11,11,11,10,11,11,11,11,
	10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,
	11,11,11,12,10,11,11,11,11,11,11,11,11,11,10,11,
	11,11,11,11,11,11,11,11,11,11,11,11,11, 9,10,10,
	11,11,10,10,11,11,11,10,10,11,11,11,10,11,11,11,
	12,10,11,11,12,12,10,10,11,11,11,10,11,11,11,12,
	11,11,11,12,12,11,11,12,12,12,11,11,12,12,12,10,
	11,11,11,11,11,11,11,12,12,10,11,11,12,12,11,12,
	11,12,12,11,12,11,12,12,11,11,11,11,12,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12, 9,10,10,11,11,10,
	11,10,11,11,10,11,10,11,11,10,11,11,12,12,10,11,
	11,12,11,10,11,11,11,11,10,11,11,11,12,11,11,11,
	12,12,11,11,12,12,12,11,11,11,12,12,10,11,10,11,
	11,11,11,11,12,12,10,11,11,12,11,11,12,11,12,12,
	11,12,11,12,12,11,11,11,12,12,11,11,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,
	11,12,11,11,12,12,12,12,11,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,10,11,11,11,11,11,11,11,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	11,11,11,12,12,11,11,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,13,12,13,12,12,12,12,13,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,10,11,11,11,11,11,11,11,12,12,11,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,12,13,12,13,
	12, 9,10,10,11,11,10,10,11,11,11,10,11,10,11,11,
	10,11,11,12,12,10,11,11,12,12,10,10,11,11,11,10,
	11,11,11,12,10,11,11,12,12,11,11,12,12,12,11,11,
	11,12,12,10,11,10,11,11,11,11,11,12,12,10,11,11,
	12,11,11,12,11,12,12,11,12,11,12,12,11,11,11,11,
	12,11,11,12,12,12,11,12,12,12,12,11,12,12,12,12,
	11,12,12,12,12,11,11,11,12,11,11,12,12,12,12,11,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,10,10,
	11,11,11,10,11,11,12,12,10,11,11,12,12,11,11,11,
	12,12,11,11,12,12,12,10,11,11,11,12,11,11,12,12,
	12,11,11,12,12,12,11,11,12,12,12,11,12,12,12,12,
	11,11,11,12,12,11,12,12,12,12,11,12,11,12,12,11,
	12,12,12,12,11,12,12,12,12,11,11,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12, 9,10,10,11,11,
	10,11,11,11,12,10,11,11,12,11,11,12,11,12,12,11,
	12,11,12,12,10,11,11,12,11,11,11,11,12,12,11,12,
	11,12,12,11,12,12,12,12,11,12,12,12,12,10,11,11,
	12,12,11,12,11,12,12,11,12,11,12,12,12,12,12,12,
	12,11,12,12,12,12,11,12,11,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,11,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,12,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,13,13,12,12,
	12,13,13,12,12,12,12,12,12,12,12,12,13,12,12,12,
	12,13,12,12,13,12,13,12,13,13,13,13,12,12,12,12,
	12,12,12,12,13,12,12,12,12,13,12,12,13,13,13,13,
	12,13,13,13,13,10,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,13,12,12,12,12,13,13,12,12,12,13,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,12,
	13,13,12,13,12,13,13,13,13,12,12,12,12,12,12,12,
	12,13,12,12,12,12,13,12,12,13,13,13,13,12,13,13,
	13,13, 9,10,10,11,11,10,10,11,11,11,10,11,10,11,
	11,10,11,11,12,12,10,11,11,12,12,10,11,11,11,11,
	10,11,11,12,12,11,11,11,12,12,11,11,12,12,12,11,
	11,12,12,12,10,11,10,11,11,10,11,11,12,12,10,11,
	11,12,11,11,12,11,12,12,11,11,11,12,12,11,11,11,
	11,12,11,11,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,11,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,11,12,12,12,12, 9,
	10,10,11,11,10,11,11,11,12,10,11,11,12,11,11,11,
	12,12,12,11,11,12,12,12,10,11,11,12,12,11,11,12,
	12,12,11,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,10,11,11,12,12,11,11,11,12,12,11,11,11,12,12,
	11,12,12,12,12,11,12,12,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,11,11,12,12,12,12,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,10,11,10,11,
	11,10,11,11,12,12,10,11,11,12,12,11,11,11,12,12,
	11,12,11,12,12,11,11,11,12,12,11,11,12,12,12,11,
	11,12,12,12,11,12,12,12,12,11,12,12,12,12,10,11,
	11,12,11,11,12,11,12,12,11,12,11,12,12,11,12,12,
	12,12,11,12,11,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,11,12,12,11,11,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,13,12,12,12,12,
	12,12,12,12,13,13,12,12,12,13,13,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,12,
	12,12,12,12,12,12,12,12,12,12,12,13,12,13,12,12,
	12,13,13,12,13,13,12,13,12,13,13,13,13,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,
	13,12,13,12,13,12,11,11,11,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,13,13,12,12,12,13,13,11,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,12,12,12,13,
	12,12,12,12,12,12,12,12,12,13,13,12,12,12,12,13,
	12,13,13,13,13,12,13,13,13,13,12,12,12,12,12,12,
	12,12,13,12,12,12,12,13,12,12,13,13,13,13,12,13,
	13,13,12,10,11,11,12,12,11,11,11,12,12,11,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,12,
	12,11,11,12,12,12,11,12,12,12,12,11,12,12,12,12,
	12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,13,12,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12,12,12,12,12,12,
	11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,11,12,12,12,12,11,11,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,12,
	13,13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,13,12,12,12,13,13,12,12,12,12,12,
	12,12,12,12,13,12,12,12,12,13,12,12,13,12,13,12,
	12,13,13,13,12,12,12,12,12,12,12,12,12,13,12,12,
	12,13,12,12,13,13,13,13,12,13,13,13,13,10,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,11,12,12,12,
	12,12,12,12,12,12,11,11,12,12,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12,12,12,13,13,11,
	12,11,12,12,12,12,12,12,12,11,12,12,12,12,12,12,
	12,13,13,12,12,12,13,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,13,12,12,12,12,12,13,12,13,12,13,
	13,12,12,12,12,12,12,12,12,13,12,12,12,12,13,12,
	12,13,12,13,13,12,13,12,13,12,11,11,11,12,12,11,
	12,12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,12,13,12,12,13,13,13,11,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12,12,12,12,13,13,
	12,12,12,13,12,12,12,12,12,12,12,12,13,12,13,12,
	12,12,12,13,12,12,13,12,13,12,13,13,12,13,12,12,
	12,12,12,12,13,13,13,12,12,12,12,13,12,12,13,13,
	13,13,12,13,13,13,12,11,11,11,12,12,11,12,12,12,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,13,12,12,13,13,13,11,12,12,12,12,12,12,
	12,12,13,12,12,12,13,12,12,13,12,13,13,12,13,12,
	13,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,13,12,13,12,13,13,13,12,12,12,12,12,12,
	12,13,12,13,12,12,12,12,13,12,12,13,13,13,12,12,
	13,12,13,12,10,11,11,12,12,11,11,11,12,12,11,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,11,
	12,12,11,11,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,11,11,11,12,12,11,12,12,12,12,
	11,12,11,12,12,12,12,12,12,12,11,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,12,12,12,12,11,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,10,11,11,12,12,11,11,12,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,11,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,
	12,13,13,11,11,11,12,12,12,12,12,12,12,11,12,12,
	12,12,12,12,12,13,13,12,12,12,13,13,12,12,12,12,
	12,12,12,12,12,13,12,12,12,12,13,12,12,13,12,13,
	12,12,13,13,13,12,12,12,12,12,12,12,12,12,13,12,
	12,12,12,12,12,12,13,13,13,12,12,12,13,12,11,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,13,12,12,12,13,13,
	11,12,11,12,12,12,12,12,12,12,11,12,12,12,12,12,
	12,12,13,13,12,12,12,13,12,12,12,12,12,12,12,12,
	12,12,13,12,12,12,13,13,12,13,13,13,13,12,13,13,
	13,13,12,12,12,12,12,12,12,12,13,12,12,12,12,13,
	12,12,13,12,13,13,12,13,12,13,12,11,11,11,12,12,
	11,12,12,12,12,11,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,11,12,12,12,12,12,12,12,12,13,12,12,
	12,13,13,12,12,13,12,13,12,12,13,13,13,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	13,12,12,12,13,12,12,12,12,12,12,12,12,12,12,13,
	12,12,12,13,13,12,12,13,12,13,12,13,13,13,13,12,
	12,12,12,12,12,12,13,12,13,12,12,12,12,12,12,13,
	13,12,12,12,13,12,12,12,11,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,12,12,12,12,12,12,12,12,13,12,12,12,12,13,
	12,12,13,13,13,12,12,12,13,13,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,13,13,12,13,
	12,13,12,12,12,12,12,12,12,12,12,12,13,12,13,12,
	13,13,12,13,13,12,13,12,13,13,13,13,12,12,12,12,
	12,12,12,12,13,12,12,13,12,13,12,12,13,12,13,12,
	12,13,12,13,12,
};

static const static_codebook _44p8_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p8_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p8_p4_1,
	0
};

static const long _vq_quantlist__44p8_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p8_p5_0[] = {
	 2, 6, 6, 9, 9, 5, 7, 8,10,11, 5, 8, 7,11,10, 8,
	10,11,12,13, 8,11,10,13,12, 6, 7, 8,10,11, 7, 8,
	10,10,12, 8, 9, 9,12,12,10,10,12,12,14,10,12,12,
	14,13, 6, 8, 7,11,10, 8, 9, 9,12,12, 7,10, 8,12,
	11,10,12,12,13,14,10,12,10,14,12, 9,10,11,11,13,
	10,10,11,11,13,11,12,12,13,14,12,12,13,11,15,13,
	14,14,15,14, 9,11,10,13,11,11,12,12,13,13,10,11,
	10,13,11,13,14,14,15,15,12,13,12,15,11, 6, 8, 9,
	11,12, 8, 9,11,12,13, 8,10,10,13,13,11,12,13,14,
	15,11,12,13,14,14, 9, 9,10,12,13,10,10,12,12,14,
	10,11,11,13,14,12,12,14,14,15,13,13,14,15,15, 9,
	10,10,13,13,10,11,11,13,14,10,11,10,14,13,13,13,
	14,15,15,12,14,13,15,14,12,12,13,13,14,12,13,14,
	13,15,13,14,14,15,15,14,14,15,14,16,15,15,15,16,
	16,12,13,13,14,14,13,14,14,15,15,12,14,13,15,14,
	14,15,15,16,16,14,15,14,16,14, 6, 9, 8,12,11, 8,
	10,10,13,13, 8,11, 9,13,12,11,12,12,14,14,11,13,
	12,15,14, 9,10,10,13,13,10,10,11,13,14,10,12,11,
	14,13,12,13,14,14,15,13,13,13,15,14, 9,10, 9,13,
	12,10,11,11,14,13,10,12,10,14,12,13,14,13,15,15,
	12,14,12,15,14,12,13,13,14,14,13,13,13,14,15,13,
	14,14,15,15,14,14,15,14,16,14,15,15,16,16,12,13,
	12,14,13,13,14,14,15,15,12,14,13,15,13,15,15,15,
	16,16,14,15,14,16,14,11,12,12,13,14,12,13,14,14,
	16,12,13,13,15,15,14,14,16,15,17,14,15,15,16,16,
	12,13,14,14,15,13,13,15,15,16,14,14,14,15,16,15,
	15,16,16,17,15,15,16,16,17,13,13,13,15,15,14,14,
	15,15,16,13,14,14,15,16,15,15,16,16,17,15,16,15,
	17,16,14,15,15,16,16,15,15,16,16,17,15,16,16,17,
	17,16,16,17,16,18,16,17,17,17,17,15,15,15,16,16,
	15,16,16,17,17,15,16,16,17,16,16,17,17,18,18,16,
	17,16,17,16,11,12,12,15,13,13,13,13,15,15,12,14,
	13,16,14,14,15,15,16,16,14,15,14,17,15,13,13,13,
	15,14,13,14,14,16,15,14,14,14,16,15,15,15,16,16,
	17,15,16,15,17,16,12,14,13,15,14,14,14,14,16,15,
	13,14,13,16,15,15,16,16,17,16,15,16,15,17,16,15,
	15,15,16,16,15,15,16,16,17,15,16,16,17,17,16,16,
	17,17,17,17,17,17,18,17,14,15,15,16,16,15,16,16,
	17,16,15,16,15,17,16,17,17,17,18,17,16,17,16,18,
	16, 6, 9, 9,12,12, 8,10,10,12,13, 8,10,10,13,12,
	10,12,12,14,15,11,13,12,15,14, 8, 9,10,12,13, 9,
	10,11,13,14,10,11,11,14,13,12,12,13,14,15,12,13,
	13,15,15, 8,10,10,13,13,10,11,11,13,14,10,12,10,
	14,13,12,13,13,15,15,12,14,13,15,14,11,12,12,13,
	14,12,12,13,13,15,12,13,13,15,15,14,13,15,14,16,
	14,15,15,16,16,12,13,13,14,14,13,13,14,15,14,12,
	14,13,15,14,14,15,15,16,15,14,15,14,16,14, 7, 9,
	10,12,12, 9,10,11,13,14, 9,11,10,13,13,11,12,13,
	14,15,12,13,13,15,14, 9,10,11,12,13,10,10,12,13,
	14,11,11,12,14,14,12,12,14,14,15,13,13,14,15,15,
	 9,11,11,13,13,11,12,12,14,14,10,12,10,14,13,13,
	14,14,15,15,13,14,13,16,14,12,12,13,14,15,13,13,
	14,14,16,13,14,14,15,15,14,14,15,14,17,14,15,15,
	16,16,12,13,13,15,14,13,14,14,15,15,13,14,13,16,
	14,15,15,15,16,16,14,15,14,16,14, 7,10, 9,13,12,
	10,11,12,12,14,10,12,11,14,12,12,13,13,14,15,12,
	14,13,15,14, 9,11,10,13,13,10,11,12,13,14,12,13,
	12,15,13,13,13,14,13,15,13,14,14,16,15,10,11,11,
	13,13,12,12,13,14,14,11,12,11,14,13,14,14,14,15,
	16,13,14,13,16,13,12,13,13,14,14,12,13,13,14,15,
	14,14,14,15,15,14,13,15,13,16,15,15,15,17,16,13,
	13,13,14,14,14,14,14,15,15,12,13,13,15,14,15,16,
	16,16,16,14,15,14,16,13,11,12,13,14,15,12,13,14,
	15,16,13,14,14,15,15,14,14,15,15,17,14,15,15,16,
	16,13,13,14,14,15,13,13,15,14,16,14,14,15,15,16,
	15,14,16,15,17,15,16,16,16,17,13,14,14,15,15,14,
	14,15,16,16,13,15,14,16,16,15,16,16,17,17,15,16,
	15,17,16,14,15,15,15,17,15,15,16,15,17,15,16,16,
	16,17,16,16,17,16,18,17,17,17,17,18,15,15,15,17,
	16,15,16,16,17,17,15,16,16,17,16,16,17,17,18,18,
	16,17,16,18,17,11,13,12,15,14,13,13,14,15,15,13,
	14,13,16,14,15,15,15,16,16,15,16,15,17,16,13,14,
	13,15,14,13,13,14,15,15,14,15,14,16,15,15,15,16,
	16,16,15,16,15,18,16,13,14,14,15,15,14,15,15,15,
	16,13,15,13,16,15,15,16,16,17,17,15,16,15,17,16,
	15,15,15,16,16,15,15,15,16,17,16,16,16,17,16,16,
	16,17,16,17,17,17,17,18,17,15,15,15,16,16,16,16,
	16,17,17,15,16,15,17,16,17,17,17,18,18,16,17,16,
	17,15, 6, 9, 9,12,12, 8,10,10,12,13, 8,10,10,13,
	12,11,12,13,14,15,10,12,12,14,14, 9,10,10,13,13,
	10,10,12,13,14,10,11,11,14,13,12,13,14,14,15,12,
	13,13,15,15, 8,10, 9,13,12,10,11,11,13,14, 9,11,
	10,14,13,12,13,13,15,15,12,13,12,15,14,12,13,13,
	14,14,12,13,13,14,15,13,14,14,14,15,14,14,15,14,
	16,14,15,15,16,16,11,12,12,14,13,13,13,13,15,15,
	12,13,12,15,13,14,15,15,16,16,14,15,14,16,14, 7,
	 9,10,12,13,10,10,12,12,14,10,12,11,14,13,12,13,
	14,14,15,12,13,13,15,14,10,11,11,13,13,11,11,12,
	13,14,12,13,12,14,14,13,13,14,13,16,14,14,14,15,
	15, 9,10,11,13,14,12,12,13,13,15,10,12,10,14,13,
	13,14,14,15,16,13,14,13,15,13,13,14,13,14,15,12,
	13,13,14,15,14,14,14,15,15,14,13,15,13,16,15,16,
	16,16,16,12,13,13,14,14,14,14,14,15,15,12,13,13,
	15,14,15,15,16,16,16,14,15,13,16,13, 7,10, 9,12,
	12, 9,10,11,13,13, 9,11,10,14,13,12,13,13,14,15,
	11,13,12,15,14, 9,11,11,13,13,10,10,12,13,14,11,
	12,12,14,14,13,13,14,14,16,13,14,14,16,15, 9,11,
	10,13,12,11,12,11,14,14,10,12,10,14,13,13,14,13,
	15,15,12,14,12,16,14,12,13,13,14,15,13,13,14,14,
	16,13,14,14,15,15,14,14,15,14,16,15,15,15,16,16,
	12,13,12,15,14,13,14,14,15,15,12,14,13,16,14,14,
	15,15,16,16,14,15,14,17,14,11,12,13,14,15,13,13,
	14,14,16,13,14,13,15,15,15,15,16,16,17,15,15,15,
	16,16,13,14,13,15,15,13,13,15,15,16,14,15,15,16,
	16,15,15,16,15,17,16,16,16,17,17,13,13,14,14,15,
	14,14,15,15,16,13,14,13,15,15,15,16,16,16,17,15,
	16,15,16,16,15,15,15,16,16,15,15,16,16,17,16,16,
	16,17,17,16,16,17,16,18,17,17,17,18,18,15,15,15,
	16,16,16,16,16,17,17,15,15,15,16,16,17,17,17,17,
	18,16,16,16,17,15,11,13,12,15,14,13,13,14,15,15,
	12,14,13,16,14,14,15,15,16,16,14,15,14,16,15,13,
	14,14,15,15,13,14,14,16,16,14,15,14,16,16,15,15,
	16,17,17,15,16,16,17,17,13,14,13,15,14,14,14,14,
	16,15,13,15,13,16,14,15,16,15,17,16,15,16,14,17,
	15,14,16,15,16,17,15,16,16,16,17,15,16,16,17,17,
	16,16,17,17,18,16,17,17,18,17,14,15,15,17,15,15,
	16,16,17,16,15,16,15,17,15,16,17,17,18,17,16,17,
	16,18,15,10,12,12,14,14,12,13,13,15,15,12,13,13,
	15,15,13,14,14,15,16,14,15,14,16,16,12,13,13,15,
	15,12,13,14,15,15,13,14,14,15,15,14,14,15,16,17,
	14,15,15,17,16,12,13,13,15,15,13,14,14,15,16,13,
	14,14,16,15,14,15,15,16,17,14,15,15,17,16,13,14,
	14,15,16,14,14,15,15,16,14,15,15,16,16,15,15,16,
	16,17,15,16,16,17,17,14,15,15,16,16,15,15,15,16,
	16,15,15,15,16,16,16,17,16,17,17,16,16,16,18,16,
	11,12,12,14,14,12,13,14,15,15,12,13,13,15,15,13,
	14,15,16,16,14,15,15,16,16,12,13,13,15,15,13,13,
	14,15,16,13,14,14,15,16,14,14,15,16,17,15,15,15,
	16,17,12,13,13,15,15,13,14,14,15,16,13,14,14,16,
	15,15,15,15,16,17,15,16,15,17,16,14,14,15,15,16,
	14,14,15,15,17,15,15,16,16,17,15,15,16,15,18,16,
	16,16,17,17,14,15,15,16,16,15,16,16,17,17,15,15,
	15,17,16,16,17,16,17,17,16,16,16,18,16,11,12,12,
	14,14,13,13,14,15,15,13,14,13,15,15,14,15,15,16,
	16,14,15,15,16,16,12,13,13,15,15,13,13,14,15,15,
	14,14,14,16,15,15,15,15,15,16,15,16,15,17,16,12,
	13,13,15,15,14,14,15,15,16,13,14,13,16,15,15,15,
	16,16,17,15,16,15,17,15,14,15,14,16,16,14,15,15,
	16,16,15,16,15,17,16,15,15,16,15,17,16,17,16,17,
	17,14,15,15,16,16,15,16,16,16,17,14,15,15,16,16,
	16,17,17,17,18,16,16,16,17,16,12,13,13,15,15,13,
	13,14,15,16,13,14,14,16,15,14,15,15,16,17,14,15,
	15,17,16,13,14,14,15,16,14,14,15,15,17,14,15,15,
	16,16,15,14,16,15,17,15,16,16,17,17,13,14,14,16,
	16,14,15,15,16,16,14,15,14,16,16,15,16,16,17,17,
	15,16,15,17,16,15,15,16,15,17,15,15,16,15,17,15,
	16,16,16,17,16,15,17,15,18,17,17,17,17,17,15,15,
	15,17,17,16,16,16,17,17,15,16,15,17,17,16,17,17,
	18,18,16,17,15,18,15,11,12,12,15,15,13,13,15,14,
	16,13,14,13,16,14,15,15,16,16,17,15,16,15,17,15,
	12,14,13,16,14,13,13,14,14,16,14,15,14,16,15,15,
	15,16,15,17,16,16,16,17,16,12,13,14,15,16,15,15,
	15,15,16,13,15,13,16,14,16,16,16,17,17,15,16,15,
	17,15,15,16,15,16,15,14,14,15,16,16,16,16,16,17,
	16,15,15,16,15,17,17,17,17,18,17,15,15,15,16,16,
	16,16,16,16,17,14,15,15,17,16,17,17,17,17,18,15,
	16,15,18,14,10,12,12,14,14,12,13,13,15,15,12,13,
	13,15,15,14,14,15,15,16,13,15,14,16,16,12,13,13,
	15,15,13,14,14,15,16,13,14,14,15,15,14,15,15,16,
	17,14,15,15,17,16,12,13,13,15,15,13,14,14,15,15,
	12,14,13,15,15,14,15,15,16,17,14,15,14,17,15,14,
	15,15,16,16,14,15,15,16,17,15,15,15,17,16,16,16,
	16,16,17,16,16,16,17,17,13,14,14,16,15,14,15,15,
	16,16,14,15,14,16,16,15,16,16,17,17,15,16,15,17,
	16,11,12,12,14,15,13,13,14,14,15,13,14,13,15,15,
	14,15,15,16,16,14,15,15,16,16,12,14,13,15,15,13,
	13,14,15,16,14,15,14,16,15,15,15,16,15,17,15,16,
	16,17,16,12,13,13,15,15,14,14,15,15,16,13,14,13,
	16,15,15,15,16,16,17,15,15,15,16,16,14,15,15,16,
	16,14,15,15,16,16,15,16,16,17,17,16,16,16,16,17,
	16,17,17,18,17,14,14,15,15,16,15,15,16,16,17,14,
	15,15,16,16,16,16,16,17,17,15,16,15,17,15,11,12,
	12,14,14,12,13,14,15,15,12,13,13,15,15,14,15,15,
	16,16,13,15,14,16,16,12,13,13,15,15,13,14,14,15,
	16,13,14,14,16,16,15,15,15,16,17,15,15,15,17,16,
	12,13,13,15,15,13,14,14,16,15,13,14,13,16,15,15,
	16,15,17,17,14,15,14,17,16,14,15,15,16,16,15,15,
	16,16,17,15,16,16,17,17,16,16,16,16,18,16,17,16,
	18,17,14,15,14,16,15,15,15,15,17,16,14,15,14,17,
	15,16,17,16,17,17,15,16,15,17,15,11,12,12,15,15,
	13,13,15,14,16,13,15,13,16,14,15,15,16,15,17,15,
	16,15,17,16,12,14,13,15,15,13,13,15,15,16,15,15,
	15,16,15,15,15,16,15,17,16,16,16,17,16,12,13,14,
	15,16,14,14,15,15,16,13,14,13,16,14,16,16,16,16,
	17,15,16,15,17,15,15,16,15,16,16,14,15,15,16,16,
	16,16,16,17,16,15,15,16,15,17,17,17,17,18,17,15,
	15,15,15,16,16,16,16,16,17,14,15,14,16,15,17,17,
	17,17,18,15,16,15,17,15,12,13,13,15,15,13,14,14,
	15,16,13,14,14,16,15,14,15,15,16,17,14,15,15,17,
	16,13,14,14,16,15,13,14,15,16,16,14,15,15,16,16,
	15,15,16,16,17,15,16,16,17,17,13,14,13,16,15,14,
	15,15,16,16,13,15,14,16,15,15,16,16,17,17,15,16,
	14,17,15,15,15,16,17,17,15,15,16,16,17,16,16,16,
	17,17,16,15,17,16,18,17,17,17,18,18,15,15,15,17,
	14,16,16,16,17,16,15,16,15,17,15,16,17,17,18,17,
	16,17,15,18,15,
};

static const static_codebook _44p8_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p8_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p8_p5_0,
	0
};

static const long _vq_quantlist__44p8_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p8_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p8_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p8_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p8_p5_1,
	0
};

static const long _vq_quantlist__44p8_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p6_0[] = {
	 2, 6, 6, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 7, 9, 7,
	 9, 9, 6, 7, 7, 8, 9, 9, 7, 9, 7, 6, 8, 8, 8, 9,
	10, 8, 9, 9, 8, 9,10, 9, 9,10,10,10,10, 8, 9, 9,
	10,10,11, 9,10,10, 6, 8, 8, 8, 9, 9, 8,10, 9, 8,
	 9, 9, 9,10,10,10,11,10, 8,10, 9,10,11,10, 9,11,
	 9, 6, 8, 8, 7, 9, 9, 7, 9, 9, 7, 9, 9, 8, 9,10,
	 9,10,10, 8, 9, 9, 9,10,10, 9,10, 9, 7, 9, 9, 9,
	 9,10, 9,10,10, 9, 9,10,10, 9,11,10,11,11, 9,10,
	10,10,11,11,10,11,10, 6, 9, 8, 9, 9,10, 9,10, 9,
	 8,10,10, 9, 9,10,10,11,11, 9,10,10,10,11,11, 9,
	11, 9, 6, 8, 8, 7, 9, 9, 7, 9, 9, 8, 9, 9, 9, 9,
	10, 9,10,10, 7, 9, 9, 9,10,10, 8,10, 9, 6, 8, 9,
	 9, 9,10, 9,10, 9, 9,10,10, 9, 9,11,10,11,11, 8,
	 9,10,10,11,11, 9,10, 9, 7, 9, 9, 9,10,10, 9,10,
	 9, 9,10,10,10,10,11,10,11,11, 9,10, 9,10,11,11,
	10,11, 9,
};

static const static_codebook _44p8_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p8_p6_0,
	0
};

static const long _vq_quantlist__44p8_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p6_1[] = {
	 4, 7, 7, 7, 7, 8, 7, 8, 7, 7, 7, 8, 7, 8, 8, 8,
	 8, 8, 7, 8, 7, 8, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 8, 8,
	 8, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9,
	 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 8, 8, 9, 8, 8, 8, 8, 9, 9, 8, 9, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8, 8,
	 8, 8, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 8, 9, 8, 8, 8, 8, 8, 9, 9, 8,
	 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 9, 9, 8, 8, 8, 8, 9, 8, 8, 9, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8,
	 8, 8, 8, 9, 9, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9,
	 8, 9, 8,
};

static const static_codebook _44p8_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p8_p6_1,
	0
};

static const long _vq_quantlist__44p8_p7_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p8_p7_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p8_p7_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p8_p7_0,
	1, -512202240, 1635281408, 2, 0,
	(long *)_vq_quantlist__44p8_p7_0,
	0
};

static const long _vq_quantlist__44p8_p7_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p8_p7_1[] = {
	 1, 7, 7,12,12, 5,11,12,12,12, 5,12,11,12,12,12,
	12,12,12,12,12,13,13,13,13, 7,11,11,13,13,13,12,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13, 7,13,10,13,13,13,13,13,13,13,12,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13, 7,13,12,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,12,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13, 8,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,12,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13, 8,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,12,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,10,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13, 8,13,12,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,11,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,11,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,
};

static const static_codebook _44p8_p7_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p8_p7_1,
	1, -514619392, 1630767104, 3, 0,
	(long *)_vq_quantlist__44p8_p7_1,
	0
};

static const long _vq_quantlist__44p8_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p8_p7_2[] = {
	 1, 3, 2, 4, 5, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44p8_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p8_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p8_p7_2,
	0
};

static const long _vq_quantlist__44p8_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p8_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p8_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p8_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p8_p7_3,
	0
};

static const char _huff_lengthlist__44p8_short[] = {
	 3, 9,15,17,20,21,22,23, 5, 5, 7, 9,11,13,17,20,
	 9, 5, 5, 6, 8,10,15,18,11, 7, 5, 4, 6, 9,13,17,
	14, 9, 7, 5, 6, 7,10,14,17,10, 8, 6, 6, 4, 5, 8,
	20,14,13,10, 8, 4, 3, 4,23,17,16,14,12, 6, 4, 4,
};

static const static_codebook _huff_book__44p8_short = {
	2, 64,
	(char *)_huff_lengthlist__44p8_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p9_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44p9_l0_0[] = {
	 2, 5, 5, 7, 6, 8, 8, 9, 9,10,10,11,11, 4, 5, 5,
	 6, 7, 8, 8, 9, 9,10,10,11,10, 4, 5, 5, 7, 6, 8,
	 8, 9, 9,10,10,10,10, 6, 6, 7, 6, 7, 8, 8, 9, 9,
	10, 9,11, 9, 6, 6, 6, 7, 6, 8, 8, 9, 9, 9,10, 9,
	11, 7, 7, 8, 8, 8, 8, 9, 9, 9,10, 9,11, 9, 7, 8,
	 8, 8, 8, 9, 8, 9, 9, 9,10, 9,11, 8, 9, 9, 9, 9,
	 9, 9,10,10,11,10,12,10, 8, 9, 9, 9, 9, 9, 9,10,
	 9,10,11,11,12, 9,10,10,10,10,10,10,10,11,11,11,
	11,12, 9,10,10,10,10,11,10,11,10,11,11,12,11,11,
	11,11,11,11,11,11,11,12,11,12,11,12,11,11,11,11,
	11,11,11,12,11,12,11,12,11,
};

static const static_codebook _44p9_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44p9_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44p9_l0_0,
	0
};

static const long _vq_quantlist__44p9_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_l0_1[] = {
	 4, 4, 4, 5, 5, 4, 4, 5, 5, 5, 4, 5, 4, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p9_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44p9_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p9_l0_1,
	0
};

static const long _vq_quantlist__44p9_l1_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_l1_0[] = {
	 1, 2, 3, 5, 9, 9, 4, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9,10,10,10,10,10,10,10,10,
};

static const static_codebook _44p9_l1_0 = {
	2, 25,
	(char *)_vq_lengthlist__44p9_l1_0,
	1, -514619392, 1630767104, 3, 0,
	(long *)_vq_quantlist__44p9_l1_0,
	0
};

static const char _huff_lengthlist__44p9_lfe[] = {
	 1, 1,
};

static const static_codebook _huff_book__44p9_lfe = {
	1, 2,
	(char *)_huff_lengthlist__44p9_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44p9_long[] = {
	 3, 3, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _huff_book__44p9_long = {
	1, 8,
	(char *)_huff_lengthlist__44p9_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44p9_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p1_0[] = {
	 1, 5, 5, 4, 8, 8, 4, 8, 8, 5, 7, 8, 8, 9,10, 8,
	10,10, 5, 8, 7, 8,10,10, 8,10, 9, 7, 9, 9, 9,11,
	11, 9,11,11, 9,11,11,11,12,13,11,13,13, 9,11,11,
	11,13,13,11,13,13, 7, 9, 9, 9,11,11, 9,11,11, 9,
	11,11,11,13,13,11,13,13, 9,11,11,11,13,13,11,13,
	12, 5, 9, 9, 9,11,11, 9,11,11, 9,11,11,11,12,13,
	11,13,13, 9,11,11,11,13,13,11,13,13, 9,11,12,11,
	13,13,12,13,13,11,12,13,13,14,15,13,14,14,12,13,
	13,13,15,15,13,15,14, 8,10,10,11,13,13,12,14,13,
	11,12,12,13,14,15,13,15,15,11,12,12,13,15,15,13,
	15,14, 5, 9, 9, 9,11,11, 9,11,11, 9,11,11,11,13,
	13,11,13,13, 9,11,10,11,13,13,11,13,12, 8,10,10,
	11,13,13,12,13,13,11,12,12,13,14,15,14,15,15,10,
	12,12,13,14,15,13,15,14, 9,12,11,12,13,13,11,13,
	13,12,13,13,13,15,15,13,14,15,11,13,12,13,15,14,
	13,15,14,
};

static const static_codebook _44p9_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p9_p1_0,
	0
};

static const long _vq_quantlist__44p9_p2_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_p2_0[] = {
	 4, 6, 6, 8, 8, 5, 7, 7, 9, 9, 5, 7, 7, 9, 9, 6,
	 8, 8,11,11, 6, 8, 8,11,11, 6, 7, 7, 9, 9, 7, 8,
	 9,10,11, 7, 9, 9,11,10, 8, 9,10,12,12, 8,10,10,
	12,12, 6, 7, 7, 9, 9, 7, 9, 9,10,10, 7, 9, 8,11,
	10, 8,10,10,12,12, 8,10, 9,12,12, 8, 9, 9,11,11,
	 9,10,10,12,12, 9,11,11,12,13,11,12,12,13,14,11,
	12,12,14,14, 8, 9, 9,11,11, 9,11,10,13,12, 9,10,
	10,13,12,11,12,12,14,14,11,12,12,14,13, 7, 8, 9,
	10,10, 8,10,10,11,11, 8,10,10,11,11,10,11,11,13,
	13,10,11,11,13,13, 8, 9,10,10,11,10,11,11,12,13,
	10,11,11,12,12,11,11,12,13,14,11,12,12,14,14, 8,
	10,10,11,11,10,11,11,12,13,10,11,11,12,12,11,12,
	12,14,14,11,12,12,14,14,10,11,11,12,13,11,12,12,
	13,14,12,13,13,14,14,13,13,14,14,16,13,14,14,15,
	16,10,11,11,13,13,12,12,12,14,14,11,12,12,14,14,
	13,14,14,15,16,13,14,14,16,15, 7, 8, 8,10,10, 8,
	10,10,11,11, 8,10,10,12,11,10,11,11,13,13,10,11,
	11,13,13, 8,10,10,11,11,10,11,11,12,12,10,11,11,
	12,12,11,12,12,14,14,11,12,12,14,14, 8,10, 9,11,
	10,10,11,11,13,12,10,11,10,13,12,11,12,12,14,14,
	11,12,11,14,13,10,11,11,13,13,11,12,12,14,14,12,
	12,12,14,14,13,14,14,15,16,13,14,14,15,15,10,11,
	11,13,12,12,12,12,14,14,11,12,12,14,13,13,14,14,
	16,15,13,14,13,16,14,10,11,11,13,13,12,12,13,14,
	15,12,13,13,14,15,13,14,15,15,16,13,14,14,16,16,
	11,12,13,14,14,13,13,14,15,16,13,14,14,15,16,14,
	15,15,16,17,14,15,16,17,17,11,12,12,14,14,13,14,
	14,15,16,13,14,14,15,15,14,15,15,16,18,14,15,15,
	17,16,13,14,15,15,16,15,15,16,16,18,15,15,15,17,
	17,16,16,17,17,18,16,16,16,18,18,14,14,14,16,16,
	15,15,15,16,17,15,15,15,16,17,16,17,17,18,18,16,
	16,17,18,17,10,11,11,14,13,12,13,13,15,14,11,13,
	13,15,14,13,15,15,16,16,13,14,14,16,16,11,12,12,
	14,14,13,13,13,15,15,13,14,13,15,15,15,15,15,17,
	16,14,15,15,17,16,11,13,12,14,14,13,14,13,15,15,
	13,14,13,15,15,14,15,15,17,17,14,15,15,17,16,14,
	14,14,16,16,14,15,15,17,17,15,15,16,17,16,17,16,
	17,18,18,16,17,17,18,18,13,14,14,16,15,15,15,15,
	17,17,14,16,15,16,16,17,17,17,18,18,16,17,16,20,
	19, 6, 8, 8,10,10, 8,10,10,11,11, 8,10,10,12,11,
	10,11,11,13,13,10,11,11,13,13, 8, 9,10,11,11,10,
	11,11,12,12,10,11,11,13,12,11,12,12,14,14,11,12,
	12,14,14, 9,10,10,11,11,10,11,11,12,12,10,11,11,
	13,12,11,12,12,14,14,11,12,12,14,14,10,10,11,12,
	13,11,12,12,14,14,11,12,12,14,14,13,14,14,15,16,
	13,14,14,15,16,10,11,11,13,13,12,12,12,14,14,12,
	13,12,14,14,13,14,14,16,16,13,14,14,15,15, 9,10,
	10,11,12,10,11,11,12,13,10,11,11,13,12,11,12,12,
	14,14,11,12,12,14,14,10,10,11,12,13,11,12,12,13,
	14,11,12,12,13,14,12,13,14,14,15,12,13,13,15,15,
	10,11,11,13,13,11,12,12,13,14,11,12,12,14,13,12,
	13,13,15,15,12,13,13,15,15,12,11,13,12,14,13,13,
	14,14,15,13,13,14,14,15,14,15,15,16,17,14,15,15,
	16,17,12,13,12,14,14,13,14,14,15,15,13,14,14,15,
	15,14,15,15,16,17,14,15,15,16,17, 8, 9, 9,11,11,
	10,11,11,12,13,10,11,11,13,12,12,13,13,14,15,11,
	13,12,15,14, 9,11,10,12,12,11,12,12,13,14,11,12,
	12,14,13,13,13,14,15,15,13,14,13,15,15, 9,11,11,
	12,12,11,12,12,14,14,11,12,12,14,13,13,14,14,15,
	16,13,14,13,15,14,11,12,12,14,13,12,13,13,14,15,
	13,14,14,16,15,15,15,15,15,16,15,16,15,17,17,11,
	12,12,14,14,13,14,14,15,15,12,13,13,15,14,15,15,
	15,17,17,14,15,15,17,15,11,12,12,14,14,12,13,13,
	15,15,12,13,13,15,15,14,15,15,17,17,14,15,15,16,
	16,12,13,13,14,15,13,14,14,16,16,14,14,14,15,16,
	15,16,16,17,17,15,16,16,17,17,12,13,13,15,15,14,
	14,14,16,16,14,14,15,16,16,15,16,16,17,17,15,16,
	16,17,17,14,15,15,15,16,15,15,16,16,18,15,16,16,
	17,17,17,17,17,18,18,16,17,17,19,18,14,15,15,16,
	17,15,16,16,17,17,15,16,16,18,17,16,17,17,19,18,
	17,17,17,19,18,10,12,12,14,14,13,13,14,15,15,12,
	14,13,16,15,15,15,15,17,17,14,15,15,17,16,12,13,
	13,15,14,13,14,14,16,16,14,14,15,17,16,15,16,16,
	17,17,15,16,16,18,17,12,13,13,15,14,14,15,15,16,
	16,13,15,14,16,15,16,17,16,19,17,15,16,16,17,17,
	14,15,15,17,15,15,16,15,17,17,16,17,16,18,17,17,
	17,18,18,18,17,17,18,19,18,14,15,15,16,16,15,16,
	16,17,18,15,16,16,18,16,17,18,18,19,19,17,18,17,
	18,19, 6, 8, 8,10,10, 8,10,10,11,11, 8,10,10,12,
	11,10,11,11,13,13, 9,11,11,13,13, 9,10,10,11,11,
	10,11,11,12,12,10,11,11,12,12,11,12,12,14,14,11,
	12,12,14,14, 8,10, 9,11,11,10,11,11,12,12,10,11,
	11,12,12,11,12,12,14,14,11,12,12,14,14,10,11,11,
	13,13,11,12,13,14,14,12,12,12,14,14,13,14,14,15,
	16,13,14,14,16,16,10,11,10,13,12,11,12,12,14,14,
	11,12,12,14,14,13,14,14,15,16,13,14,14,16,15, 8,
	 9, 9,11,11,10,11,11,12,13,10,11,11,13,12,12,13,
	13,14,15,12,13,13,15,14,10,11,11,12,12,11,11,12,
	13,14,11,12,12,14,14,13,13,14,15,16,13,14,14,15,
	15, 9,10,11,12,12,11,12,12,13,14,11,12,12,14,13,
	13,14,14,15,16,12,14,13,15,15,11,12,12,14,14,12,
	13,13,14,15,13,14,14,16,15,14,15,15,15,17,15,15,
	16,16,17,11,12,12,13,14,13,14,14,15,15,12,13,13,
	15,14,15,16,15,16,17,14,16,15,17,15, 9,10,10,12,
	11,10,11,11,13,13,10,11,11,13,12,11,12,12,14,14,
	11,12,12,14,14,10,11,11,12,13,11,12,12,13,14,11,
	12,12,14,14,12,13,13,15,15,12,13,13,15,15,10,11,
	10,13,12,11,12,12,13,13,11,12,12,14,13,12,13,13,
	15,15,12,13,13,15,14,12,13,12,14,14,13,14,14,15,
	15,13,14,14,15,15,14,15,15,16,16,14,15,15,16,16,
	11,13,11,14,12,13,13,13,15,14,12,14,13,15,14,15,
	15,15,17,16,14,15,14,17,15,10,12,12,14,14,13,13,
	14,15,16,12,14,13,15,15,14,15,16,17,17,14,15,16,
	17,17,12,13,13,14,15,13,14,14,16,16,14,14,15,16,
	16,16,16,16,17,17,16,16,16,18,18,12,13,13,14,15,
	14,14,15,16,16,13,14,14,16,15,16,16,16,17,18,15,
	16,16,17,17,14,15,15,16,16,15,15,16,17,17,15,16,
	16,17,18,17,18,18,18,19,17,18,18,19,19,14,15,15,
	16,16,15,16,16,17,17,15,16,16,17,17,17,17,18,20,
	18,17,18,17,18,18,11,12,12,14,14,12,13,14,15,15,
	12,13,13,15,15,14,15,15,16,17,14,15,15,16,17,12,
	13,13,15,15,14,14,14,16,16,14,14,14,16,16,15,16,
	16,17,17,15,16,16,17,17,12,13,13,15,14,13,14,14,
	16,15,14,15,14,16,15,15,16,16,17,17,15,16,16,17,
	16,14,15,15,16,16,15,16,16,17,17,16,16,16,17,17,
	17,17,17,19,18,17,17,17,18,19,14,15,14,17,15,15,
	16,16,17,17,15,16,15,17,17,16,17,17,18,18,16,17,
	17,18,17, 6,11,11,13,13,11,12,12,14,14,11,12,12,
	14,14,13,14,14,16,16,13,14,14,16,16,11,12,12,14,
	14,12,13,13,15,15,12,13,13,15,15,14,15,15,16,17,
	14,15,15,17,18,11,12,12,14,14,12,13,13,15,15,12,
	13,13,15,15,14,15,15,17,17,14,15,15,16,16,13,14,
	14,15,16,14,15,15,16,17,14,15,15,17,16,15,16,17,
	18,17,16,16,16,18,17,14,14,15,16,16,14,15,15,18,
	16,14,15,15,17,16,16,17,17,18,18,16,17,16,18,17,
	11,12,12,14,14,12,13,13,15,15,12,13,13,15,15,14,
	15,15,17,17,14,15,15,16,16,12,13,13,15,15,13,14,
	14,15,16,13,14,14,16,16,15,16,16,17,17,15,15,16,
	17,17,12,13,13,15,15,14,14,14,16,16,13,14,14,16,
	16,15,16,16,17,17,15,16,16,17,17,14,14,15,15,16,
	15,15,16,16,17,15,15,16,16,17,16,17,17,17,18,16,
	17,17,18,18,14,15,15,16,16,15,16,16,17,17,15,16,
	16,17,17,17,17,17,18,19,17,17,17,18,18,10,12,12,
	14,14,12,13,14,15,16,13,14,13,15,15,14,15,15,17,
	17,14,15,16,17,17,12,13,13,15,15,13,14,14,15,15,
	14,15,14,16,16,15,16,16,17,18,15,17,16,18,17,12,
	13,13,15,15,14,14,14,16,16,13,14,14,16,15,15,16,
	16,17,18,15,16,16,17,17,14,14,14,16,16,15,15,16,
	17,17,15,16,16,17,17,17,17,17,18,20,17,17,17,19,
	19,14,15,15,16,16,15,17,16,18,18,15,16,15,17,16,
	17,18,19,19,19,17,17,17,18,17,13,14,14,16,16,14,
	15,15,17,17,14,15,15,16,17,15,17,17,18,18,16,16,
	17,18,17,14,15,15,16,17,15,16,16,17,17,15,16,16,
	17,17,16,17,17,18,18,17,17,17,18,19,14,15,15,16,
	17,15,16,16,17,17,15,16,16,17,17,16,17,17,18,18,
	17,17,17,19,19,16,16,16,16,18,16,17,17,17,18,17,
	17,17,17,19,18,18,18,19,19,18,18,18,19,20,16,16,
	17,18,18,16,18,17,18,18,17,17,17,20,19,18,18,19,
	21,20,18,20,18,18,19,10,12,12,14,14,14,14,15,15,
	17,14,15,14,17,15,16,16,17,18,18,16,18,17,19,18,
	12,14,13,16,15,14,14,15,15,17,15,16,16,18,17,16,
	17,18,17,19,17,19,18,20,19,12,13,13,15,15,15,16,
	17,17,18,14,16,14,17,16,17,18,18,19,19,17,17,17,
	18,18,15,15,15,17,16,15,16,16,17,17,17,19,17,18,
	18,18,18,18,18,21,19,20,19,20,19,15,15,16,16,17,
	17,17,18,20,20,15,16,16,18,17,18,19,19,19,20,18,
	19,18,19,17, 6,11,11,13,13,11,12,12,14,14,11,12,
	12,14,14,13,14,14,16,16,13,14,14,16,16,11,12,12,
	14,14,12,13,13,15,15,12,13,13,15,15,14,15,15,17,
	17,14,15,15,17,16,11,12,12,14,14,12,13,13,15,15,
	12,13,13,15,15,14,15,15,16,16,14,15,15,16,16,13,
	14,14,16,16,15,15,15,16,16,14,15,15,17,16,16,17,
	17,19,18,16,17,17,18,18,13,14,14,15,15,14,15,15,
	17,16,14,15,15,17,16,16,17,16,17,18,15,16,16,18,
	18,10,12,12,14,14,12,13,14,15,15,12,13,13,15,15,
	14,15,15,17,17,14,15,15,17,16,12,13,13,15,15,14,
	14,14,15,16,14,15,15,16,16,15,16,16,17,18,16,16,
	16,18,18,12,13,13,14,14,14,14,15,16,16,13,14,14,
	16,16,15,16,16,18,18,15,16,16,19,17,14,15,15,16,
	17,15,15,16,17,17,16,17,16,17,18,17,17,18,17,19,
	17,17,18,18,19,14,14,14,16,16,15,16,16,17,17,15,
	16,15,17,17,17,17,17,19,20,16,17,17,18,18,11,12,
	12,14,14,12,13,13,15,15,12,13,13,15,15,14,15,15,
	16,16,14,15,14,16,16,12,13,13,15,15,14,14,14,16,
	16,13,14,14,16,16,15,16,16,18,17,15,16,16,17,17,
	12,13,13,15,15,13,14,14,16,16,13,14,14,16,16,15,
	16,15,18,18,15,16,15,17,16,14,15,15,16,16,15,16,
	16,17,17,15,16,16,18,17,16,17,17,18,18,16,17,17,
	18,18,14,15,14,16,15,15,16,15,17,17,15,16,15,17,
	16,16,17,17,18,18,17,17,16,19,17,10,12,12,14,15,
	14,14,15,15,17,14,15,14,17,15,16,17,17,17,18,16,
	17,17,18,18,12,14,13,16,15,14,14,16,15,17,15,17,
	16,18,17,17,17,18,17,19,18,18,18,19,18,12,13,14,
	15,15,15,16,16,16,17,14,15,14,18,16,18,17,18,19,
	19,17,18,17,20,18,15,15,15,17,17,15,16,16,17,18,
	18,18,18,19,18,18,18,19,18,20,18,19,19,21,21,15,
	15,16,16,17,17,18,18,18,18,15,16,16,17,17,17,19,
	20,19,20,17,18,18,19,17,13,14,14,16,16,14,15,15,
	16,17,14,15,15,17,17,16,16,17,17,18,15,17,16,17,
	17,14,15,15,16,16,15,16,16,17,17,16,16,16,17,17,
	17,17,18,17,18,17,17,17,18,20,14,15,15,17,16,15,
	16,16,17,17,15,16,16,17,17,17,17,17,18,18,16,17,
	17,19,18,16,16,17,17,17,17,18,17,19,18,17,17,17,
	18,19,17,20,18,19,21,17,19,18,19,20,15,17,15,17,
	16,16,17,17,18,18,17,17,17,18,17,18,19,18,19,21,
	18,18,17,19,19,
};

static const static_codebook _44p9_p2_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p9_p2_0,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p9_p2_0,
	0
};

static const long _vq_quantlist__44p9_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p3_0[] = {
	 2, 5, 4, 4, 7, 7, 4, 7, 6, 5, 6, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 6, 7, 9, 9, 7, 9, 8, 6, 8, 8, 8,10,
	10, 8,10,10, 8, 9,10,10,11,12,10,12,12, 8,10,10,
	10,12,12,10,12,11, 6, 8, 8, 8,10,10, 8,10,10, 8,
	10,10,10,11,12,10,12,12, 8,10, 9,10,12,11,10,12,
	11, 5, 8, 8, 8,10,10, 8,10,10, 8, 9,10,10,11,11,
	10,11,11, 8,10,10,10,11,12,10,12,11, 8,10,10,10,
	11,11,10,11,11,10,11,11,11,12,13,11,12,13,10,11,
	11,11,13,13,11,13,13, 7, 9, 9,10,11,12,10,12,11,
	 9,11,11,11,12,13,12,14,13, 9,11,11,12,13,14,11,
	13,12, 5, 8, 8, 8,10,10, 8,10,10, 8,10,10,10,11,
	12,10,12,12, 8,10, 9,10,12,11, 9,11,11, 7, 9, 9,
	10,11,12,10,12,11, 9,11,11,11,12,13,12,14,13, 9,
	11,11,12,13,14,11,13,12, 8,10,10,10,11,11,10,11,
	11,10,11,11,11,13,13,11,13,13,10,11,10,11,13,12,
	11,13,12,
};

static const static_codebook _44p9_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p3_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44p9_p3_0,
	0
};

static const long _vq_quantlist__44p9_p3_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p3_1[] = {
	 4, 6, 6, 6, 7, 7, 6, 7, 7, 6, 7, 7, 7, 7, 8, 7,
	 7, 8, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8,
	 8, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9,
	 9, 5, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9,
	 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8,
	 9, 9, 8, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8, 9,
	 9, 9, 9, 9, 9, 9, 9, 7, 8, 8, 8, 9, 9, 8, 9, 9,
	 8, 9, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8, 9, 9, 9, 9,
	 9, 9, 6, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 9, 7, 8, 8,
	 8, 9, 9, 8, 9, 9, 8, 8, 9, 9, 9, 9, 9, 9, 9, 8,
	 8, 8, 9, 9, 9, 9, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9,
	 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 8, 9, 8, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44p9_p3_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p3_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44p9_p3_1,
	0
};

static const long _vq_quantlist__44p9_p4_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p4_0[] = {
	 2, 5, 5, 4, 7, 7, 4, 7, 6, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 6, 7, 8, 8, 9,
	10, 8,10,10, 8, 9,10,10,11,12,10,11,12, 8,10,10,
	10,11,12,10,12,11, 6, 8, 7, 8,10,10, 8,10, 9, 8,
	10,10,10,11,12,10,12,12, 8,10, 9,10,12,11,10,12,
	11, 5, 8, 8, 8,10,10, 8,10,10, 7, 9,10, 9,10,11,
	10,11,11, 8,10,10,10,12,12,10,12,11, 7, 9, 9, 9,
	11,11, 9,11,11, 9,10,11,11,11,12,11,12,12, 9,11,
	11,11,12,12,11,12,12, 7, 9, 9,10,11,12,10,12,11,
	 9,11,10,11,11,12,12,13,13, 9,11,11,12,13,13,11,
	13,11, 5, 8, 8, 8,10,10, 8,10,10, 8,10,10,10,11,
	12,10,12,12, 7, 9, 9, 9,11,11, 9,11,10, 7, 9, 9,
	10,11,12,10,12,11, 9,11,11,11,11,13,12,13,13, 9,
	10,11,12,13,13,11,12,11, 7, 9, 9, 9,11,11, 9,11,
	11, 9,11,11,11,12,12,11,12,12, 9,11,10,11,12,12,
	10,12,11,
};

static const static_codebook _44p9_p4_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p4_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44p9_p4_0,
	0
};

static const long _vq_quantlist__44p9_p4_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_p4_1[] = {
	 6, 8, 8,10, 9, 8, 9, 9,10,10, 8, 9, 9,10,10, 8,
	10,10,10,10, 8,10,10,10,10, 9, 9, 9,10,10, 9,10,
	10,10,11, 9,10,10,11,11,10,10,10,11,11,10,10,10,
	11,11, 9, 9, 9,10,10, 9,10,10,11,11, 9,10,10,11,
	10,10,10,10,11,11,10,10,10,11,11,10,10,10,10,11,
	10,10,11,11,11,10,11,11,11,11,11,11,11,11,11,11,
	11,11,11,11,10,10,10,11,10,10,11,11,11,11,10,11,
	10,11,11,11,11,11,11,11,10,11,11,11,11, 9,10,10,
	10,11,10,10,11,11,11,10,11,11,11,11,10,11,11,11,
	11,10,11,11,11,11,10,10,11,11,11,11,11,11,11,11,
	11,11,11,11,12,11,11,12,12,12,11,11,11,12,12,10,
	11,11,11,11,11,11,11,12,12,11,11,11,11,11,11,11,
	11,12,12,11,11,11,12,12,11,11,11,11,11,11,12,12,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,11,11,11,11,11,11,12,12,12,12,11,12,11,12,12,
	11,12,12,12,12,12,12,12,12,12, 9,10,10,11,10,10,
	11,11,11,11,10,11,11,11,11,10,11,11,11,11,10,11,
	11,11,11,10,11,11,11,11,11,11,11,11,11,11,11,11,
	12,12,11,11,12,12,12,11,11,11,12,12,10,11,10,11,
	11,11,11,11,11,11,11,11,11,11,11,11,11,11,12,12,
	11,11,11,12,12,11,11,11,11,11,11,12,12,12,12,11,
	12,12,12,12,11,12,12,12,12,12,12,12,12,12,11,11,
	11,11,11,11,12,12,12,12,11,12,11,12,12,12,12,12,
	12,12,11,12,12,12,12,11,11,11,11,11,11,12,12,12,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,13,12,12,12,13,13,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,12,12,12,
	13,13,12,12,12,12,12,12,12,12,12,13,12,12,12,13,
	13,12,13,13,13,13,12,13,13,13,13,12,12,12,12,12,
	12,12,12,13,13,12,12,12,13,13,12,13,13,13,13,12,
	13,13,13,13,11,11,11,11,11,11,12,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	13,12,12,12,13,13,11,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,13,13,12,12,12,13,13,12,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,12,13,
	13,13,13,12,13,13,13,13,12,12,12,12,12,12,12,12,
	13,13,12,12,12,13,12,12,13,13,13,13,12,13,13,13,
	13, 7,10,10,11,11,10,10,11,11,11,10,11,11,11,11,
	10,11,11,11,11,10,11,11,11,11,10,10,10,11,11,10,
	11,11,11,11,11,11,11,11,12,11,11,11,12,12,11,11,
	11,12,12,10,11,11,11,11,11,11,11,12,11,11,11,11,
	12,11,11,11,11,12,12,11,11,11,12,12,11,11,11,11,
	11,11,11,11,12,12,11,11,12,12,12,11,12,12,12,12,
	11,12,12,12,12,11,11,11,11,11,11,12,12,12,12,11,
	11,12,12,12,11,12,12,12,12,11,12,12,12,12,10,11,
	11,11,11,11,11,11,11,12,11,11,11,11,11,11,11,11,
	12,12,11,11,11,12,12,11,11,11,11,11,11,11,12,12,
	12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,
	11,11,11,11,11,11,12,11,12,12,11,11,11,12,12,11,
	12,12,12,12,11,12,12,12,12,11,11,11,11,12,11,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,11,11,11,12,12,11,12,12,12,12,11,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,10,11,10,11,11,
	11,11,11,12,12,11,11,11,12,12,11,12,12,12,12,11,
	12,12,12,12,10,11,11,12,11,11,11,12,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,11,11,11,
	12,11,11,12,12,12,12,11,12,11,12,12,12,12,12,12,
	12,12,12,12,12,12,11,12,11,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,13,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,12,12,13,12,11,11,11,12,12,12,12,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,13,13,12,12,12,13,13,11,12,12,12,12,12,
	12,12,12,13,12,12,12,12,12,12,12,13,13,13,12,13,
	12,13,13,12,12,12,12,12,12,12,12,13,13,12,12,12,
	13,13,12,13,13,13,13,12,13,13,13,13,12,12,12,12,
	12,12,12,12,13,13,12,13,12,13,13,12,13,13,13,13,
	12,13,13,13,13,11,11,11,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,11,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,12,13,12,
	13,13,12,13,13,13,13,11,12,12,12,12,12,12,12,13,
	13,12,12,12,13,12,12,13,13,13,13,12,13,13,13,13,
	12,12,12,12,12,12,12,13,13,13,12,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,12,12,12,12,12,12,13,
	13,13,13,12,12,12,13,13,13,13,13,13,13,13,13,13,
	13,13, 7,10,10,11,11,10,11,11,11,11,10,11,11,11,
	11,10,11,11,11,11,10,11,11,11,11,10,11,11,11,11,
	11,11,11,11,11,11,11,11,12,11,11,11,12,12,12,11,
	11,11,12,12,10,10,10,11,11,11,11,11,12,11,10,11,
	11,11,11,11,11,11,12,12,11,11,11,12,12,11,11,11,
	11,11,11,11,12,12,12,11,12,11,12,12,11,12,12,12,
	12,11,12,12,12,12,11,11,11,11,11,11,11,11,12,12,
	11,12,11,12,12,11,12,12,12,12,11,12,12,12,12,10,
	10,10,11,11,11,11,11,12,12,11,11,11,12,12,11,12,
	12,12,12,11,12,12,12,12,11,11,11,11,11,11,11,12,
	12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,11,11,11,11,11,11,12,12,12,12,11,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,13,12,12,
	12,13,12,11,11,11,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,10,11,11,11,
	11,11,11,11,11,11,11,11,11,11,11,11,11,11,12,12,
	11,11,11,12,12,11,11,11,11,11,11,11,12,12,12,11,
	12,11,12,12,11,12,12,12,12,11,12,12,12,12,11,11,
	11,11,11,11,11,11,12,12,11,11,11,12,12,11,12,12,
	12,12,11,12,12,12,12,11,11,11,12,11,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	11,11,11,12,11,11,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,11,11,11,12,12,11,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,12,12,12,
	13,12,11,12,12,12,12,12,12,12,12,13,12,12,12,13,
	13,12,13,13,13,13,12,13,13,13,13,11,12,12,12,12,
	12,12,12,12,13,12,12,12,12,12,12,13,13,13,13,12,
	13,13,13,13,12,12,12,12,12,12,12,13,13,13,12,13,
	12,13,13,13,13,13,13,13,13,13,13,13,13,12,12,12,
	12,12,12,13,13,13,13,12,13,12,13,13,13,13,13,13,
	13,13,13,13,13,13,11,11,11,12,12,11,12,12,12,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,
	12,13,13,12,12,12,13,13,11,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,13,12,13,12,13,
	13,12,12,12,12,12,12,12,12,13,13,12,12,12,13,13,
	13,13,13,13,13,12,13,13,13,13,12,12,12,12,12,12,
	13,12,13,13,12,13,12,13,12,12,13,13,13,13,12,13,
	13,13,13, 8,11,11,12,12,11,12,12,12,12,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,11,11,11,12,
	12,11,12,12,12,12,12,12,12,12,12,12,12,12,13,13,
	12,12,12,13,13,11,11,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,13,13,12,12,12,13,13,11,12,
	12,12,12,12,12,12,12,13,12,12,12,12,12,12,12,13,
	13,13,12,12,13,13,13,11,12,12,12,12,12,12,12,13,
	12,12,12,12,13,13,12,13,13,13,13,12,13,13,13,13,
	11,11,11,12,12,11,12,12,12,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,13,13,12,12,12,
	13,13,11,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,12,13,13,12,13,12,13,13,12,12,12,12,12,
	12,12,12,12,13,12,12,12,13,13,12,13,13,13,13,12,
	13,13,13,13,12,12,12,12,12,12,12,12,13,13,12,12,
	12,13,13,12,13,13,13,13,12,13,13,13,13,11,11,11,
	12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,13,
	12,12,12,12,12,13,11,12,12,12,12,12,12,12,12,13,
	12,12,12,12,13,12,13,13,13,13,12,13,13,13,13,11,
	12,12,12,12,12,12,12,12,13,12,12,12,13,12,12,13,
	13,13,13,12,13,13,13,13,12,12,12,12,12,12,12,12,
	13,13,12,12,13,13,13,12,13,13,13,13,12,13,13,13,
	13,12,12,12,12,12,12,13,13,13,13,12,13,12,13,13,
	12,13,13,13,13,13,13,13,13,13,11,11,11,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,11,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,13,13,13,12,13,13,13,13,11,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,13,
	12,13,12,13,13,12,12,12,12,12,12,12,12,13,13,12,
	12,12,13,13,12,13,13,13,13,12,13,13,13,13,12,12,
	12,12,12,12,13,12,13,13,12,12,12,13,13,13,13,13,
	13,13,12,13,13,13,13,11,11,11,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,12,12,12,13,12,
	11,12,12,12,12,12,12,12,12,12,12,12,12,13,13,12,
	12,13,13,13,12,13,13,13,13,11,12,12,12,12,12,12,
	12,12,13,12,12,12,13,12,12,13,13,13,13,12,13,13,
	13,13,12,12,12,12,12,12,12,12,13,13,12,12,12,13,
	13,13,13,13,13,13,13,13,13,13,13,12,12,12,12,12,
	12,13,13,13,13,12,13,12,13,13,13,13,13,13,13,13,
	13,13,13,13, 8,11,11,11,11,11,12,12,12,12,11,12,
	12,12,12,12,12,12,12,12,11,12,12,12,12,11,11,11,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,12,12,12,13,13,11,11,11,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,13,13,12,12,12,13,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,13,13,12,13,
	13,13,13,12,13,13,13,13,11,12,12,12,12,12,12,12,
	12,13,12,12,12,13,12,12,13,13,13,13,12,13,12,13,
	13,11,11,11,12,12,12,12,12,12,12,11,12,12,12,12,
	12,12,12,13,13,12,12,12,13,12,11,12,12,12,12,12,
	12,12,12,12,12,12,12,13,13,12,12,13,13,13,12,13,
	13,13,13,11,12,12,12,12,12,12,12,13,13,12,12,12,
	12,12,12,13,13,13,13,12,13,13,13,13,12,12,12,12,
	12,12,12,13,13,13,12,12,13,13,13,13,13,13,13,13,
	12,13,13,13,13,12,12,12,12,12,12,13,12,13,13,12,
	12,12,13,13,13,13,13,13,13,12,13,13,13,13,11,11,
	11,12,12,11,12,12,12,12,11,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,11,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,13,12,13,13,12,12,12,13,13,
	11,12,12,12,12,12,12,12,12,13,12,12,12,12,12,12,
	12,12,13,13,12,13,12,13,13,12,12,12,12,12,12,12,
	12,13,12,12,12,12,13,13,12,13,13,13,13,12,13,13,
	13,13,12,12,12,12,12,12,12,12,13,13,12,12,12,13,
	12,12,13,13,13,13,12,13,13,13,13,11,11,11,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,13,13,11,12,12,12,12,12,12,12,12,13,12,12,
	12,12,12,12,13,13,13,13,12,13,13,13,13,11,12,12,
	12,12,12,12,12,12,13,12,12,12,12,12,12,13,13,13,
	13,12,13,13,13,13,12,12,12,12,12,12,12,12,13,13,
	12,12,13,13,13,13,13,13,13,13,13,13,13,13,13,12,
	12,12,12,12,12,13,13,13,13,12,12,12,13,12,13,13,
	13,13,13,12,13,13,13,13,11,11,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	13,11,12,12,12,12,12,12,12,12,12,12,12,12,13,12,
	12,12,12,13,13,12,13,13,13,13,11,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,13,13,13,13,12,13,
	12,13,13,12,12,12,12,12,12,12,13,13,13,12,13,12,
	13,13,12,13,13,13,13,13,13,13,13,13,12,12,12,12,
	12,12,12,12,12,13,12,12,12,13,13,13,13,13,13,13,
	12,13,13,13,13,
};

static const static_codebook _44p9_p4_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p9_p4_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p9_p4_1,
	0
};

static const long _vq_quantlist__44p9_p5_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_p5_0[] = {
	 4, 6, 6, 9, 9, 6, 7, 8,10,11, 6, 8, 7,10,10, 8,
	10,10,12,12, 8,10,10,12,12, 6, 7, 8,10,10, 7, 8,
	 9,10,11, 8, 9, 9,11,11,10,10,11,12,13,10,11,11,
	13,13, 6, 8, 7,10,10, 8, 9, 9,11,11, 7, 9, 8,11,
	10,10,11,11,13,13,10,11,10,13,12, 9,10,10,11,12,
	10,10,11,12,13,10,11,11,12,13,12,12,13,12,14,12,
	13,13,14,14, 9,10,10,12,11,10,11,11,13,12,10,11,
	10,13,12,12,13,13,14,14,12,13,12,14,12, 7, 8, 8,
	10,11, 8, 9,10,11,12, 8, 9, 9,11,12,10,11,12,13,
	14,10,11,11,13,13, 8, 9,10,11,12, 9,10,11,12,13,
	10,10,11,12,12,11,12,12,13,14,11,12,12,14,14, 8,
	 9, 9,11,12,10,10,11,12,13, 9,10,10,12,12,11,12,
	12,14,14,11,12,12,14,13,11,11,12,12,13,11,12,12,
	13,14,12,12,13,14,14,13,13,14,14,16,13,14,14,15,
	15,11,12,11,13,13,12,12,12,14,14,11,12,12,14,13,
	13,14,14,15,15,13,14,13,15,14, 7, 8, 8,11,10, 8,
	10, 9,12,11, 8,10, 9,12,11,10,11,11,13,13,10,12,
	11,14,13, 8, 9, 9,12,11, 9,10,10,12,12,10,11,10,
	13,12,11,12,12,13,14,11,12,12,14,14, 8,10, 9,12,
	11,10,11,10,12,12, 9,11,10,13,11,11,12,12,14,14,
	11,12,12,14,13,11,11,12,13,13,11,12,12,13,14,12,
	12,12,14,14,13,13,14,14,15,13,14,14,15,15,11,12,
	11,13,12,12,12,12,14,14,11,12,12,14,13,13,14,14,
	15,15,13,14,13,15,14,10,11,11,12,13,11,12,12,13,
	14,11,12,12,13,14,13,13,14,14,16,13,14,14,15,15,
	11,12,12,12,14,12,12,13,13,15,12,13,13,13,15,14,
	14,15,15,16,14,14,15,15,16,11,12,12,13,14,12,13,
	13,14,15,12,13,13,14,14,14,14,15,15,16,14,14,14,
	15,15,13,14,14,14,15,14,14,15,15,16,14,15,15,15,
	16,15,15,16,16,18,16,16,16,17,17,13,14,14,15,15,
	14,14,15,16,16,14,14,14,16,15,16,16,16,17,17,15,
	16,16,17,16,10,11,11,13,12,11,12,12,14,13,11,12,
	12,14,13,13,14,14,15,15,13,14,13,16,14,11,12,12,
	14,13,12,13,13,14,14,12,13,13,15,14,14,14,14,15,
	15,14,15,14,16,15,11,12,12,14,12,12,13,13,15,14,
	12,13,12,15,13,14,15,14,16,15,14,15,14,16,15,13,
	14,14,15,15,14,14,14,15,16,14,15,14,16,16,15,16,
	16,16,17,16,16,16,17,17,13,14,14,15,14,14,15,15,
	16,15,14,15,14,16,15,16,16,16,17,17,15,16,15,18,
	16, 6, 8, 8,11,11, 8, 9,10,11,12, 8,10, 9,12,12,
	10,11,11,13,13,10,12,11,14,13, 8, 9, 9,11,12, 9,
	10,10,12,12, 9,10,10,12,12,11,11,12,13,14,11,12,
	12,14,14, 8,10, 9,12,11,10,11,11,12,12, 9,11,10,
	13,12,11,12,12,14,14,11,12,12,14,13,10,11,11,13,
	13,11,12,12,13,14,11,12,12,14,14,13,13,14,13,15,
	13,14,14,15,15,11,12,11,13,13,12,12,12,14,14,11,
	12,12,14,13,13,14,14,15,15,13,14,13,15,14, 8, 9,
	 9,11,11, 9,10,10,12,12, 9,10,10,12,12,11,12,12,
	13,14,11,12,12,14,14, 9, 9,10,11,12,10,10,11,12,
	13,10,10,11,12,13,12,12,13,13,15,12,12,13,14,14,
	 9,10,10,12,12,10,11,11,13,13,10,11,11,13,13,12,
	13,13,14,15,12,13,12,14,14,11,11,12,12,14,12,12,
	13,13,14,12,12,13,13,14,13,13,14,14,16,14,14,14,
	15,15,11,12,12,14,13,12,13,13,14,14,12,13,13,15,
	14,14,14,14,16,16,13,14,14,16,14, 7, 9, 9,12,11,
	 9,10,10,12,12, 9,11,10,13,12,11,12,12,13,14,11,
	13,12,14,13, 9,10,10,12,12,10,10,11,12,13,10,12,
	11,13,13,12,12,13,13,14,12,13,13,15,14, 9,10,10,
	12,12,11,11,11,13,13,10,12,10,13,12,12,13,13,14,
	15,12,13,12,15,13,11,12,12,14,13,12,12,13,13,14,
	12,13,13,15,14,13,13,14,13,16,14,15,14,16,15,12,
	12,12,14,14,13,13,13,14,14,12,13,12,14,13,14,15,
	15,16,16,13,14,13,16,13,10,11,12,13,14,11,12,13,
	13,15,12,12,13,14,14,13,14,14,15,16,13,14,14,16,
	15,12,12,13,12,14,12,12,13,13,15,13,13,13,13,15,
	14,14,15,14,16,14,15,15,15,16,12,13,12,14,14,13,
	13,13,15,15,12,13,13,15,15,14,15,15,16,16,14,15,
	15,16,16,13,14,14,13,16,14,14,15,14,16,14,14,15,
	14,16,15,15,16,15,18,16,16,16,16,17,14,14,14,16,
	15,14,15,15,16,16,14,15,15,16,16,16,16,16,17,17,
	15,16,16,17,16,10,12,11,14,13,12,13,13,14,14,12,
	13,12,15,14,14,14,14,15,15,14,15,14,16,15,12,13,
	12,14,13,12,13,13,15,14,13,14,13,15,14,14,15,15,
	16,16,14,15,15,17,15,12,13,12,14,14,13,14,14,15,
	15,13,14,13,15,14,15,15,15,16,16,14,15,15,17,15,
	14,14,14,16,15,14,15,15,16,16,14,15,15,16,15,16,
	16,16,16,17,16,17,16,18,17,14,14,14,16,15,15,15,
	15,16,16,14,15,14,16,15,16,16,17,17,17,15,16,15,
	17,16, 6, 8, 8,11,11, 8, 9,10,12,12, 8,10, 9,12,
	11,10,11,12,13,13,10,11,11,13,13, 8, 9,10,11,12,
	 9,10,11,12,13,10,11,11,12,12,11,12,12,13,14,11,
	12,12,14,14, 8, 9, 9,12,11, 9,10,10,12,12, 9,10,
	10,12,12,11,12,12,14,14,11,12,11,14,13,11,11,12,
	13,13,11,12,12,13,14,12,12,12,14,14,13,13,14,14,
	15,13,14,14,15,15,10,11,11,13,13,11,12,12,14,14,
	11,12,12,14,13,13,14,14,15,15,13,14,13,15,13, 7,
	 9, 9,11,12, 9,10,11,12,13, 9,10,10,12,12,11,12,
	13,13,14,11,12,12,14,14, 9,10,10,12,12,10,10,11,
	12,13,11,12,11,13,13,12,12,13,13,15,12,13,13,15,
	14, 9,10,10,12,12,10,11,12,13,13,10,11,10,13,12,
	12,13,13,14,15,12,13,12,14,13,12,12,12,14,14,12,
	12,13,13,14,13,13,13,15,14,14,13,14,13,16,14,15,
	15,16,16,11,12,12,13,14,12,13,13,14,15,12,13,12,
	14,13,14,14,15,15,16,13,14,13,15,13, 8, 9, 9,11,
	11, 9,10,10,12,12, 9,10,10,12,12,11,12,12,14,14,
	11,12,11,14,13, 9,10,10,12,12,10,11,11,13,13,10,
	11,11,13,13,12,12,13,14,15,12,13,13,15,14, 9,10,
	 9,12,11,10,11,10,13,12,10,11,10,13,12,12,13,12,
	14,14,12,13,12,15,13,11,12,12,13,14,12,13,13,14,
	14,12,13,13,14,14,14,14,14,14,16,14,14,14,16,15,
	11,12,11,14,12,12,13,12,15,13,12,13,12,15,13,14,
	14,14,16,15,13,14,13,16,14,10,11,12,13,14,12,12,
	13,13,15,12,13,13,14,14,14,14,15,15,16,14,14,14,
	15,16,12,12,13,14,14,12,13,14,14,15,13,14,14,15,
	15,14,15,15,15,17,15,15,15,16,16,12,12,13,13,14,
	13,13,14,14,15,12,13,13,14,15,15,15,15,15,17,14,
	15,15,15,15,14,14,14,16,16,14,15,15,15,16,15,15,
	15,16,16,16,15,16,16,18,16,16,17,17,17,14,14,14,
	15,16,15,15,15,16,17,14,15,14,16,16,16,16,17,17,
	18,16,16,15,17,16,10,12,11,14,13,12,12,12,14,14,
	11,13,12,14,13,13,14,14,15,15,13,14,13,16,15,12,
	12,13,14,14,12,13,13,15,15,13,13,13,15,15,14,15,
	15,16,16,14,15,15,17,16,12,13,12,14,12,13,13,13,
	15,13,12,13,12,15,13,14,15,15,16,15,14,15,14,16,
	14,14,14,14,16,16,14,15,15,16,16,14,15,15,16,16,
	15,16,16,16,17,16,17,16,18,17,13,14,14,16,13,14,
	15,15,16,14,14,15,14,16,14,16,16,16,17,16,15,16,
	15,18,15, 9,11,11,13,13,11,12,12,14,14,11,12,12,
	14,14,13,14,14,15,15,13,14,14,15,15,11,12,12,14,
	14,11,12,13,14,15,12,13,13,15,14,13,14,14,15,16,
	13,14,14,16,16,11,12,12,14,14,12,13,13,15,15,12,
	13,13,15,14,14,14,14,16,16,14,15,14,16,15,12,13,
	13,14,15,12,13,14,15,16,13,14,14,16,16,14,14,15,
	16,17,15,15,15,17,17,13,14,14,15,15,14,15,14,16,
	16,14,15,14,16,15,15,16,16,17,17,15,16,15,17,16,
	10,12,12,13,14,11,12,13,14,14,12,13,12,14,14,13,
	14,14,15,16,13,14,14,16,15,11,12,12,14,14,12,12,
	13,14,15,12,13,13,15,15,13,13,15,15,17,14,14,15,
	16,16,12,13,12,14,14,12,13,13,15,15,12,13,13,15,
	14,14,15,15,16,16,14,15,14,16,16,13,12,14,13,16,
	13,13,15,14,16,14,13,15,15,16,14,14,16,15,17,15,
	15,16,16,17,13,14,14,16,15,14,15,15,16,16,14,15,
	14,16,15,16,16,16,17,17,15,16,16,18,16,10,12,12,
	14,14,12,12,13,14,14,12,13,12,15,14,13,14,14,15,
	16,14,15,14,16,15,11,12,12,14,14,12,13,13,14,15,
	13,14,13,15,15,14,14,15,15,16,14,15,15,17,16,12,
	13,13,14,14,13,13,14,15,15,12,14,13,15,15,14,15,
	15,16,16,14,15,15,17,15,13,14,13,15,15,13,14,14,
	15,16,14,15,14,17,16,15,15,15,15,17,16,16,16,18,
	17,14,14,14,16,16,15,15,15,16,16,14,15,14,16,16,
	16,16,17,17,17,16,16,16,17,16,11,12,13,14,14,12,
	13,13,15,15,12,13,13,15,15,14,15,15,16,16,14,15,
	15,17,16,12,13,13,14,15,13,13,14,14,16,13,14,14,
	15,16,15,14,16,15,17,15,15,16,16,17,12,13,13,15,
	15,13,14,14,16,16,13,14,14,16,15,15,15,16,17,17,
	15,16,15,17,16,14,14,15,13,16,15,14,16,14,17,15,
	15,16,14,17,16,15,17,15,18,16,16,17,16,18,14,15,
	15,17,16,15,16,16,17,17,15,16,15,17,16,16,17,17,
	18,18,16,17,15,18,16,11,12,12,14,14,13,13,14,14,
	15,13,14,13,16,14,15,15,15,16,16,15,16,15,17,16,
	12,13,13,15,14,13,13,14,15,15,14,15,14,16,15,15,
	15,16,15,16,16,16,16,18,16,12,13,13,15,15,14,14,
	15,15,16,13,14,13,16,15,16,16,16,17,17,15,16,15,
	17,15,14,15,14,16,15,14,15,15,16,16,15,16,15,17,
	16,16,15,16,15,17,17,18,17,18,17,15,15,15,16,16,
	16,16,16,17,17,14,15,15,17,16,17,17,18,18,18,16,
	17,15,18,15, 9,11,11,13,13,11,12,12,14,14,11,12,
	12,14,14,13,14,14,15,16,13,14,14,15,15,11,12,12,
	14,14,12,13,13,14,15,12,13,13,14,14,14,14,15,15,
	16,14,14,14,16,16,11,12,12,14,14,12,13,13,14,15,
	11,13,12,14,14,13,14,14,16,16,13,14,14,16,15,13,
	14,14,15,15,14,14,15,15,16,14,15,14,16,16,15,15,
	16,16,17,15,16,16,17,17,12,13,13,15,15,13,14,14,
	16,15,12,14,13,16,15,15,16,15,17,17,14,15,15,17,
	15,10,12,12,14,14,12,12,13,14,15,12,13,12,14,14,
	14,14,15,15,16,13,14,14,16,16,12,13,13,14,14,13,
	13,14,14,15,13,14,13,15,15,14,15,15,15,17,14,15,
	15,16,16,11,12,12,14,14,13,13,14,15,15,12,13,13,
	15,14,14,15,15,16,17,14,15,14,16,15,14,14,14,16,
	16,14,15,15,16,16,15,15,15,16,16,15,16,16,16,18,
	16,17,16,18,17,13,13,14,15,15,14,14,15,16,16,13,
	14,14,16,15,16,16,17,17,17,15,15,15,17,15,10,12,
	12,14,13,12,12,13,14,14,11,13,12,14,14,13,14,14,
	16,16,13,14,14,16,15,12,12,13,14,14,12,13,13,14,
	15,13,13,13,15,15,14,14,15,16,16,14,15,15,16,16,
	11,12,12,14,14,12,13,13,15,15,12,13,12,15,14,14,
	15,14,16,16,13,15,13,16,15,13,14,14,15,16,14,15,
	15,15,17,14,15,15,16,16,16,15,16,16,17,16,16,16,
	17,17,13,14,12,16,13,14,15,13,16,15,13,15,13,16,
	14,15,16,15,17,16,15,16,14,17,15,11,12,12,14,15,
	13,13,14,14,16,13,14,13,15,14,15,15,16,16,17,15,
	15,15,16,16,12,13,13,15,15,13,13,14,15,16,14,15,
	14,16,15,15,15,16,15,17,16,16,16,17,17,12,13,13,
	14,15,14,14,15,15,16,13,14,13,15,15,16,16,16,17,
	17,15,16,15,16,15,15,15,15,16,16,14,15,15,16,17,
	16,16,16,17,17,16,15,17,15,18,17,18,17,18,18,14,
	14,15,15,17,15,15,16,16,17,14,15,15,16,16,17,17,
	17,17,18,16,16,15,17,15,11,12,12,14,14,12,13,13,
	15,15,12,13,13,15,15,14,15,15,16,16,14,15,14,17,
	16,13,13,13,15,15,13,14,14,15,16,13,14,14,16,16,
	15,15,16,16,17,15,16,16,17,17,12,13,13,15,14,13,
	14,14,16,15,13,14,13,16,14,15,16,16,17,16,15,16,
	14,17,15,14,15,15,16,17,15,15,16,16,17,15,16,16,
	17,17,16,15,17,16,18,16,17,17,18,18,14,15,14,16,
	13,15,16,15,17,14,15,16,14,17,14,16,17,16,18,16,
	16,17,15,18,15,
};

static const static_codebook _44p9_p5_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p9_p5_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44p9_p5_0,
	0
};

static const long _vq_quantlist__44p9_p5_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44p9_p5_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44p9_p5_1 = {
	1, 7,
	(char *)_vq_lengthlist__44p9_p5_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44p9_p5_1,
	0
};

static const long _vq_quantlist__44p9_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p6_0[] = {
	 2, 5, 5, 5, 7, 7, 5, 7, 7, 5, 7, 7, 7, 8, 9, 7,
	 9, 9, 5, 7, 7, 7, 9, 9, 7, 9, 8, 5, 7, 8, 8, 9,
	10, 8, 9,10, 8, 9,10,10,10,12,10,11,11, 8,10,10,
	10,11,12,10,11,10, 5, 8, 7, 8,10,10, 8,10, 9, 8,
	10,10,10,10,11,10,12,11, 8,10, 9,10,11,11,10,12,
	10, 5, 8, 8, 7, 9,10, 8,10, 9, 7, 9,10, 9,10,11,
	 9,11,11, 8,10, 9,10,11,11, 9,11,10, 7, 9, 9, 9,
	10,11, 9,11,11, 9, 9,11,10,10,13,11,12,12, 9,11,
	11,11,12,13,11,13,11, 7, 9, 9, 9,10,11, 9,11,10,
	 9,11,10,10,10,12,11,13,12, 9,11,11,11,12,12,10,
	12,10, 5, 8, 8, 8, 9,10, 7,10, 9, 8, 9,10, 9,10,
	11,10,11,11, 7,10, 9, 9,11,11, 9,11,10, 7, 9, 9,
	 9,10,11, 9,11,10, 9,11,11,10,10,12,11,12,12, 9,
	10,11,11,12,13,10,12,10, 7, 9, 9, 9,11,11, 9,11,
	10, 9,11,11,11,11,13,11,13,12, 9,11, 9,11,12,12,
	10,13,10,
};

static const static_codebook _44p9_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p6_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44p9_p6_0,
	0
};

static const long _vq_quantlist__44p9_p6_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44p9_p6_1[] = {
	 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 8, 8, 7,
	 8, 8, 7, 8, 7, 7, 8, 8, 7, 8, 8, 7, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9,
	 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 9, 8, 8,
	 8, 8, 9, 9, 8, 9, 9, 7, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 9, 8,
	 9, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 8,
	 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 9, 9, 8,
	 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
	 8, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8, 8, 8, 8, 9, 8,
	 8, 9, 8,
};

static const static_codebook _44p9_p6_1 = {
	5, 243,
	(char *)_vq_lengthlist__44p9_p6_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44p9_p6_1,
	0
};

static const long _vq_quantlist__44p9_p7_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_p7_0[] = {
	 1,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,
	12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,
	13,13,13,13,13,
};

static const static_codebook _44p9_p7_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44p9_p7_0,
	1, -510105088, 1635281408, 3, 0,
	(long *)_vq_quantlist__44p9_p7_0,
	0
};

static const long _vq_quantlist__44p9_p7_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44p9_p7_1[] = {
	 1, 4, 4,16,16, 4, 9,11,15,16, 4,12, 8,16,16,12,
	16,16,16,16,13,16,16,16,16, 5, 8,10,16,16, 9, 9,
	14,15,16,12,14,14,16,16,16,16,16,16,16,16,16,16,
	16,16, 5,11, 8,16,15,12,14,16,16,16, 9,15, 9,16,
	16,16,16,16,16,16,16,16,16,16,16,15,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16, 6,11,11,
	16,16,12,13,16,16,16,12,16,14,16,16,16,16,16,16,
	16,16,16,16,16,16,11,15,15,16,16,14,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,12,
	15,16,16,16,16,16,16,16,16,14,16,15,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16, 5,11,11,16,16,12,
	15,16,16,16,12,16,14,16,16,16,16,16,16,16,16,16,
	16,16,16,12,15,15,16,16,14,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,11,15,15,16,
	16,16,16,16,16,16,15,16,14,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16, 6,11,12,16,16,11,15,16,16,16,13,16,14,16,16,
	16,16,16,16,16,16,16,16,16,16,11,16,14,16,16,14,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,12,14,14,16,16,16,16,16,16,16,15,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,15,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16, 8,13,
	15,16,16,15,15,16,16,16,14,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,14,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	15,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16, 7,12,12,16,16,
	13,12,16,16,16,14,16,14,16,16,16,16,16,16,16,16,
	16,16,16,16,13,16,16,16,16,14,14,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,12,14,16,
	16,16,16,16,16,16,16,14,16,14,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16, 6,11,11,16,16,13,15,16,16,16,11,15,14,16,
	16,16,16,16,16,16,14,16,16,16,16,11,16,16,16,16,
	16,16,16,16,16,15,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,11,16,14,16,16,14,16,16,16,16,13,15,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16, 7,
	11,11,16,16,13,13,16,16,16,13,16,13,16,16,16,16,
	16,16,16,16,16,16,16,16,12,16,15,16,16,14,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,12,14,16,16,16,16,16,16,16,16,14,16,13,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16, 8,13,14,16,
	16,15,16,16,16,16,14,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,15,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,15,16,
	15,16,16,16,16,16,16,16,16,16,15,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,15,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,15,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,
	16,16,16,16,16,
};

static const static_codebook _44p9_p7_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44p9_p7_1,
	1, -514619392, 1630767104, 3, 0,
	(long *)_vq_quantlist__44p9_p7_1,
	0
};

static const long _vq_quantlist__44p9_p7_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p9_p7_2[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9,10,10,10,11,11,11,
	12,12,12,13,13,13,13,13,13,
};

static const static_codebook _44p9_p7_2 = {
	1, 25,
	(char *)_vq_lengthlist__44p9_p7_2,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44p9_p7_2,
	0
};

static const long _vq_quantlist__44p9_p7_3[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44p9_p7_3[] = {
	 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44p9_p7_3 = {
	1, 25,
	(char *)_vq_lengthlist__44p9_p7_3,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44p9_p7_3,
	0
};

static const char _huff_lengthlist__44p9_short[] = {
	 3, 3, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _huff_book__44p9_short = {
	1, 8,
	(char *)_huff_lengthlist__44p9_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44pn1_l0_0[] = {
	6,
	5,
	7,
	4,
	8,
	3,
	9,
	2,
	10,
	1,
	11,
	0,
	12,
};

static const char _vq_lengthlist__44pn1_l0_0[] = {
	 1, 3, 3, 8, 8,10,10,10,10,10,10,10,10, 5, 7, 5,
	 9, 8,10,10,10,10,11,10,11,10, 5, 5, 7, 8, 9,10,
	10,11,10,10,11,10,11,10,10,10,11,11,11,11,11,11,
	11,10,11,11,10,10,10,10,11,11,11,11,11,10,11,11,
	11,11,11,11,11,11,12,11,10,11,11,11,11,11,11,11,
	11,11,11,11,11,10,10,11,11,12,11,11,11,11,11,11,
	12,11,11,11,10,11,11,11,11,11,11,11,11,10,11,11,
	10,11,10,11,11,11,11,11,11,11,11,11,11,12,11,11,
	12,12,11,11,11,11,11,11,11,11,11,11,11,11,12,11,
	10,11,11,11,11,11,11,11,12,11,13,11,11,11,11,11,
	11,11,11,11,11,11,12,11,13,
};

static const static_codebook _44pn1_l0_0 = {
	2, 169,
	(char *)_vq_lengthlist__44pn1_l0_0,
	1, -526516224, 1616117760, 4, 0,
	(long *)_vq_quantlist__44pn1_l0_0,
	0
};

static const long _vq_quantlist__44pn1_l0_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44pn1_l0_1[] = {
	 1, 4, 4, 7, 7, 4, 5, 6, 7, 7, 4, 6, 5, 7, 7, 7,
	 6, 7, 6, 7, 7, 7, 6, 7, 6,
};

static const static_codebook _44pn1_l0_1 = {
	2, 25,
	(char *)_vq_lengthlist__44pn1_l0_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44pn1_l0_1,
	0
};

static const long _vq_quantlist__44pn1_l1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_l1_0[] = {
	 1, 4, 4, 4, 4, 4, 4, 4, 4,
};

static const static_codebook _44pn1_l1_0 = {
	2, 9,
	(char *)_vq_lengthlist__44pn1_l1_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44pn1_l1_0,
	0
};

static const char _huff_lengthlist__44pn1_lfe[] = {
	 1, 3, 2, 3,
};

static const static_codebook _huff_book__44pn1_lfe = {
	2, 4,
	(char *)_huff_lengthlist__44pn1_lfe,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const char _huff_lengthlist__44pn1_long[] = {
	 2, 3, 6, 7, 9,13,17, 3, 2, 5, 7, 9,13,17, 6, 5,
	 5, 6, 9,12,16, 7, 7, 6, 6, 7,10,13,10,10, 9, 7,
	 6,10,13,13,13,12,10,10,11,15,17,17,17,14,14,15,
	17,
};

static const static_codebook _huff_book__44pn1_long = {
	2, 49,
	(char *)_huff_lengthlist__44pn1_long,
	0, 0, 0, 0, 0,
	NULL,
	0
};

static const long _vq_quantlist__44pn1_p1_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p1_0[] = {
	 1, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44pn1_p1_0 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p1_0,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44pn1_p1_0,
	0
};

static const long _vq_quantlist__44pn1_p2_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p2_0[] = {
	 1, 5, 5, 0, 7, 7, 0, 8, 8, 0, 9, 9, 0,12,12, 0,
	 8, 8, 0, 9, 9, 0,13,13, 0, 8, 8, 0, 6, 6, 0,11,
	11, 0,12,12, 0,12,12, 0,14,14, 0,11,12, 0,12,12,
	 0,15,15, 0,12,12, 0, 5, 5, 0, 5, 5, 0, 6, 6, 0,
	 7, 7, 0,10,10, 0, 6, 6, 0, 7, 7, 0,11,11, 0, 6,
	 6, 0, 7, 7, 0,11,11, 0,12,11, 0,11,11, 0,14,14,
	 0,10,10, 0,12,12, 0,15,15, 0,12,12, 0, 6, 6, 0,
	12,12, 0,12,12, 0,12,12, 0,14,14, 0,11,11, 0,12,
	12, 0,16,16, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 8, 8, 0,12,12, 0,12,12, 0,12,12, 0,15,
	15, 0,12,12, 0,11,11, 0,16,16, 0,11,11, 0, 6, 6,
	 0,12,12, 0,12,12, 0,13,13, 0,15,15, 0,12,12, 0,
	13,13, 0,15,15, 0,12,12, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44pn1_p2_0 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p2_0,
	1, -533200896, 1614282752, 2, 0,
	(long *)_vq_quantlist__44pn1_p2_0,
	0
};

static const long _vq_quantlist__44pn1_p2_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p2_1[] = {
	 1, 3, 3, 0, 9, 9, 0, 9, 9, 0,10,10, 0, 9, 9, 0,
	10,10, 0,10,10, 0,10,10, 0,10,10, 0, 7, 7, 0, 7,
	 7, 0, 6, 6, 0, 8, 8, 0, 7, 7, 0, 8, 8, 0, 8, 8,
	 0, 7, 7, 0, 8, 8, 0, 7, 7, 0, 9, 9, 0, 8, 9, 0,
	10,10, 0, 9, 9, 0,10,10, 0,10,11, 0, 9, 9, 0,10,
	10, 0, 9, 9, 0,11,11, 0,12,12, 0,12,12, 0,11,11,
	 0,12,12, 0,13,13, 0,12,12, 0,13,13, 0, 8, 8, 0,
	12,12, 0,12,12, 0,13,13, 0,13,13, 0,13,13, 0,13,
	13, 0,13,13, 0,13,13, 0, 7, 7, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0, 9, 9, 0,11,11, 0,12,12, 0,13,13, 0,12,
	12, 0,13,13, 0,13,13, 0,12,12, 0,12,12, 0, 9, 9,
	 0,12,12, 0,13,13, 0,14,14, 0,13,13, 0,14,14, 0,
	14,14, 0,13,13, 0,14,14, 0, 7, 7, 0, 0, 0, 0, 0,
	 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,
};

static const static_codebook _44pn1_p2_1 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p2_1,
	1, -535822336, 1611661312, 2, 0,
	(long *)_vq_quantlist__44pn1_p2_1,
	0
};

static const long _vq_quantlist__44pn1_p3_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p3_0[] = {
	 1, 6, 6, 6, 8, 8, 6, 8, 8, 7, 9, 9,10,11,11, 8,
	 8, 8, 7, 9, 9,11,12,12, 9, 9, 9, 6, 7, 7,10,11,
	11,10,11,11,10,11,11,13,13,13,12,12,12,10,12,11,
	14,14,14,12,12,12, 6, 5, 5, 9, 6, 6, 9, 6, 6, 9,
	 7, 7,12,10,10,11, 7, 6, 9, 7, 7,13,11,11,12, 7,
	 7, 7, 8, 8,12,10,10,12,10,10,11,10,10,15,13,13,
	13, 9, 9,12,11,11,15,14,14,15,11,11, 8, 7, 7,12,
	11,11,12,11,11,11,11,11,14,13,14,14,12,12,12,11,
	11,16,15,15,14,12,12, 0,10,10, 0,12,12, 0,12,12,
	 0,11,11, 0,14,14, 0,11,11, 0,11,11, 0,15,15, 0,
	11,11, 7, 8, 8,13,11,11,12,10,10,12,11,11,15,13,
	13,14,11,11,12,10,10,16,14,14,15,10,10, 9, 7, 7,
	13,11,12,13,12,11,12,11,11,15,14,14,14,12,12,13,
	12,12,16,15,15,15,12,12, 0,11,11, 0,12,12, 0,12,
	13, 0,12,12, 0,15,15, 0,12,12, 0,12,12, 0,16,15,
	 0,12,12,
};

static const static_codebook _44pn1_p3_0 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p3_0,
	1, -531365888, 1616117760, 2, 0,
	(long *)_vq_quantlist__44pn1_p3_0,
	0
};

static const long _vq_quantlist__44pn1_p3_1[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44pn1_p3_1[] = {
	 2, 3, 4, 9, 9,10,12,12,12,11,10,12,12,13,12,11,
	13,12,11,11,11,12,12,12,11,11,13,13,13,13,11,12,
	12,14,14,12,13,13,13,13,11,13,13,13,13,11,13,13,
	13,13,11,13,13,13,13,11,12,12,14,14,12,13,13,12,
	12,11,13,13,13,13,11,13,13,12,12,11,13,13,13,13,
	12,12,13,14,14,12,13,13,12,12,11,13,13,13,13,11,
	13,13,12,12,11,13,13,13,13,12,13,13,14,14,12,13,
	13,12,12,11,13,13,13,13,11,13,13,12,12,11,10,10,
	10,10,12,10,10,11,11,12, 9, 9,11,11,13,11,11,10,
	10,13,10,10,10,10,13,11,11,12,12,13,10,10,12,12,
	14,12,11,12,12,13,11,11,11,12,13,12,12,12,12,13,
	11,11,12,12,13,10,10,12,12,14,11,11,12,12,13,11,
	11,12,12,13,11,11,12,12,14,12,12,12,12,14,10,10,
	11,11,14,12,11,11,11,13,11,11,11,11,13,12,12,11,
	11,14,12,12,12,11,14,10,10,11,11,14,12,11,11,11,
	13,11,11,11,11,13,12,12,11,11,11,11,11,10,10,12,
	10,11, 9, 9,12,12,12,11,11,13,12,12, 9, 9,13,13,
	13,10,10,13,13,13,12,12,13,13,13,14,14,13,12,12,
	11,11,14,13,13,12,12,14,13,13,11,11,13,13,13,12,
	11,13,13,13,14,14,13,12,12,10,10,14,13,13,11,11,
	13,13,13,10,10,13,13,13,11,11,14,13,13,14,14,14,
	12,12,10,10,13,13,13,11,11,13,13,13,10,10,13,13,
	13,11,11,14,13,13,14,14,14,13,13,10,10,13,13,13,
	11,11,13,13,13,10,10,14,12,12, 8, 8,14,12,12, 9,
	 9,14,11,11, 9, 9,14,12,12, 8, 8,14,12,12, 7, 7,
	15,13,13,10,10,15,12,12,10,10,15,13,13,10,10,15,
	12,13, 9, 9,15,13,13,10,10,15,13,13,10,10,15,12,
	12,10,10,15,13,13,10,10,15,13,13, 9, 9,15,13,13,
	10,10,15,13,13,10,10,15,12,12,10,10,15,13,13, 9,
	 9,14,13,12, 9, 9,14,13,13, 9, 9,15,13,13,10,10,
	15,12,12,10,10,15,13,13, 9, 9,15,13,13, 9, 9,14,
	13,13, 9, 9,14,12,12, 8, 8,13,13,13, 8, 8,14,14,
	13, 9, 9,14,14,13, 7, 7,14,14,14, 8, 8,14,14,14,
	10,10,15,14,14,12,12,14,14,14, 9, 9,15,14,14,10,
	10,14,14,14, 9, 9,14,14,14,10, 9,15,14,14,12,12,
	14,14,14, 9, 9,15,14,14,10,10,14,14,14, 9, 9,15,
	14,15, 9, 9,15,14,14,11,11,14,14,14, 8, 8,14,14,
	14, 9, 9,14,14,14, 8, 8,14,15,14,10,10,15,14,14,
	11,11,14,14,14, 8, 8,15,14,14, 9, 9,14,14,14, 8,
	 8,12,12,12,13,13,16,16,15,12,12,17,16,16,13,13,
	17,16,16,11,11,17,16,16,12,12,17,16,17,13,13,17,
	16,16,14,14,17,17,16,12,12,18,16,16,13,13,17,16,
	17,12,12,17,17,17,13,13,18,16,16,14,14,18,17,17,
	12,12,17,17,17,13,13,18,17,17,13,13,17,17,17,13,
	13,17,16,16,14,14,17,17,17,12,12,16,16,17,13,13,
	17,17,16,12,12,18,17,17,13,13,18,16,16,14,14,18,
	17,17,12,12,19,16,17,13,13,17,16,17,12,12,13,14,
	14,10,10,16,14,14,13,13,17,15,15,14,14,17,14,14,
	13,13,16,14,14,13,13,17,16,15,14,14,16,16,16,15,
	15,17,15,15,14,14,17,15,15,14,14,17,15,15,14,14,
	17,16,15,14,14,16,16,16,15,15,18,15,15,13,13,16,
	16,15,14,14,17,15,15,14,13,17,15,15,14,14,16,16,
	16,15,15,18,15,14,13,13,17,15,15,14,14,18,14,15,
	13,13,18,15,15,14,14,16,16,16,15,15,17,15,15,13,
	13,17,15,15,14,14,17,15,15,13,13,13,11,11,10,10,
	16,14,14,13,13,17,14,15,14,14,17,15,15,12,12,17,
	14,14,12,12,16,15,15,14,14,16,14,14,14,14,16,15,
	15,14,14,16,15,15,14,14,16,15,15,14,14,16,15,15,
	14,14,16,15,14,15,15,17,15,15,14,14,17,15,15,14,
	14,17,15,15,14,14,17,15,16,14,14,16,14,14,14,14,
	17,15,15,13,13,17,15,15,13,13,16,15,15,13,13,17,
	16,16,14,14,17,15,14,15,14,17,15,15,13,13,17,15,
	15,13,13,17,15,15,13,13,14,14,14, 9, 9,14,14,14,
	18,19,14,15,15,19,18,14,14,14,19,19,15,14,14,19,
	19,15,16,16,19,19,15,16,16,19,19,15,15,15,19,19,
	15,16,16,19,20,15,15,15,19,19,15,15,15,19,19,15,
	16,16,20,20,15,15,15,18,19,15,15,16,19,20,15,15,
	15,19,18,15,15,15,18,18,15,16,16,21,20,15,15,15,
	19,19,15,15,15,19,19,15,15,14,19,20,15,15,15,20,
	19,15,16,16,19,20,15,15,15,19,19,15,15,15,20,21,
	15,14,15,19,19,14,12,12, 9, 9,14,14,15,21,19,14,
	14,14,18,19,14,15,15,19,20,14,14,14,19,19,15,15,
	15,19,20,15,15,14,21,19,15,15,15,20,19,15,14,15,
	20,21,15,15,15,18,18,15,15,15,20,21,16,14,14,18,
	19,15,15,15,20,19,15,15,15,18,21,15,15,15,19,19,
	15,15,15,19,20,16,15,14,20,19,15,16,15,19,19,15,
	15,15,19, 0,14,15,15,19,19,15,15,15,19,19,15,15,
	14,20,19,15,15,15,20,19,15,15,15,19,19,15,15,15,
	20,19,12,12,12,13,13,16,15,16,11,11,16,16,16,12,
	12,17,16,16,11,11,17,16,16,12,11,17,17,17,13,13,
	18,16,16,14,14,18,18,17,13,13,17,16,16,13,13,17,
	17,17,13,13,17,16,17,12,12,17,15,16,13,13,17,16,
	17,12,12,17,16,16,13,12,17,16,16,12,12,18,17,17,
	13,13,18,16,16,13,14,18,17,17,12,12,17,16,16,12,
	12,17,17,17,12,12,18,17,17,13,13,17,16,16,14,14,
	17,17,17,12,12,17,16,16,12,12,18,17,17,12,12,13,
	14,14, 9, 9,16,14,14,13,13,16,15,15,14,14,16,14,
	14,13,13,16,14,14,13,13,17,16,15,15,15,16,15,16,
	16,15,17,15,15,14,14,17,15,15,15,15,17,15,15,14,
	14,17,15,15,14,14,16,15,16,16,16,17,15,15,14,14,
	16,15,15,14,15,16,15,15,14,14,17,15,15,15,15,16,
	16,16,15,16,18,15,14,13,14,17,15,15,14,14,17,14,
	14,13,13,17,15,15,14,14,16,15,15,15,15,17,15,14,
	14,14,17,15,15,14,14,17,14,14,13,13,13,11,11,11,
	11,16,14,14,12,12,16,14,14,13,13,16,14,14,12,12,
	16,14,14,12,12,16,15,15,13,13,17,14,14,14,14,17,
	15,15,13,13,16,15,15,14,13,16,15,15,13,13,16,15,
	15,13,13,16,14,14,14,14,16,15,15,13,13,16,14,15,
	13,13,17,15,15,13,13,17,15,15,13,13,16,14,14,14,
	14,17,15,15,12,12,17,14,15,13,13,17,15,15,12,12,
	16,15,15,13,13,17,14,14,14,14,17,15,15,12,12,17,
	15,15,13,13,16,15,15,12,12,14,15,15, 8, 8,14,14,
	14,19,18,14,15,15,19,20,14,14,14,19,19,14,14,15,
	19,20,15,16,15,19,21,15,16,16,21,19,15,15,15,20,
	19,15,16,16,19,20,15,15,15,19,18,15,16,15,20,19,
	15,16,16,19,20,15,15,15,19,19,15,16,15,20,20,14,
	15,15,19,19,15,15,15,21,19,15,17,16,19,20,15,14,
	15, 0,21,15,15,15,19,20,14,14,14,19,19,15,15,15,
	20,19,15,16,16,19,19,15,15,15,19,18,15,15,15,20,
	19,14,14,15,18,18,14,12,12, 9, 9,14,14,14,18,18,
	14,14,14,18,18,14,15,14,19,18,14,14,14,19,18,15,
	15,15,19,20,15,14,14,18,18,15,15,15,20,19,15,15,
	15,18,20,15,15,15,19,18,15,15,15,19,19,15,14,14,
	19,21,15,15,15,20,20,15,15,15,18,19,14,15,15,19,
	20,15,15,15,20,19,15,14,14,19,21,15,15,15,18,19,
	15,14,15,20,19,14,15,15,21,21,14,15,15,19,20,15,
	14,14,19,20,15,15,15,19,20,15,15,14,20,20,14,15,
	15,20,19,13,12,12,13,13,17,16,16,11,11,17,16,16,
	12,12,18,17,16,11,11,18,16,16,11,11,17,17,17,13,
	13,18,16,16,13,13,18,17,17,12,12,18,16,16,13,13,
	18,17,17,12,12,18,17,17,13,13,18,16,16,14,14,18,
	16,17,12,12,18,17,17,13,13,17,17,17,12,12,17,17,
	17,12,12,17,16,15,13,13,18,16,16,11,11,17,16,16,
	12,12,17,16,17,11,11,18,17,17,13,12,17,16,16,13,
	13,17,17,17,12,12,17,16,17,12,12,18,17,17,11,11,
	14,14,14, 9, 9,16,14,14,13,13,17,15,15,14,14,17,
	14,14,13,13,16,14,14,13,13,17,15,15,14,14,16,16,
	16,16,15,18,15,15,14,14,17,16,15,15,15,17,15,15,
	14,14,17,15,15,14,15,16,16,16,15,16,18,15,15,14,
	14,17,15,15,14,15,17,15,15,14,14,17,15,15,14,14,
	16,16,16,15,16,17,14,14,13,13,17,15,15,14,14,18,
	15,15,13,13,17,15,15,14,14,16,16,16,15,15,17,14,
	14,13,13,17,15,15,14,14,17,14,14,13,13,13,11,11,
	11,11,16,14,14,12,12,16,14,14,12,13,17,15,14,11,
	11,17,14,14,11,11,17,15,15,13,14,17,14,14,14,14,
	17,15,15,13,13,17,14,14,13,13,17,15,15,13,13,17,
	15,15,13,13,17,14,14,14,14,17,15,15,13,13,18,14,
	15,13,13,17,15,15,13,13,16,15,15,13,13,17,14,14,
	13,13,17,15,15,12,12,16,14,14,12,12,16,15,15,12,
	12,17,16,15,13,13,17,14,14,13,13,17,15,15,12,12,
	16,15,15,12,12,16,15,15,12,12,13,15,15, 8, 8,14,
	14,14,18,19,14,15,15,19,20,14,14,14,18,18,14,15,
	15,18,18,15,16,16,19,19,15,16,17,20,20,15,15,15,
	19,19,15,16,16,18,20,15,15,15,19,19,15,15,16,18,
	18,15,17,16,19,19,15,15,15,18,21,15,16,16,21,20,
	15,15,15,19,21,15,16,15,20,19,15,16,17,20,20,15,
	15,15,19,19,15,16,16,21,20,15,15,15,19,20,15,15,
	15,19,19,15,16,16,20,19,15,15,15,19,19,15,16,15,
	20,21,15,15,15,21,19,14,12,12, 8, 8,14,14,14,20,
	18,14,13,13,19,19,14,14,14,19,18,15,14,14,19,20,
	14,15,15,20,20,15,14,14,21,20,15,15,15,20,20,15,
	15,14,21,19,15,15,15,19,19,15,15,15,19,20,15,14,
	14,20,20,15,15,15,19,20,15,14,14,19,20,15,15,15,
	20,20,15,15,15,20,19,15,14,14,20,21,15,15,15,20,
	21,15,14,14,20, 0,15,16,15,20,21,15,15,15,19,20,
	15,14,14,19,19,15,15,15,19,20,15,15,15,19,19,15,
	15,15,18,20,13,12,12,13,13,18,16,17,12,12,17,16,
	16,12,12,17,17,16,11,11,18,16,16,11,11,17,17,18,
	13,13,18,16,16,14,14,18,17,17,13,13,18,16,16,13,
	13,18,17,17,12,12,17,17,16,13,13,17,16,16,13,14,
	18,17,17,12,12,18,16,16,12,13,17,16,17,12,12,17,
	18,17,13,13,18,16,16,13,13,18,17,17,12,12,17,16,
	16,12,12,17,17,17,11,11,17,16,17,12,12,17,16,16,
	13,13,17,16,16,11,11,17,16,16,12,12,18,16,17,11,
	11,14,14,14, 9, 9,16,14,15,13,13,17,15,15,14,14,
	17,14,14,12,12,16,14,14,13,13,18,15,15,15,15,17,
	15,16,15,16,18,15,15,14,14,17,15,16,15,15,17,15,
	15,14,14,18,15,15,14,14,16,16,16,16,15,17,15,15,
	14,14,16,15,15,14,14,17,15,15,14,14,17,15,15,14,
	14,17,16,16,15,15,17,15,14,13,13,17,15,15,14,14,
	17,15,15,13,13,17,15,15,14,14,16,16,16,15,15,18,
	15,14,14,14,17,15,15,14,14,18,15,15,13,13,13,12,
	12,11,11,16,14,14,12,12,16,14,14,13,13,17,15,15,
	12,12,17,14,14,12,12,17,15,15,14,14,17,14,14,14,
	14,17,15,15,13,13,17,15,14,13,13,17,15,15,13,13,
	17,15,15,13,13,16,14,14,14,14,17,15,15,13,13,16,
	14,14,13,13,16,15,15,13,13,17,15,16,13,13,17,14,
	14,14,13,17,15,15,12,12,16,15,14,12,12,17,15,15,
	12,12,16,15,16,13,13,16,14,14,14,13,17,15,15,12,
	12,16,14,14,12,12,17,15,15,12,12,14,15,15, 8, 8,
	14,14,14,18,18,14,15,15,19,18,14,14,14,18,18,14,
	15,15,19,20,15,16,15,21,18,15,16,16,18, 0,15,15,
	15,19,20,15,16,16,20, 0,15,16,15,19,18,15,15,15,
	19,19,15,16,16,21,19,15,15,15,19,19,15,16,16,20,
	20,15,15,15,19,19,15,15,15,19,18,15,16,16,20,20,
	15,14,15,20,19,15,15,15,19,20,15,15,15,19,19,15,
	16,15,19,20,15,16,16,19,20,15,15,15,19,19,15,16,
	15,20,20,15,15,15,20,18,13,12,12, 8, 8,14,14,14,
	19,20,14,14,14,19,19,14,15,15,20,20,14,14,14,18,
	19,15,15,15,20, 0,15,14,14,18,20,15,15,15,19,19,
	15,15,15,21,19,15,15,15,19,20,15,15,15,20,21,15,
	14,14,20,19,15,15,15,20,19,15,15,14,21,19,15,15,
	15,19,18,15,15,15,20,19,15,14,14,19,19,15,15,16,
	20,19,15,15,15,20, 0,15,15,15,19,21,15,15,15,22,
	20,15,14,14,22,19,15,15,15,19,20,15,14,14,20,19,
	14,15,15,19,21,
};

static const static_codebook _44pn1_p3_1 = {
	5, 3125,
	(char *)_vq_lengthlist__44pn1_p3_1,
	1, -533725184, 1611661312, 3, 0,
	(long *)_vq_quantlist__44pn1_p3_1,
	0
};

static const long _vq_quantlist__44pn1_p4_0[] = {
	2,
	1,
	3,
	0,
	4,
};

static const char _vq_lengthlist__44pn1_p4_0[] = {
	 1, 7, 7,14,14, 6, 8, 8,15,16, 7, 8, 8,16,15, 0,
	14,14,17,17, 0,14,14,16,16, 7, 9, 9,16,16,10,11,
	11,17,18, 9, 8, 8,16,16, 0,14,14,19,19, 0,14,14,
	17,16, 8, 9, 9,16,16,12,12,12,17,17,10, 9, 9,16,
	16, 0,15,14,18,20, 0,14,14,17,17, 0,15,15,18,17,
	 0,21, 0, 0,21, 0,13,13,17,17, 0,17,17, 0, 0, 0,
	15,15,17,17, 0,15,15,17,18, 0, 0, 0, 0,21, 0,13,
	13,17,17, 0,18,18, 0,21, 0,16,15,17,18, 6, 7, 7,
	14,14, 9,10,10,16,16,11,10,10,15,15, 0,21, 0,20,
	21, 0, 0, 0,18,20,10,10,10,15,16,12,13,13,18,18,
	12,11,11,15,15, 0, 0, 0,20,20, 0, 0,21,19,19,12,
	11,11,15,15,15,14,14,18,18,13,11,11,15,16, 0, 0,
	 0,20,19, 0, 0, 0,20,21, 0, 0,20,19,19, 0, 0, 0,
	 0, 0, 0,20, 0,17,18, 0, 0,21, 0, 0, 0, 0, 0,21,
	 0, 0,21, 0,20,19, 0, 0, 0, 0, 0, 0,21, 0,18,18,
	 0, 0, 0,21, 0, 0, 0, 0, 0,20, 7, 6, 6,13,13, 9,
	 6, 6,12,12, 9, 7, 7,14,14, 0,10,10,12,12, 0,11,
	11,15,15, 9, 7, 7,14,14,12, 9, 9,14,14,10, 7, 7,
	14,13, 0,11,11,16,15, 0,11,11,14,14, 9, 7, 7,14,
	14,13,10,10,14,14,11, 7, 7,14,13, 0,11,11,16,16,
	 0,11,11,14,14, 0,12,12,16,16, 0,19, 0,17,18, 0,
	10,10,14,14, 0,15,14, 0, 0, 0,12,12,14,14, 0,12,
	12,15,15, 0,20, 0,18,19, 0,10,10,14,14, 0,16,15,
	 0,20, 0,13,13,14,14, 0,11,11,13,13, 0,12,13,16,
	16, 0,12,12,16,16, 0,16,16, 0,21, 0,17,18, 0, 0,
	 0,12,12,16,16, 0,15,15,18, 0, 0,12,12,16,16, 0,
	17,16,21,21, 0,16,17, 0, 0, 0,13,13,17,16, 0,16,
	16,20,21, 0,12,12,17,16, 0,17,17, 0,21, 0,17,17,
	21,21, 0,17,18, 0, 0, 0, 0, 0, 0, 0, 0,15,15, 0,
	 0, 0,18,21, 0, 0, 0,18,19, 0, 0, 0,18,17,21,21,
	 0, 0, 0, 0, 0, 0,16,16, 0, 0, 0, 0, 0, 0, 0, 0,
	19,19, 0, 0, 0,11,11,12,12, 0,11,11,10,10, 0,12,
	12,13,13, 0,12,12, 9, 9, 0,14,14,13,13, 0,12,12,
	13,13, 0,14,14,12,13, 0,11,11,12,12, 0,13,13,13,
	13, 0,13,13,13,13, 0,12,12,13,13, 0,14,14,12,12,
	 0,11,11,12,12, 0,14,13,14,14, 0,13,13,13,13, 0,
	15,15,14,15, 0, 0, 0,16,16, 0,12,12,13,13, 0,16,
	17,20,21, 0,14,13,12,12, 0,14,14,14,14, 0,21, 0,
	16,16, 0,12,12,13,13, 0,18,17,21, 0, 0,14,14,13,
	13, 7, 8, 8,17,17,11,10,10,18,18,12,10,10,17,17,
	 0,15,15,20,18, 0,15,15,17,17,11, 9, 9,17,17,14,
	12,12,19,19,13, 9, 9,16,16, 0,15,14, 0,19, 0,14,
	14,16,16,12,10,10,20,18,16,13,13,21,20,14,10,10,
	17,17, 0,15,15,21,20, 0,15,14,17,17, 0,15,15,21,
	21, 0, 0,21, 0, 0, 0,13,13,18,18, 0,19,16, 0, 0,
	 0,15,15,17,16, 0,16,16, 0,21, 0, 0, 0, 0,21, 0,
	13,14,18,17, 0,20,19, 0, 0, 0,15,15,18,18, 8, 7,
	 7,15,15,12,11,11,17,16,13,11,11,16,16, 0, 0, 0,
	21,20, 0, 0, 0, 0,20,11,10,10,17,17,14,13,13,19,
	18,14,11,11,16,16, 0,20, 0,21,19, 0, 0,21, 0,20,
	12,11,11,17,17,16,15,15, 0,19,14,11,11,17,16, 0,
	21, 0, 0,19, 0, 0, 0,21,20, 0, 0,21,20, 0, 0, 0,
	 0, 0, 0, 0, 0, 0,19,21, 0, 0, 0, 0, 0, 0, 0, 0,
	19,20, 0, 0, 0,20,21, 0, 0, 0, 0, 0, 0,20, 0,19,
	21, 0, 0, 0, 0, 0, 0, 0, 0,21,20,11,10, 9,15,15,
	14,11,11,15,15,14,11,11,16,16, 0,14,14,14,14, 0,
	16,15,17,16,13,11,11,16,16,16,13,13,16,16,15,10,
	10,15,15, 0,14,15,17,17, 0,14,14,16,15,13,11,11,
	16,16,17,15,14,16,16,15,10,10,15,15, 0,15,15,17,
	18, 0,15,15,16,16, 0,16,16,17,17, 0,21, 0,21,20,
	 0,13,13,15,15, 0,18,18, 0,21, 0,15,15,15,15, 0,
	16,16,17,17, 0, 0, 0, 0,18, 0,13,13,15,15, 0,19,
	18, 0, 0, 0,15,15,16,16, 0,12,12,15,15, 0,13,13,
	17,17, 0,13,13,17,18, 0,16,17,21, 0, 0,20,18, 0,
	 0, 0,13,13,17,17, 0,15,15, 0,18, 0,12,12,17,18,
	 0,16,16, 0, 0, 0,17,17,21, 0, 0,13,13,18,18, 0,
	16,16,21,21, 0,12,12,17,18, 0,16,17,21, 0, 0,17,
	17, 0,21, 0,17,18, 0, 0, 0, 0, 0, 0, 0, 0,16,15,
	 0,21, 0,21,19, 0, 0, 0,18,18, 0, 0, 0,18,19, 0,
	 0, 0, 0, 0, 0, 0, 0,16,16,21,21, 0,20,19, 0, 0,
	 0,19,21, 0,21, 0,12,12,15,15, 0,12,12,15,16, 0,
	13,13,16,16, 0,14,14,15,15, 0,16,15,17,17, 0,13,
	13,17,17, 0,15,15,16,18, 0,12,12,16,16, 0,14,14,
	17,17, 0,15,14,16,16, 0,13,13,16,16, 0,16,15,17,
	17, 0,12,12,16,16, 0,15,15,18,18, 0,14,14,17,16,
	 0,16,16,17,18, 0, 0, 0,20,21, 0,13,13,16,17, 0,
	17,17, 0, 0, 0,15,15,16,16, 0,15,16,17,17, 0, 0,
	 0,19, 0, 0,13,13,15,16, 0,19,18, 0, 0, 0,16,15,
	16,17, 8, 8, 8,17,17,13,11,10,17,18,13,10,10,17,
	17, 0,15,15,20,19, 0,15,15,17,17,12,10,10,19,18,
	15,12,12,20,18,14,10,10,17,16, 0,15,15,20,20, 0,
	14,15,16,16,13,10,10,17,17,17,14,14, 0,18,15,10,
	10,17,17, 0,16,15,20,20, 0,14,14,17,17, 0,15,16,
	20,20, 0, 0,21, 0, 0, 0,13,13,17,17, 0,18,17, 0,
	 0, 0,15,16,17,18, 0,15,15,18,21, 0, 0, 0,21, 0,
	 0,13,13,18,18, 0,19,19, 0, 0, 0,16,16,18,17, 9,
	 8, 8,15,15,12,11,11,16,16,13,11,11,16,15, 0, 0,
	 0, 0,21, 0,21, 0,19,19,12,11,11,17,18,15,13,13,
	18,19,14,11,11,16,16, 0, 0,21,21,19, 0, 0, 0,21,
	20,13,11,11,18,17,17,14,15,20,21,15,11,12,16,16,
	 0, 0, 0,20, 0, 0, 0,21, 0,19, 0, 0, 0, 0,19, 0,
	 0, 0, 0, 0, 0,21,21,19,19, 0, 0, 0,21, 0, 0, 0,
	 0,19,21, 0, 0, 0,19,20, 0, 0, 0,21, 0, 0, 0,21,
	19,19, 0, 0, 0, 0, 0, 0, 0, 0,21,20, 0,11,11,15,
	15, 0,12,12,15,16, 0,12,12,16,16, 0,15,15,16,15,
	 0,16,16,17,17, 0,12,12,17,17, 0,14,14,17,17, 0,
	11,11,16,16, 0,15,15,19,18, 0,15,15,16,16, 0,12,
	12,17,16, 0,14,15,16,16, 0,11,11,15,15, 0,16,16,
	18,19, 0,15,15,15,16, 0,17,17,18,20, 0,21, 0,21,
	19, 0,14,14,16,16, 0,18,18, 0, 0, 0,16,16,15,15,
	 0,16,16,18,17, 0, 0, 0,19,20, 0,14,14,16,16, 0,
	19,19, 0, 0, 0,16,17,15,15, 0,12,12,14,15, 0,13,
	13,16,17, 0,12,12,17,17, 0,17,16, 0, 0, 0,18,17,
	21, 0, 0,13,13,19,17, 0,15,15,20,21, 0,12,12,17,
	17, 0,17,17, 0, 0, 0,17,17, 0, 0, 0,13,13,17,18,
	 0,16,16,21, 0, 0,12,12,17,17, 0,17,17, 0, 0, 0,
	17,17, 0, 0, 0,18,21, 0, 0, 0, 0, 0, 0, 0, 0,15,
	15,21, 0, 0,20,21, 0, 0, 0,18,19, 0, 0, 0,18,17,
	 0, 0, 0, 0, 0, 0, 0, 0,16,16,21, 0, 0,21,21, 0,
	 0, 0,18,19, 0, 0, 0,12,12,16,16, 0,13,13,16,17,
	 0,13,13,17,16, 0,14,14,16,16, 0,16,15,19,18, 0,
	13,13,17,17, 0,15,15,18,18, 0,12,12,16,16, 0,15,
	15,18,19, 0,15,15,17,16, 0,13,13,17,17, 0,16,16,
	18,17, 0,12,12,17,16, 0,15,15,18,18, 0,15,15,17,
	17, 0,16,16, 0,19, 0, 0, 0, 0, 0, 0,14,14,16,17,
	 0,18,18, 0, 0, 0,15,15,17,17, 0,16,16,21,19, 0,
	21, 0,21,21, 0,13,14,16,16, 0,19,19, 0, 0, 0,15,
	16,16,16, 0,11,11,17,16, 0,15,14,19,18, 0,14,14,
	19,19, 0,18,17,18,20, 0,17,17,18,19, 0,13,13,17,
	17, 0,16,17,21,18, 0,13,13,17,16, 0,18,17,19, 0,
	 0,16,17,18,18, 0,12,12,19,18, 0,18,18,20,20, 0,
	13,13,17,17, 0,17,17,21, 0, 0,16,17,17,18, 0,18,
	17,19,18, 0, 0, 0, 0, 0, 0,14,14,17,17, 0,19,19,
	21, 0, 0,16,16,16,17, 0,17,17,19,20, 0, 0, 0, 0,
	21, 0,15,15,17,18, 0,21,21, 0, 0, 0,17,17,17,18,
	 0,10,10,15,15, 0,15,14,17,18, 0,14,14,16,16, 0,
	 0, 0,18, 0, 0,21, 0,19, 0, 0,13,13,17,16, 0,17,
	17,18, 0, 0,14,14,16,15, 0, 0, 0,21, 0, 0,21, 0,
	19,18, 0,13,13,17,17, 0,18,18,20,20, 0,15,15,16,
	16, 0, 0, 0,21,21, 0, 0, 0,20,20, 0, 0, 0,19, 0,
	 0, 0, 0, 0, 0, 0,21,20,18,18, 0, 0, 0, 0, 0, 0,
	 0, 0, 0,20, 0, 0, 0, 0,20, 0, 0, 0, 0, 0, 0, 0,
	 0,19,18, 0, 0, 0, 0,21, 0, 0, 0,18,20, 0,18,19,
	16,17, 0,21,19,17,17, 0, 0,21,18,18, 0, 0,21,20,
	19, 0, 0, 0,20,20, 0, 0,21,17,17, 0, 0, 0,19,19,
	 0,20,20,17,17, 0, 0, 0, 0,20, 0, 0,20,18,18, 0,
	21,20,17,17, 0, 0, 0,20,21, 0,19, 0,17,17, 0, 0,
	21, 0, 0, 0,20, 0,18,19, 0, 0, 0,21,21, 0, 0, 0,
	 0,21, 0,20,20,17,17, 0, 0, 0, 0, 0, 0,21, 0,18,
	17, 0, 0, 0,20,19, 0, 0, 0, 0,21, 0,20,20,17,17,
	 0, 0, 0, 0, 0, 0,21,21,18,18, 0,12,12,15,14, 0,
	14,14,17,17, 0,14,14,17,16, 0,18,18,21, 0, 0,19,
	20, 0, 0, 0,13,13,18,17, 0,16,16,19,18, 0,13,13,
	17,17, 0,17,17, 0, 0, 0,17,17,21, 0, 0,13,13,17,
	17, 0,17,17,21,20, 0,13,13,18,17, 0,18,19,21,21,
	 0,19,18, 0, 0, 0,18,17, 0, 0, 0, 0, 0, 0, 0, 0,
	15,16, 0, 0, 0,21,21, 0, 0, 0,20,18,21, 0, 0,17,
	18, 0, 0, 0, 0, 0, 0, 0, 0,15,16, 0, 0, 0, 0,20,
	 0, 0, 0, 0,19, 0, 0, 0,15,15,18,19, 0,18,17,21,
	 0, 0,16,18, 0,20, 0,17,18,21, 0, 0,18,20, 0, 0,
	 0,16,16,21,21, 0,19,20,21, 0, 0,16,15, 0,21, 0,
	18,20, 0, 0, 0,18,19, 0, 0, 0,16,15,21,21, 0,21,
	 0, 0, 0, 0,16,15,21, 0, 0,20,19, 0, 0, 0,18,21,
	21, 0, 0,20,18, 0, 0, 0, 0, 0, 0, 0, 0,16,16, 0,
	20, 0,21, 0, 0, 0, 0,17,18,20,21, 0,18,18,21,21,
	 0, 0, 0, 0, 0, 0,16,16,20, 0, 0, 0,21, 0, 0, 0,
	21,18, 0, 0, 0,12,12,20,17, 0,15,15,19,18, 0,14,
	14,19,18, 0,18,17,21,19, 0,17,17,21,17, 0,13,13,
	21,19, 0,16,17,20,19, 0,13,13,16,16, 0,17,17,20,
	21, 0,16,16,19,17, 0,13,13,18,18, 0,17,19,19,19,
	 0,13,13,17,17, 0,18,18, 0,19, 0,16,17,18,18, 0,
	16,17,19,21, 0, 0, 0, 0, 0, 0,15,15,16,17, 0,20,
	19,21, 0, 0,17,17,17,17, 0,17,17,21,19, 0, 0, 0,
	 0, 0, 0,15,15,17,17, 0,21, 0, 0, 0, 0,18,18,17,
	17, 0,10,10,15,15, 0,15,15,17,17, 0,15,14,16,16,
	 0, 0, 0,21,19, 0,21,21,19,21, 0,13,13,17,16, 0,
	17,17,18,19, 0,14,15,16,15, 0, 0, 0,21,19, 0,21,
	21,18,19, 0,14,14,16,17, 0,18,18,18,19, 0,15,15,
	15,16, 0, 0,21, 0,21, 0, 0, 0,19,20, 0, 0, 0,21,
	19, 0, 0, 0, 0, 0, 0,21,21,19,17, 0, 0, 0, 0, 0,
	 0, 0, 0,21,21, 0,21, 0, 0,21, 0, 0, 0, 0, 0, 0,
	21,21,19,18, 0, 0, 0, 0, 0, 0, 0, 0, 0,19, 0,21,
	18,18,17, 0,21, 0,20,20, 0, 0, 0,18,20, 0, 0,21,
	18,21, 0, 0, 0,21,18, 0, 0, 0, 0,19, 0, 0, 0,21,
	21, 0,20,21,17,19, 0,21, 0,21, 0, 0,21, 0,18,18,
	 0,20,21,17,18, 0, 0, 0,21,19, 0,20,21,17,18, 0,
	 0, 0,21,21, 0, 0, 0,20,19, 0, 0, 0,21,21, 0, 0,
	 0, 0, 0, 0,21,21,19,18, 0, 0, 0, 0, 0, 0, 0,21,
	19,18, 0,21,21,19, 0, 0, 0, 0,21, 0, 0,21,21,18,
	17, 0, 0, 0, 0, 0, 0,21, 0,21,18, 0,12,12,14,14,
	 0,15,14,17,17, 0,14,14,17,16, 0,19,17, 0, 0, 0,
	19,19, 0, 0, 0,13,13,17,17, 0,17,17,20,20, 0,13,
	13,18,18, 0,18,17, 0, 0, 0,18,21, 0, 0, 0,13,13,
	17,17, 0,18,18,21,20, 0,14,14,18,19, 0,19,18,21,
	 0, 0,19,19, 0, 0, 0,20,18,20, 0, 0, 0, 0, 0, 0,
	 0,15,16, 0, 0, 0,21,21, 0, 0, 0,19,19, 0, 0, 0,
	18,18, 0, 0, 0, 0, 0, 0, 0, 0,16,16, 0,21, 0, 0,
	 0, 0, 0, 0,19,20, 0, 0, 0,15,15,20,21, 0,17,17,
	21,21, 0,17,17, 0, 0, 0,19,18, 0, 0, 0,18,19, 0,
	 0, 0,17,16, 0,21, 0, 0,20, 0, 0, 0,16,16, 0,20,
	 0,19,19, 0,21, 0,19,18, 0,21, 0,16,16, 0, 0, 0,
	21,21, 0, 0, 0,16,16, 0, 0, 0,21,21, 0, 0, 0,19,
	19, 0, 0, 0,20, 0, 0, 0, 0, 0, 0, 0, 0, 0,17,17,
	 0,21, 0, 0,20, 0, 0, 0,20,18,21,21, 0,19,18, 0,
	20, 0, 0, 0, 0, 0, 0,16,17,21, 0, 0, 0,21, 0, 0,
	 0,19,20,21,20,
};

static const static_codebook _44pn1_p4_0 = {
	5, 3125,
	(char *)_vq_lengthlist__44pn1_p4_0,
	1, -528744448, 1616642048, 3, 0,
	(long *)_vq_quantlist__44pn1_p4_0,
	0
};

static const long _vq_quantlist__44pn1_p4_1[] = {
	3,
	2,
	4,
	1,
	5,
	0,
	6,
};

static const char _vq_lengthlist__44pn1_p4_1[] = {
	 2, 3, 3, 3, 3, 3, 3,
};

static const static_codebook _44pn1_p4_1 = {
	1, 7,
	(char *)_vq_lengthlist__44pn1_p4_1,
	1, -533200896, 1611661312, 3, 0,
	(long *)_vq_quantlist__44pn1_p4_1,
	0
};

static const long _vq_quantlist__44pn1_p5_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p5_0[] = {
	 1, 7, 7, 6, 8, 8, 7, 8, 8, 7, 9, 9,11,11,11, 9,
	 8, 8, 7, 9, 9,11,12,11, 9, 9, 9, 6, 7, 7,10,11,
	11,10,10,10,10,11,11,15,14,14,12,12,12,11,11,11,
	14,14,14,12,12,12, 5, 6, 6, 8, 5, 5, 8, 7, 7, 8,
	 8, 8,12,10,10,10, 7, 7, 8, 7, 7,12,10,10,10, 7,
	 7, 6, 7, 7,12,11,11,12,10,10,11,10,10,14,14,13,
	13,10,10,11,10,10,16,14,14,14,11,10, 7, 7, 7,13,
	12,12,12,12,11,11,11,11,15,14,17,13,12,12,12,11,
	11,15,15,15,14,13,13,10, 9, 9,14,12,11,13,11,11,
	12,11,11,16,15,14,14,11,11,12,11,11,17,14,14,15,
	11,11, 7, 8, 8,12,11,11,13,10,10,11,10,10,17,14,
	13,14,10,10,12,10,10,18,15,15,14,10,10, 8, 7, 7,
	13,12,12,13,11,11,12,11,11,16,14,15,14,12,12,12,
	11,11,18,16,16,14,12,12,11,10,10,13,12,11,13,11,
	11,13,12,12, 0,15,14,14,11,11,13,11,11,16,15,15,
	15,11,11,
};

static const static_codebook _44pn1_p5_0 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p5_0,
	1, -527106048, 1620377600, 2, 0,
	(long *)_vq_quantlist__44pn1_p5_0,
	0
};

static const long _vq_quantlist__44pn1_p5_1[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p5_1[] = {
	 2, 6, 7, 6, 8, 8, 7, 7, 8, 7, 8, 8, 9, 9, 9, 8,
	 7, 7, 8, 8, 8, 9, 9, 9, 9, 8, 8, 6, 6, 6, 9, 7,
	 7, 9, 7, 7, 9, 8, 8,10, 8, 8,10, 8, 8,10, 8, 8,
	10, 9, 8,10, 8, 8, 7, 6, 6, 9, 6, 6, 9, 6, 6, 9,
	 7, 7,10, 8, 8,10, 6, 6, 9, 7, 7,10, 8, 8,10, 6,
	 6, 7, 7, 7,11, 9, 9,11, 9, 9,10, 9, 9,12,10,10,
	12, 8, 8,11, 9, 9,13, 9,10,12, 8, 8, 8, 7, 7,11,
	 9,10,11,10,10,10, 9, 9,11,11,11,11, 9, 9,11,10,
	 9,12,11,11,11, 9,10,10, 8, 8,11, 9,10,11, 9, 9,
	11, 9, 9,12,10,10,11, 9, 9,11, 9, 9,12,10,11,11,
	 9, 9, 8, 8, 8,12, 9, 9,12, 9, 9,11, 9, 9,13, 9,
	 9,13, 8, 8,12, 9, 9,13,10,10,12, 8, 8, 9, 7, 7,
	11,10,10,11,10,10,11,10,10,12,11,11,11,10, 9,11,
	10,10,11,11,11,11, 9, 9,11, 9, 9,12,10,10,11,10,
	10,12,10,10,11,11,11,11, 9, 9,11,10,10,12,11,11,
	11, 9, 9,
};

static const static_codebook _44pn1_p5_1 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p5_1,
	1, -530841600, 1616642048, 2, 0,
	(long *)_vq_quantlist__44pn1_p5_1,
	0
};

static const long _vq_quantlist__44pn1_p6_0[] = {
	1,
	0,
	2,
};

static const char _vq_lengthlist__44pn1_p6_0[] = {
	 1, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 8, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9,
	 9, 9, 9,
};

static const static_codebook _44pn1_p6_0 = {
	5, 243,
	(char *)_vq_lengthlist__44pn1_p6_0,
	1, -516716544, 1630767104, 2, 0,
	(long *)_vq_quantlist__44pn1_p6_0,
	0
};

static const long _vq_quantlist__44pn1_p6_1[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44pn1_p6_1[] = {
	 1, 3, 2, 5, 4, 7, 7, 8, 8, 9, 9,10,10,11,11,12,
	12,13,13,14,14,15,15,15,15,
};

static const static_codebook _44pn1_p6_1 = {
	1, 25,
	(char *)_vq_lengthlist__44pn1_p6_1,
	1, -518864896, 1620639744, 5, 0,
	(long *)_vq_quantlist__44pn1_p6_1,
	0
};

static const long _vq_quantlist__44pn1_p6_2[] = {
	12,
	11,
	13,
	10,
	14,
	9,
	15,
	8,
	16,
	7,
	17,
	6,
	18,
	5,
	19,
	4,
	20,
	3,
	21,
	2,
	22,
	1,
	23,
	0,
	24,
};

static const char _vq_lengthlist__44pn1_p6_2[] = {
	 3, 5, 4, 5, 4, 5, 4, 5, 5, 5, 4, 5, 5, 5, 5, 5,
	 5, 5, 5, 5, 5, 5, 5, 5, 5,
};

static const static_codebook _44pn1_p6_2 = {
	1, 25,
	(char *)_vq_lengthlist__44pn1_p6_2,
	1, -529006592, 1611661312, 5, 0,
	(long *)_vq_quantlist__44pn1_p6_2,
	0
};

static const char _huff_lengthlist__44pn1_short[] = {
	 4, 3, 7, 9,12,16,16, 3, 2, 5, 7,11,14,15, 7, 4,
	 5, 6, 9,12,15, 8, 5, 5, 5, 8,10,14, 9, 7, 6, 6,
	 8,10,12,12,10,10, 7, 6, 8,10,15,12,10, 6, 4, 7,
	 9,
};

static const static_codebook _huff_book__44pn1_short = {
	2, 49,
	(char *)_huff_lengthlist__44pn1_short,
	0, 0, 0, 0, 0,
	NULL,
	0
};

