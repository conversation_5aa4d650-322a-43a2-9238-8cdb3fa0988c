This is FNA3D, the 3D graphics library for FNA.

Project Website: https://fna-xna.github.io/

License
-------
FNA3D is released under the zlib license. See LICENSE for details.

About FNA3D
-----------
FNA3D was written to be used for FNA's Graphics namespace. It carries the same
API design as XNA 4.0, with various implementations available at runtime
(including Vulkan, OpenGL, Metal via MoltenVK, and Direct3D). For shaders,
we support Direct3D 9 Effect Framework binaries via MojoShader.

Note that while FNA is the main consumer of FNA3D, we do NOT provide separate C#
bindings. If you want to use FNA3D in C#, simply use FNA instead; it's the same
API and can be used entirely by itself without dragging in other dependencies.

Dependencies
------------
FNA3D depends solely on SDL 2.26.0 or newer.
FNA3D never explicitly uses the C runtime.

Building FNA3D
---------------
For *nix platforms, use CMake:

    $ mkdir build/
    $ cd build/
    $ cmake ../
    $ make

For Windows, see the 'visualc/' directory.

For Xbox GDK, see the 'visualc-gdk/' directory.

For iOS/tvOS and macOS universal binaries, see the 'Xcode/' directory.

Found an issue?
---------------
Issues and patches can be reported via GitHub:

https://github.com/FNA-XNA/FNA3D/issues
