name: Deploy

on:
  workflow_dispatch:
  # workflow_run:
  #   branches: [main]
  #   workflows: [Build-Test]
  #   types:
  #     - completed

env:
  CUO_ASSEMBLY_VERSION: "1.1.0.${{ github.run_number }}"
  CUO_OUTPUT_PATH: "../../bin/dist"
  CUO_PROJECT_PATH: "src/ClassicUO.Client/ClassicUO.Client.csproj"
  BOOT_PROJECT_PATH: "src/ClassicUO.Bootstrap/src/ClassicUO.Bootstrap.csproj"
  TARGET_FRAMEWORK: net9.0

  DOTNET_NOLOGO: false
  DOTNET_CLI_TELEMETRY_OPTOUT: 1
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  NUGET_XMLDOC_MODE: skip

jobs:
  build:
    #if: ${{ github.event.workflow_run.conclusion == 'success' }} && "${{ env.GITHUB_REPOSITORY }}" == 'ClassicUO/ClassicUO'
    runs-on: ${{ matrix.os }}

    env:
      CUO_ZIP_NAME: "ClassicUO-${{ matrix.target }}-release.zip"

    strategy:
      matrix:
        include:
          - os: windows-latest
            target: win-x64
          - os: ubuntu-22.04
            target: linux-x64
          - os: macos-13
            target: osx-x64

    steps:
      - uses: actions/checkout@v4

      - name: Get submodules
        run: |
          git config --global url."https://".insteadOf git://
          git submodule update --init --recursive

      - name: Setup dotnet
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 9.0.x
          include-prerelease: false

      - name: Build Bootstrap app
        run: dotnet publish ${{ env.BOOT_PROJECT_PATH }} -c Release -o ${{ env.CUO_OUTPUT_PATH }} -p:AssemblyVersion=${{ env.CUO_ASSEMBLY_VERSION }} -p:FileVersion=${{ env.CUO_ASSEMBLY_VERSION }} -p:OutputType=WinExe

      - name: Build ClassicUO
        run: dotnet publish ${{ env.CUO_PROJECT_PATH }} -c Release -r ${{ matrix.target }} -o ${{ env.CUO_OUTPUT_PATH }} -p:IS_DEV_BUILD=true -p:AssemblyVersion=${{ env.CUO_ASSEMBLY_VERSION }} -p:FileVersion=${{ env.CUO_ASSEMBLY_VERSION }} -p:NativeLib=Shared -p:OutputType=Library

      - name: Create manifest
        run: |
          dotnet run --project tools/ManifestCreator/ManifestCreator.csproj "${{ env.CUO_OUTPUT_PATH }}" "dev-preview" "${{ env.CUO_ZIP_NAME }}"
          mkdir upload
          mv manifest.xml upload

      - name: Create package
        uses: thedoctor0/zip-release@master
        with:
          type: "zip"
          directory: ${{ env.CUO_OUTPUT_PATH }}
          filename: ${{ env.CUO_ZIP_NAME }}
          exclusions: "*.zip manifest.xml"

      - name: Move output
        run: mv "${{ env.CUO_OUTPUT_PATH }}/${{ env.CUO_ZIP_NAME }}" upload

      - name: Upload Release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "upload/${{ env.CUO_ZIP_NAME }}"
          name: "ClassicUO dev preview"
          body: "This build may not be safe: use it at your own risk."
          draft: false
          prerelease: true
          allowUpdates: true
          tag: "ClassicUO-dev-release"
          token: ${{ secrets.GITHUB_TOKEN }}

  #   - name: Repo Preparation - clone
  #     uses: actions/checkout@master
  #     with:
  #       repository: ClassicUO/deploy
  #       path: deploy
  #       ssh-key: ${{ secrets.SSH_DEPLOY_KEY }}
  #
  #   - name: Repo Preparation - prepare
  #     run: |
  #       cp -rdf ${{ env.CUO_OUTPUT_PATH }}/* deploy/client/${{ matrix.target }}/
  #       cp upload/manifest.xml deploy/client/${{ matrix.target }}_manifest.xml
  #
  #   - name: Pushes to another repository
  #     uses: cpina/github-action-push-to-another-repository@main
  #     env:
  #       SSH_DEPLOY_KEY: ${{ secrets.SSH_DEPLOY_KEY }}
  #     with:
  #       source-directory: deploy/
  #       destination-github-username: "ClassicUO"
  #       destination-repository-name: "deploy"
  #       commit-message: "release ${{ env.CUO_ASSEMBLY_VERSION }} on ${{ matrix.target }}"
  #       target-branch: main

