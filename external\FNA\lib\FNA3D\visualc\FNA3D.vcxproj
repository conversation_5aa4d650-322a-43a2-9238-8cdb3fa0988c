﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6DB15344-E000-45CB-A48A-1D72F7D6E945}</ProjectGuid>
    <RootNamespace>FNA3D</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <IncludePath>..\..\SDL2\include;..\MojoShader;..\include;$(DXSDK_DIR)\Include;..\Vulkan-Headers\include;$(IncludePath)</IncludePath>
    <LibraryPath>..\..\SDL2\lib\$(PlatformShortName);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>FNA3D_DRIVER_OPENGL;FNA3D_DRIVER_D3D11;FNA3D_DRIVER_VULKAN;MOJOSHADER_NO_VERSION_INCLUDE;MOJOSHADER_USE_SDL_STDLIB;MOJOSHADER_EFFECT_SUPPORT;MOJOSHADER_DEPTH_CLIPPING;MOJOSHADER_FLIP_RENDERTARGET;MOJOSHADER_XNA4_VERTEX_TEXTURES;SUPPORT_PROFILE_ARB1=0;SUPPORT_PROFILE_ARB1_NV=0;SUPPORT_PROFILE_BYTECODE=0;SUPPORT_PROFILE_D3D=0;SUPPORT_PROFILE_METAL=0;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>SDL2.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>FNA3D_DRIVER_OPENGL;FNA3D_DRIVER_D3D11;FNA3D_DRIVER_VULKAN;MOJOSHADER_NO_VERSION_INCLUDE;MOJOSHADER_USE_SDL_STDLIB;MOJOSHADER_EFFECT_SUPPORT;MOJOSHADER_DEPTH_CLIPPING;MOJOSHADER_FLIP_RENDERTARGET;MOJOSHADER_XNA4_VERTEX_TEXTURES;SUPPORT_PROFILE_ARB1=0;SUPPORT_PROFILE_ARB1_NV=0;SUPPORT_PROFILE_BYTECODE=0;SUPPORT_PROFILE_D3D=0;SUPPORT_PROFILE_METAL=0;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>SDL2.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\MojoShader\mojoshader.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_common.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_d3d11.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_effects.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_metal.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_opengl.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\mojoshader_vulkan.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\profiles\mojoshader_profile_common.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\profiles\mojoshader_profile_glsl.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\profiles\mojoshader_profile_hlsl.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\profiles\mojoshader_profile_metal.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\MojoShader\profiles\mojoshader_profile_spirv.c">
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <ClCompile Include="..\src\FNA3D.c" />
    <ClCompile Include="..\src\FNA3D_CommandBuffer.c" />
    <ClCompile Include="..\src\FNA3D_Driver_D3D11.c" />
    <ClCompile Include="..\src\FNA3D_Driver_OpenGL.c" />
    <ClCompile Include="..\src\FNA3D_Driver_Vulkan.c" />
    <ClCompile Include="..\src\FNA3D_Image.c" />
    <ClCompile Include="..\src\FNA3D_Memory.c" />
    <ClCompile Include="..\src\FNA3D_PipelineCache.c" />
    <ClCompile Include="..\src\FNA3D_Tracing.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\FNA3D.h" />
    <ClInclude Include="..\include\FNA3D_Image.h" />
    <ClInclude Include="..\src\FNA3D_CommandBuffer.h" />
    <ClInclude Include="..\src\FNA3D_Driver.h" />
    <ClInclude Include="..\src\FNA3D_Driver_D3D11.h" />
    <ClInclude Include="..\src\FNA3D_Driver_OpenGL.h" />
    <ClInclude Include="..\src\FNA3D_Driver_OpenGL_glfuncs.h" />
    <ClInclude Include="..\src\FNA3D_Driver_Vulkan_vkfuncs.h" />
    <ClInclude Include="..\src\FNA3D_Memory.h" />
    <ClInclude Include="..\src\FNA3D_PipelineCache.h" />
    <ClInclude Include="..\src\FNA3D_Tracing.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>