CMake - Cross Platform Makefile Generator
Copyright 2000-2018 Kitware, Inc. and Contributors
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

* Redistributions of source code must retain the above copyright
  notice, this list of conditions and the following disclaimer.

* Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.

* Neither the name of Kitware, Inc. nor the names of Contributors
  may be used to endorse or promote products derived from this
  software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEM<PERSON>ARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

------------------------------------------------------------------------------

The following individuals and institutions are among the Contributors:

* Aaron C. Meadows <<EMAIL>>
* Adriaan de Groot <<EMAIL>>
* Aleksey Avdeev <<EMAIL>>
* Alexander Neundorf <<EMAIL>>
* Alexander Smorkalov <<EMAIL>>
* Alexey Sokolov <<EMAIL>>
* Alex Turbov <<EMAIL>>
* Andreas Pakulat <<EMAIL>>
* Andreas Schneider <<EMAIL>>
* André Rigland Brodtkorb <<EMAIL>>
* Axel Huebl, Helmholtz-Zentrum Dresden - Rossendorf
* Benjamin Eikel
* Bjoern Ricks <<EMAIL>>
* Brad Hards <<EMAIL>>
* Christopher Harvey
* Christoph Grüninger <<EMAIL>>
* Clement Creusot <<EMAIL>>
* Daniel Blezek <<EMAIL>>
* Daniel Pfeifer <<EMAIL>>
* Enrico Scholz <<EMAIL>>
* Eran Ifrah <<EMAIL>>
* Esben Mose Hansen, Ange Optimization ApS
* Geoffrey Viola <<EMAIL>>
* Google Inc
* Gregor Jasny
* Helio Chissini de Castro <<EMAIL>>
* Ilya Lavrenov <<EMAIL>>
* Insight Software Consortium <insightsoftwareconsortium.org>
* Jan Woetzel
* Kelly Thompson <<EMAIL>>
* Konstantin Podsvirov <<EMAIL>>
* Mario Bensi <<EMAIL>>
* Mathieu Malaterre <<EMAIL>>
* Matthaeus G. Chajdas
* Matthias Kretz <<EMAIL>>
* Matthias Maennich <<EMAIL>>
* Michael Stürmer
* Miguel A. Figueroa-Villanueva
* Mike Jackson
* Mike McQuaid <<EMAIL>>
* Nicolas Bock <<EMAIL>>
* Nicolas Despres <<EMAIL>>
* Nikita Krupen'ko <<EMAIL>>
* NVIDIA Corporation <www.nvidia.com>
* OpenGamma Ltd. <opengamma.com>
* Patrick Stotko <<EMAIL>>
* Per Øyvind Karlsen <<EMAIL>>
* Peter Collingbourne <<EMAIL>>
* Petr Gotthard <<EMAIL>>
* Philip Lowman <<EMAIL>>
* Philippe Proulx <<EMAIL>>
* Raffi Enficiaud, Max Planck Society
* Raumfeld <raumfeld.com>
* Roger Leigh <<EMAIL>>
* Rolf Eike Beer <<EMAIL>>
* Roman Donchenko <<EMAIL>>
* Roman Kharitonov <<EMAIL>>
* Ruslan Baratov
* Sebastian Holtermann <<EMAIL>>
* Stephen Kelly <<EMAIL>>
* Sylvain Joubert <<EMAIL>>
* Thomas Sondergaard <<EMAIL>>
* Tobias Hunger <<EMAIL>>
* Todd Gamblin <<EMAIL>>
* Tristan Carel
* University of Dundee
* Vadim Zhukov
* Will Dicharry <<EMAIL>>

See version control history for details of individual contributions.

The above copyright and license notice applies to distributions of
CMake in source and binary form.  Third-party software packages supplied
with CMake under compatible licenses provide their own copyright notices
documented in corresponding subdirectories or source files.

------------------------------------------------------------------------------

CMake was initially developed by Kitware with the following sponsorship:

 * National Library of Medicine at the National Institutes of Health
   as part of the Insight Segmentation and Registration Toolkit (ITK).

 * US National Labs (Los Alamos, Livermore, Sandia) ASC Parallel
   Visualization Initiative.

 * National Alliance for Medical Image Computing (NAMIC) is funded by the
   National Institutes of Health through the NIH Roadmap for Medical Research,
   Grant U54 EB005149.

 * Kitware, Inc.
