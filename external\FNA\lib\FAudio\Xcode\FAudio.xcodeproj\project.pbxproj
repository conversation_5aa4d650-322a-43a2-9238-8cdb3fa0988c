// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		7B6908272190EC41003C0941 /* XNA_Song.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B6908262190EC41003C0941 /* XNA_Song.c */; };
		7B6908282190EC41003C0941 /* XNA_Song.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B6908262190EC41003C0941 /* XNA_Song.c */; };
		7B7E14162190E10C00616654 /* F3DAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D612190C8E50020B14B /* F3DAudio.c */; };
		7B7E14172190E10C00616654 /* FACT_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D602190C8E50020B14B /* FACT_internal.c */; };
		7B7E14182190E10C00616654 /* FACT.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6A2190C8E50020B14B /* FACT.c */; };
		7B7E14192190E10C00616654 /* FACT3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D652190C8E50020B14B /* FACT3D.c */; };
		7B7E141A2190E10C00616654 /* FAPOBase.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D642190C8E50020B14B /* FAPOBase.c */; };
		7B7E141B2190E10C00616654 /* FAPOFX_echo.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D682190C8E50020B14B /* FAPOFX_echo.c */; };
		7B7E141C2190E10C00616654 /* FAPOFX_eq.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6B2190C8E50020B14B /* FAPOFX_eq.c */; };
		7B7E141D2190E10C00616654 /* FAPOFX_masteringlimiter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D632190C8E50020B14B /* FAPOFX_masteringlimiter.c */; };
		7B7E141E2190E10C00616654 /* FAPOFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6D2190C8E50020B14B /* FAPOFX_reverb.c */; };
		7B7E141F2190E10C00616654 /* FAPOFX.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D672190C8E50020B14B /* FAPOFX.c */; };
		7B7E14202190E10C00616654 /* FAudio_internal_simd.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D662190C8E50020B14B /* FAudio_internal_simd.c */; };
		7B7E14212190E10C00616654 /* FAudio_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D622190C8E50020B14B /* FAudio_internal.c */; };
		7B7E14222190E10C00616654 /* FAudio_platform_sdl2.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6C2190C8E50020B14B /* FAudio_platform_sdl2.c */; };
		7B7E14232190E10C00616654 /* FAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D692190C8E50020B14B /* FAudio.c */; };
		7B7E14242190E10C00616654 /* FAudioFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6E2190C8E50020B14B /* FAudioFX_reverb.c */; };
		7B7E14252190E10C00616654 /* FAudioFX_volumemeter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D5F2190C8E50020B14B /* FAudioFX_volumemeter.c */; };
		7B80D133227CE0E000AE825D /* FAudio_operationset.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B80D132227CE0E000AE825D /* FAudio_operationset.c */; };
		7BD20D6F2190C8E50020B14B /* FAudioFX_volumemeter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D5F2190C8E50020B14B /* FAudioFX_volumemeter.c */; };
		7BD20D712190C8E50020B14B /* FACT_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D602190C8E50020B14B /* FACT_internal.c */; };
		7BD20D732190C8E50020B14B /* F3DAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D612190C8E50020B14B /* F3DAudio.c */; };
		7BD20D752190C8E50020B14B /* FAudio_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D622190C8E50020B14B /* FAudio_internal.c */; };
		7BD20D772190C8E50020B14B /* FAPOFX_masteringlimiter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D632190C8E50020B14B /* FAPOFX_masteringlimiter.c */; };
		7BD20D792190C8E50020B14B /* FAPOBase.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D642190C8E50020B14B /* FAPOBase.c */; };
		7BD20D7B2190C8E50020B14B /* FACT3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D652190C8E50020B14B /* FACT3D.c */; };
		7BD20D7D2190C8E50020B14B /* FAudio_internal_simd.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D662190C8E50020B14B /* FAudio_internal_simd.c */; };
		7BD20D7F2190C8E50020B14B /* FAPOFX.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D672190C8E50020B14B /* FAPOFX.c */; };
		7BD20D812190C8E50020B14B /* FAPOFX_echo.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D682190C8E50020B14B /* FAPOFX_echo.c */; };
		7BD20D832190C8E50020B14B /* FAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D692190C8E50020B14B /* FAudio.c */; };
		7BD20D852190C8E50020B14B /* FACT.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6A2190C8E50020B14B /* FACT.c */; };
		7BD20D872190C8E50020B14B /* FAPOFX_eq.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6B2190C8E50020B14B /* FAPOFX_eq.c */; };
		7BD20D892190C8E50020B14B /* FAudio_platform_sdl2.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6C2190C8E50020B14B /* FAudio_platform_sdl2.c */; };
		7BD20D8B2190C8E50020B14B /* FAPOFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6D2190C8E50020B14B /* FAPOFX_reverb.c */; };
		7BD20D8D2190C8E50020B14B /* FAudioFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6E2190C8E50020B14B /* FAudioFX_reverb.c */; };
		7BD31FBE2B434349003EEE59 /* FAPOBase.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D642190C8E50020B14B /* FAPOBase.c */; };
		7BD31FBF2B434349003EEE59 /* FAudio_internal_simd.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D662190C8E50020B14B /* FAudio_internal_simd.c */; };
		7BD31FC02B434349003EEE59 /* FAudio_operationset.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B80D132227CE0E000AE825D /* FAudio_operationset.c */; };
		7BD31FC12B434349003EEE59 /* FAPOFX_masteringlimiter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D632190C8E50020B14B /* FAPOFX_masteringlimiter.c */; };
		7BD31FC22B434349003EEE59 /* FACT.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6A2190C8E50020B14B /* FACT.c */; };
		7BD31FC32B434349003EEE59 /* FAudioFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6E2190C8E50020B14B /* FAudioFX_reverb.c */; };
		7BD31FC42B434349003EEE59 /* FAudio_platform_sdl2.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6C2190C8E50020B14B /* FAudio_platform_sdl2.c */; };
		7BD31FC52B434349003EEE59 /* FAPOFX.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D672190C8E50020B14B /* FAPOFX.c */; };
		7BD31FC62B434349003EEE59 /* FAPOFX_reverb.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6D2190C8E50020B14B /* FAPOFX_reverb.c */; };
		7BD31FC72B434349003EEE59 /* FAPOFX_eq.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D6B2190C8E50020B14B /* FAPOFX_eq.c */; };
		7BD31FC82B434349003EEE59 /* FACT_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D602190C8E50020B14B /* FACT_internal.c */; };
		7BD31FC92B434349003EEE59 /* XNA_Song.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B6908262190EC41003C0941 /* XNA_Song.c */; };
		7BD31FCA2B434349003EEE59 /* FAudio_internal.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D622190C8E50020B14B /* FAudio_internal.c */; };
		7BD31FCB2B434349003EEE59 /* FAPOFX_echo.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D682190C8E50020B14B /* FAPOFX_echo.c */; };
		7BD31FCC2B434349003EEE59 /* FACT3D.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D652190C8E50020B14B /* FACT3D.c */; };
		7BD31FCD2B434349003EEE59 /* FAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D692190C8E50020B14B /* FAudio.c */; };
		7BD31FCE2B434349003EEE59 /* F3DAudio.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D612190C8E50020B14B /* F3DAudio.c */; };
		7BD31FCF2B434349003EEE59 /* FAudioFX_volumemeter.c in Sources */ = {isa = PBXBuildFile; fileRef = 7BD20D5F2190C8E50020B14B /* FAudioFX_volumemeter.c */; };
		7BD31FF52B4344D0003EEE59 /* libSDL2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 7BD31FED2B4344C1003EEE59 /* libSDL2.dylib */; };
		7BD806B122A0B4F900D9679D /* FAudio_operationset.c in Sources */ = {isa = PBXBuildFile; fileRef = 7B80D132227CE0E000AE825D /* FAudio_operationset.c */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7BD31FDE2B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF66C0761BA81005FE872;
			remoteInfo = Framework;
		};
		7BD31FE02B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88B5423E2437C00DCD162;
			remoteInfo = "Framework-iOS";
		};
		7BD31FE22B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88D1523E24BED00DCD162;
			remoteInfo = "Framework-tvOS";
		};
		7BD31FE42B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = E2D187CF28A5673500D2B4F1;
			remoteInfo = "xcFramework-iOS";
		};
		7BD31FE62B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6B30761BA81005FE872;
			remoteInfo = "Static Library";
		};
		7BD31FE82B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A7D88E5423E24D3B00DCD162;
			remoteInfo = "Static Library-iOS";
		};
		7BD31FEA2B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A769B23D23E259AE00872273;
			remoteInfo = "Static Library-tvOS";
		};
		7BD31FEC2B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = DB31407717554B71006C0E22;
			remoteInfo = "Shared Library";
		};
		7BD31FEE2B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A75FCEB323E25AB700529352;
			remoteInfo = "Shared Library-iOS";
		};
		7BD31FF02B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = A75FD06C23E25AC700529352;
			remoteInfo = "Shared Library-tvOS";
		};
		7BD31FF22B4344C1003EEE59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = BECDF6BE0761BA81005FE872;
			remoteInfo = "Standard DMG";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7B1CDD432190C0A200175C7B /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E140B2190E0CB00616654 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		7B1CDD452190C0A200175C7B /* libFAudio.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libFAudio.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7B5EE3122B43428D004061E2 /* libFAudio.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libFAudio.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		7B6908262190EC41003C0941 /* XNA_Song.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = XNA_Song.c; path = ../src/XNA_Song.c; sourceTree = "<group>"; };
		7B7E140D2190E0CB00616654 /* libFAudio.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libFAudio.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7B80D132227CE0E000AE825D /* FAudio_operationset.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudio_operationset.c; path = ../src/FAudio_operationset.c; sourceTree = "<group>"; };
		7BA5611F21B9C7D800AB0E8C /* F3DAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = F3DAudio.h; path = ../include/F3DAudio.h; sourceTree = "<group>"; };
		7BA5612021B9C7D800AB0E8C /* FAPO.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAPO.h; path = ../include/FAPO.h; sourceTree = "<group>"; };
		7BA5612121B9C7D800AB0E8C /* FAudioFX.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAudioFX.h; path = ../include/FAudioFX.h; sourceTree = "<group>"; };
		7BA5612221B9C7D800AB0E8C /* FACT.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FACT.h; path = ../include/FACT.h; sourceTree = "<group>"; };
		7BA5612321B9C7D800AB0E8C /* FAPOFX.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAPOFX.h; path = ../include/FAPOFX.h; sourceTree = "<group>"; };
		7BA5612421B9C7D800AB0E8C /* FACT3D.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FACT3D.h; path = ../include/FACT3D.h; sourceTree = "<group>"; };
		7BA5612521B9C7D900AB0E8C /* FAPOBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAPOBase.h; path = ../include/FAPOBase.h; sourceTree = "<group>"; };
		7BA5612621B9C7D900AB0E8C /* FAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAudio.h; path = ../include/FAudio.h; sourceTree = "<group>"; };
		7BD20D582190C8C30020B14B /* FACT_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FACT_internal.h; path = ../src/FACT_internal.h; sourceTree = "<group>"; };
		7BD20D5B2190C8C30020B14B /* FAudio_internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = FAudio_internal.h; path = ../src/FAudio_internal.h; sourceTree = "<group>"; };
		7BD20D5F2190C8E50020B14B /* FAudioFX_volumemeter.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudioFX_volumemeter.c; path = ../src/FAudioFX_volumemeter.c; sourceTree = "<group>"; };
		7BD20D602190C8E50020B14B /* FACT_internal.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FACT_internal.c; path = ../src/FACT_internal.c; sourceTree = "<group>"; };
		7BD20D612190C8E50020B14B /* F3DAudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = F3DAudio.c; path = ../src/F3DAudio.c; sourceTree = "<group>"; };
		7BD20D622190C8E50020B14B /* FAudio_internal.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudio_internal.c; path = ../src/FAudio_internal.c; sourceTree = "<group>"; };
		7BD20D632190C8E50020B14B /* FAPOFX_masteringlimiter.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOFX_masteringlimiter.c; path = ../src/FAPOFX_masteringlimiter.c; sourceTree = "<group>"; };
		7BD20D642190C8E50020B14B /* FAPOBase.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOBase.c; path = ../src/FAPOBase.c; sourceTree = "<group>"; };
		7BD20D652190C8E50020B14B /* FACT3D.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FACT3D.c; path = ../src/FACT3D.c; sourceTree = "<group>"; };
		7BD20D662190C8E50020B14B /* FAudio_internal_simd.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudio_internal_simd.c; path = ../src/FAudio_internal_simd.c; sourceTree = "<group>"; };
		7BD20D672190C8E50020B14B /* FAPOFX.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOFX.c; path = ../src/FAPOFX.c; sourceTree = "<group>"; };
		7BD20D682190C8E50020B14B /* FAPOFX_echo.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOFX_echo.c; path = ../src/FAPOFX_echo.c; sourceTree = "<group>"; };
		7BD20D692190C8E50020B14B /* FAudio.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudio.c; path = ../src/FAudio.c; sourceTree = "<group>"; };
		7BD20D6A2190C8E50020B14B /* FACT.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FACT.c; path = ../src/FACT.c; sourceTree = "<group>"; };
		7BD20D6B2190C8E50020B14B /* FAPOFX_eq.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOFX_eq.c; path = ../src/FAPOFX_eq.c; sourceTree = "<group>"; };
		7BD20D6C2190C8E50020B14B /* FAudio_platform_sdl2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudio_platform_sdl2.c; path = ../src/FAudio_platform_sdl2.c; sourceTree = "<group>"; };
		7BD20D6D2190C8E50020B14B /* FAPOFX_reverb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAPOFX_reverb.c; path = ../src/FAPOFX_reverb.c; sourceTree = "<group>"; };
		7BD20D6E2190C8E50020B14B /* FAudioFX_reverb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = FAudioFX_reverb.c; path = ../src/FAudioFX_reverb.c; sourceTree = "<group>"; };
		7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SDL.xcodeproj; path = ../../SDL2/Xcode/SDL/SDL.xcodeproj; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7B1CDD422190C0A200175C7B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B5EE3102B43428D004061E2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BD31FF52B4344D0003EEE59 /* libSDL2.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E140A2190E0CB00616654 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7B1CDD3C2190C0A200175C7B = {
			isa = PBXGroup;
			children = (
				7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */,
				7B1CDDA72190C52900175C7B /* Public Headers */,
				7B1CDDA62190C50300175C7B /* Library Source */,
				7B1CDD462190C0A200175C7B /* Products */,
				7BD31FF42B4344D0003EEE59 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7B1CDD462190C0A200175C7B /* Products */ = {
			isa = PBXGroup;
			children = (
				7B1CDD452190C0A200175C7B /* libFAudio.a */,
				7B7E140D2190E0CB00616654 /* libFAudio.a */,
				7B5EE3122B43428D004061E2 /* libFAudio.dylib */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7B1CDDA62190C50300175C7B /* Library Source */ = {
			isa = PBXGroup;
			children = (
				7BD20D612190C8E50020B14B /* F3DAudio.c */,
				7BD20D602190C8E50020B14B /* FACT_internal.c */,
				7BD20D6A2190C8E50020B14B /* FACT.c */,
				7BD20D652190C8E50020B14B /* FACT3D.c */,
				7BD20D642190C8E50020B14B /* FAPOBase.c */,
				7BD20D682190C8E50020B14B /* FAPOFX_echo.c */,
				7BD20D6B2190C8E50020B14B /* FAPOFX_eq.c */,
				7BD20D632190C8E50020B14B /* FAPOFX_masteringlimiter.c */,
				7BD20D6D2190C8E50020B14B /* FAPOFX_reverb.c */,
				7BD20D672190C8E50020B14B /* FAPOFX.c */,
				7BD20D662190C8E50020B14B /* FAudio_internal_simd.c */,
				7B80D132227CE0E000AE825D /* FAudio_operationset.c */,
				7BD20D622190C8E50020B14B /* FAudio_internal.c */,
				7BD20D6C2190C8E50020B14B /* FAudio_platform_sdl2.c */,
				7BD20D692190C8E50020B14B /* FAudio.c */,
				7BD20D6E2190C8E50020B14B /* FAudioFX_reverb.c */,
				7BD20D5F2190C8E50020B14B /* FAudioFX_volumemeter.c */,
				7B6908262190EC41003C0941 /* XNA_Song.c */,
			);
			name = "Library Source";
			sourceTree = "<group>";
		};
		7B1CDDA72190C52900175C7B /* Public Headers */ = {
			isa = PBXGroup;
			children = (
				7BA5611F21B9C7D800AB0E8C /* F3DAudio.h */,
				7BA5612221B9C7D800AB0E8C /* FACT.h */,
				7BA5612421B9C7D800AB0E8C /* FACT3D.h */,
				7BA5612021B9C7D800AB0E8C /* FAPO.h */,
				7BA5612521B9C7D900AB0E8C /* FAPOBase.h */,
				7BA5612321B9C7D800AB0E8C /* FAPOFX.h */,
				7BA5612621B9C7D900AB0E8C /* FAudio.h */,
				7BA5612121B9C7D800AB0E8C /* FAudioFX.h */,
				7BD20D582190C8C30020B14B /* FACT_internal.h */,
				7BD20D5B2190C8C30020B14B /* FAudio_internal.h */,
			);
			name = "Public Headers";
			sourceTree = "<group>";
		};
		7BD31FD12B4344C0003EEE59 /* Products */ = {
			isa = PBXGroup;
			children = (
				7BD31FDF2B4344C1003EEE59 /* SDL2.framework */,
				7BD31FE12B4344C1003EEE59 /* SDL2.framework */,
				7BD31FE32B4344C1003EEE59 /* SDL2.framework */,
				7BD31FE52B4344C1003EEE59 /* SDL2.framework */,
				7BD31FE72B4344C1003EEE59 /* libSDL2.a */,
				7BD31FE92B4344C1003EEE59 /* libSDL2.a */,
				7BD31FEB2B4344C1003EEE59 /* libSDL2.a */,
				7BD31FED2B4344C1003EEE59 /* libSDL2.dylib */,
				7BD31FEF2B4344C1003EEE59 /* libSDL2.dylib */,
				7BD31FF12B4344C1003EEE59 /* libSDL2.dylib */,
				7BD31FF32B4344C1003EEE59 /* SDL2 */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7BD31FF42B4344D0003EEE59 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		7B5EE30E2B43428D004061E2 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		7B1CDD442190C0A200175C7B /* FAudio-iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B1CDD4E2190C0A200175C7B /* Build configuration list for PBXNativeTarget "FAudio-iOS" */;
			buildPhases = (
				7B1CDD412190C0A200175C7B /* Sources */,
				7B1CDD422190C0A200175C7B /* Frameworks */,
				7B1CDD432190C0A200175C7B /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FAudio-iOS";
			productName = "FAudio-iOS";
			productReference = 7B1CDD452190C0A200175C7B /* libFAudio.a */;
			productType = "com.apple.product-type.library.static";
		};
		7B5EE3112B43428D004061E2 /* FAudio-macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B5EE3132B43428D004061E2 /* Build configuration list for PBXNativeTarget "FAudio-macOS" */;
			buildPhases = (
				7B5EE30E2B43428D004061E2 /* Headers */,
				7B5EE30F2B43428D004061E2 /* Sources */,
				7B5EE3102B43428D004061E2 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FAudio-macOS";
			productName = "FAudio-macOS";
			productReference = 7B5EE3122B43428D004061E2 /* libFAudio.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
		7B7E140C2190E0CB00616654 /* FAudio-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7B7E14132190E0CB00616654 /* Build configuration list for PBXNativeTarget "FAudio-tvOS" */;
			buildPhases = (
				7B7E14092190E0CB00616654 /* Sources */,
				7B7E140A2190E0CB00616654 /* Frameworks */,
				7B7E140B2190E0CB00616654 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "FAudio-tvOS";
			productName = "FAudio-tv";
			productReference = 7B7E140D2190E0CB00616654 /* libFAudio.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7B1CDD3D2190C0A200175C7B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1010;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					7B1CDD442190C0A200175C7B = {
						CreatedOnToolsVersion = 10.1;
					};
					7B5EE3112B43428D004061E2 = {
						CreatedOnToolsVersion = 15.1;
					};
					7B7E140C2190E0CB00616654 = {
						CreatedOnToolsVersion = 10.1;
					};
				};
			};
			buildConfigurationList = 7B1CDD402190C0A200175C7B /* Build configuration list for PBXProject "FAudio" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 7B1CDD3C2190C0A200175C7B;
			productRefGroup = 7B1CDD462190C0A200175C7B /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 7BD31FD12B4344C0003EEE59 /* Products */;
					ProjectRef = 7BD31FD02B4344C0003EEE59 /* SDL.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				7B1CDD442190C0A200175C7B /* FAudio-iOS */,
				7B7E140C2190E0CB00616654 /* FAudio-tvOS */,
				7B5EE3112B43428D004061E2 /* FAudio-macOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		7BD31FDF2B4344C1003EEE59 /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7BD31FDE2B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FE12B4344C1003EEE59 /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7BD31FE02B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FE32B4344C1003EEE59 /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7BD31FE22B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FE52B4344C1003EEE59 /* SDL2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SDL2.framework;
			remoteRef = 7BD31FE42B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FE72B4344C1003EEE59 /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7BD31FE62B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FE92B4344C1003EEE59 /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7BD31FE82B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FEB2B4344C1003EEE59 /* libSDL2.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libSDL2.a;
			remoteRef = 7BD31FEA2B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FED2B4344C1003EEE59 /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7BD31FEC2B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FEF2B4344C1003EEE59 /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7BD31FEE2B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FF12B4344C1003EEE59 /* libSDL2.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.dylib";
			path = libSDL2.dylib;
			remoteRef = 7BD31FF02B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		7BD31FF32B4344C1003EEE59 /* SDL2 */ = {
			isa = PBXReferenceProxy;
			fileType = "compiled.mach-o.executable";
			path = SDL2;
			remoteRef = 7BD31FF22B4344C1003EEE59 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXSourcesBuildPhase section */
		7B1CDD412190C0A200175C7B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BD20D832190C8E50020B14B /* FAudio.c in Sources */,
				7BD20D852190C8E50020B14B /* FACT.c in Sources */,
				7BD20D792190C8E50020B14B /* FAPOBase.c in Sources */,
				7BD20D8B2190C8E50020B14B /* FAPOFX_reverb.c in Sources */,
				7BD20D732190C8E50020B14B /* F3DAudio.c in Sources */,
				7BD20D772190C8E50020B14B /* FAPOFX_masteringlimiter.c in Sources */,
				7BD20D7F2190C8E50020B14B /* FAPOFX.c in Sources */,
				7BD20D7B2190C8E50020B14B /* FACT3D.c in Sources */,
				7BD20D892190C8E50020B14B /* FAudio_platform_sdl2.c in Sources */,
				7BD20D712190C8E50020B14B /* FACT_internal.c in Sources */,
				7B80D133227CE0E000AE825D /* FAudio_operationset.c in Sources */,
				7BD20D872190C8E50020B14B /* FAPOFX_eq.c in Sources */,
				7BD20D812190C8E50020B14B /* FAPOFX_echo.c in Sources */,
				7BD20D752190C8E50020B14B /* FAudio_internal.c in Sources */,
				7BD20D8D2190C8E50020B14B /* FAudioFX_reverb.c in Sources */,
				7BD20D6F2190C8E50020B14B /* FAudioFX_volumemeter.c in Sources */,
				7B6908272190EC41003C0941 /* XNA_Song.c in Sources */,
				7BD20D7D2190C8E50020B14B /* FAudio_internal_simd.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B5EE30F2B43428D004061E2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BD31FC42B434349003EEE59 /* FAudio_platform_sdl2.c in Sources */,
				7BD31FCD2B434349003EEE59 /* FAudio.c in Sources */,
				7BD31FCC2B434349003EEE59 /* FACT3D.c in Sources */,
				7BD31FCF2B434349003EEE59 /* FAudioFX_volumemeter.c in Sources */,
				7BD31FC72B434349003EEE59 /* FAPOFX_eq.c in Sources */,
				7BD31FC22B434349003EEE59 /* FACT.c in Sources */,
				7BD31FC82B434349003EEE59 /* FACT_internal.c in Sources */,
				7BD31FCB2B434349003EEE59 /* FAPOFX_echo.c in Sources */,
				7BD31FC92B434349003EEE59 /* XNA_Song.c in Sources */,
				7BD31FCA2B434349003EEE59 /* FAudio_internal.c in Sources */,
				7BD31FC32B434349003EEE59 /* FAudioFX_reverb.c in Sources */,
				7BD31FBE2B434349003EEE59 /* FAPOBase.c in Sources */,
				7BD31FC12B434349003EEE59 /* FAPOFX_masteringlimiter.c in Sources */,
				7BD31FC52B434349003EEE59 /* FAPOFX.c in Sources */,
				7BD31FC62B434349003EEE59 /* FAPOFX_reverb.c in Sources */,
				7BD31FCE2B434349003EEE59 /* F3DAudio.c in Sources */,
				7BD31FC02B434349003EEE59 /* FAudio_operationset.c in Sources */,
				7BD31FBF2B434349003EEE59 /* FAudio_internal_simd.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7B7E14092190E0CB00616654 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7BD806B122A0B4F900D9679D /* FAudio_operationset.c in Sources */,
				7B7E14162190E10C00616654 /* F3DAudio.c in Sources */,
				7B7E14172190E10C00616654 /* FACT_internal.c in Sources */,
				7B7E14182190E10C00616654 /* FACT.c in Sources */,
				7B7E14192190E10C00616654 /* FACT3D.c in Sources */,
				7B7E141A2190E10C00616654 /* FAPOBase.c in Sources */,
				7B7E141B2190E10C00616654 /* FAPOFX_echo.c in Sources */,
				7B7E141C2190E10C00616654 /* FAPOFX_eq.c in Sources */,
				7B7E141D2190E10C00616654 /* FAPOFX_masteringlimiter.c in Sources */,
				7B7E141E2190E10C00616654 /* FAPOFX_reverb.c in Sources */,
				7B7E141F2190E10C00616654 /* FAPOFX.c in Sources */,
				7B7E14202190E10C00616654 /* FAudio_internal_simd.c in Sources */,
				7B7E14212190E10C00616654 /* FAudio_internal.c in Sources */,
				7B7E14222190E10C00616654 /* FAudio_platform_sdl2.c in Sources */,
				7B7E14232190E10C00616654 /* FAudio.c in Sources */,
				7B7E14242190E10C00616654 /* FAudioFX_reverb.c in Sources */,
				7B6908282190EC41003C0941 /* XNA_Song.c in Sources */,
				7B7E14252190E10C00616654 /* FAudioFX_volumemeter.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7B1CDD4C2190C0A200175C7B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = ../../SDL2/include;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = FAudio;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		7B1CDD4D2190C0A200175C7B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					FAUDIO_DISABLE_DEBUGCONFIGURATION,
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = ../../SDL2/include;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = FAudio;
				TVOS_DEPLOYMENT_TARGET = 9.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7B1CDD4F2190C0A200175C7B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7B1CDD502190C0A200175C7B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphonesimulator iphoneos";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7B5EE3142B43428D004061E2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 78E9RL28VG;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		7B5EE3152B43428D004061E2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 78E9RL28VG;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		7B7E14142190E0CB00616654 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Debug;
		};
		7B7E14152190E0CB00616654 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "appletvsimulator appletvos";
				TARGETED_DEVICE_FAMILY = 3;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7B1CDD402190C0A200175C7B /* Build configuration list for PBXProject "FAudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B1CDD4C2190C0A200175C7B /* Debug */,
				7B1CDD4D2190C0A200175C7B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B1CDD4E2190C0A200175C7B /* Build configuration list for PBXNativeTarget "FAudio-iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B1CDD4F2190C0A200175C7B /* Debug */,
				7B1CDD502190C0A200175C7B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B5EE3132B43428D004061E2 /* Build configuration list for PBXNativeTarget "FAudio-macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B5EE3142B43428D004061E2 /* Debug */,
				7B5EE3152B43428D004061E2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7B7E14132190E0CB00616654 /* Build configuration list for PBXNativeTarget "FAudio-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7B7E14142190E0CB00616654 /* Debug */,
				7B7E14152190E0CB00616654 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7B1CDD3D2190C0A200175C7B /* Project object */;
}
