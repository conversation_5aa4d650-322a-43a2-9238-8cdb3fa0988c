stages:
  - build
  - package
  - deploy

build_net461:
  image: mono
  stage: build
  only:
    - schedules
  script:
    - ./gitlab-ci/build-net461.sh
  artifacts:
    paths:
      - bin/
  tags:
    - docker

build_netcore:
  image: microsoft/dotnet:2.1-sdk
  stage: build
  only:
    - schedules
  script:
    - ./gitlab-ci/build-netcore.sh
  artifacts:
    paths:
      - bin/
  tags:
    - docker

package_nuget:
  image: mono
  stage: package
  only:
    - schedules
  script:
    - ./gitlab-ci/package.sh
  dependencies:
    - build_net461
    - build_netcore
  artifacts:
    paths:
      - '*.nupkg'

deploy_nuget:
  image: mono
  stage: deploy
  only:
    - schedules
  script:
    - ./gitlab-ci/deploy.sh
  dependencies:
    - package_nuget
  environment:
    name: nuget
    url: http://nuget.org/

