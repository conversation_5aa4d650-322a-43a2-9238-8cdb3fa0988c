# Configuration for cmake-format (v0.4.1, circa Jul 2018)
# https://github.com/cheshirekow/cmake_format

# How wide to allow formatted cmake files
line_width = 132

# How many spaces to tab for indent
tab_size = 4

# If arglists are longer than this, break them always
max_subargs_per_line = 3

# If true, separate flow control names from their parentheses with a space
separate_ctrl_name_with_space = False

# If true, separate function names from parentheses with a space
separate_fn_name_with_space = False

# If a statement is wrapped to more than one line, than dangle the closing
# parenthesis on it's own line
dangle_parens = False

# What character to use for bulleted lists
bullet_char = u'*'

# What character to use as punctuation after numerals in an enumerated list
enum_char = u'.'

# What style line endings to use in the output.
line_ending = u'unix'

# Format command names consistently as 'lower' or 'upper' case
command_case = u'lower'

