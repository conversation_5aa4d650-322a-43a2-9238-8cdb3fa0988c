name: Build-Test

on:
  push:
    branches: "*"
    paths-ignore:
      - '**/build-release.yml'
      - 'appveyor.yml'
      - '**/README.md'
  pull_request:
    branches: "*"
    paths-ignore:
      - '**/build-release.yml'
      - 'appveyor.yml'
      - '**/README.md'


env:
  DOTNET_NOLOGO: false
  DOTNET_CLI_TELEMETRY_OPTOUT: 1
  DOTNET_SKIP_FIRST_TIME_EXPERIENCE: 1
  NUGET_XMLDOC_MODE: skip

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
       os: [ macos-13, ubuntu-latest, windows-latest ]

    steps:
    - uses: actions/checkout@v4

    - name: Get submodules
      run: |
        git config --global url."https://".insteadOf git://
        git submodule update --init --recursive

    - name: Setup dotnet
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 9.0.x
        include-prerelease: false

    - name: Restore
      run: dotnet restore

    - name: Build
      run: dotnet build -c Release

    - name: Test
      run: dotnet test --verbosity normal tests/ClassicUO.UnitTests

